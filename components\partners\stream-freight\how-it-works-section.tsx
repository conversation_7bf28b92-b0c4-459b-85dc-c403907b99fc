import type React from "react"
import { ClipboardList, Upload, CheckCircle, Truck, DollarSign, BarChart } from "lucide-react"

interface Step {
  icon: React.ReactNode
  title: string
  description: string
  number: number
}

export function HowItWorksSection() {
  const steps: Step[] = [
    {
      icon: <ClipboardList className="h-10 w-10 text-white" />,
      title: "Apply Online",
      description: "Register with your DOT number, fleet registration details, or relevant regional transport license.",
      number: 1,
    },
    {
      icon: <Upload className="h-10 w-10 text-white" />,
      title: "Upload Compliance Documents",
      description:
        "Submit your proof of insurance, driver certifications, operating permits, and any other required regulatory documents.",
      number: 2,
    },
    {
      icon: <CheckCircle className="h-10 w-10 text-white" />,
      title: "Get Verified",
      description:
        "Our team will review your application and documents. Upon approval, you'll be assigned an operational zone and gain portal access.",
      number: 3,
    },
    {
      icon: <Truck className="h-10 w-10 text-white" />,
      title: "Start Receiving & Bidding on Jobs",
      description:
        "Access the live bid pool and receive job assignments based on your product category expertise, location, and load type capacity.",
      number: 4,
    },
    {
      icon: <DollarSign className="h-10 w-10 text-white" />,
      title: "Accept, Deliver, and Get Paid",
      description:
        "Manage your accepted jobs, provide real-time status updates, and receive payments through our centralized platform.",
      number: 5,
    },
    {
      icon: <BarChart className="h-10 w-10 text-white" />,
      title: "Track Your Success",
      description: "Monitor your progress and performance history directly within your StreamFreight portal.",
      number: 6,
    },
  ]

  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-8 text-center">
          How It Works: A Simple Path to Partnership
        </h2>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
          {steps.map((step, index) => (
            <div key={index} className="flex flex-col items-center">
              <div className="relative mb-6">
                <div className="w-20 h-20 rounded-full bg-[#004235] flex items-center justify-center">{step.icon}</div>
                <div className="absolute -top-2 -right-2 w-8 h-8 rounded-full bg-[#028475] flex items-center justify-center text-white font-bold">
                  {step.number}
                </div>
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-3 text-center">{step.title}</h3>
              <p className="text-gray-700 text-center">{step.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
