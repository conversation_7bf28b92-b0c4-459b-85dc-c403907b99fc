"use client"
"use client";

import { WorldMap } from "@/components/ui/world-map";
import { useEffect, useState } from "react";

// Define a larger pool of potential logistics connections
const allPossibleConnections = [
  // North America
  { start: { lat: 40.7128, lng: -74.0060, label: "New York" }, end: { lat: 51.5074, lng: -0.1278, label: "London" }, color: "#3b82f6" }, // NA-EU
  { start: { lat: 34.0522, lng: -118.2437, label: "Los Angeles" }, end: { lat: 35.6895, lng: 139.6917, label: "Tokyo" }, color: "#ef4444" }, // NA-Asia
  { start: { lat: 49.2827, lng: -123.1207, label: "Vancouver" }, end: { lat: -33.8688, lng: 151.2093, label: "Sydney" }, color: "#22c55e" }, // NA-Oceania
  { start: { lat: 19.4326, lng: -99.1332, label: "Mexico City" }, end: { lat: -23.5505, lng: -46.6333, label: "São Paulo" }, color: "#f97316" }, // NA-SA

  // Europe
  { start: { lat: 52.5200, lng: 13.4050, label: "Berlin" }, end: { lat: 25.276987, lng: 55.296249, label: "Dubai" }, color: "#f59e0b" }, // EU-MEA
  { start: { lat: 48.8566, lng: 2.3522, label: "Paris" }, end: { lat: 39.9042, lng: 116.4074, label: "Beijing" }, color: "#d946ef" }, // EU-Asia
  { start: { lat: 55.7558, lng: 37.6173, label: "Moscow" }, end: { lat: 28.6139, lng: 77.2090, label: "New Delhi" }, color: "#84cc16" }, // EU-Asia

  // Asia
  { start: { lat: 22.3193, lng: 114.1694, label: "Hong Kong" }, end: { lat: 1.3521, lng: 103.8198, label: "Singapore" }, color: "#6366f1" }, // Intra-Asia
  { start: { lat: 37.5665, lng: 126.9780, label: "Seoul" }, end: { lat: -37.8136, lng: 144.9631, label: "Melbourne" }, color: "#14b8a6" }, // Asia-Oceania
  { start: { lat: 25.276987, lng: 55.296249, label: "Dubai" }, end: { lat: -1.2921, lng: 36.8219, label: "Nairobi" }, color: "#10b981" }, // MEA-Africa
  
  // South America
  { start: { lat: -34.6037, lng: -58.3816, label: "Buenos Aires" }, end: { lat: -33.9249, lng: 18.4241, label: "Cape Town" }, color: "#8b5cf6" }, // SA-Africa
  { start: { lat: 4.7110, lng: -74.0721, label: "Bogotá" }, end: { lat: 28.6139, lng: 77.2090, label: "New Delhi" }, color: "#e11d48" }, // SA-Asia

  // Africa
  { start: { lat: -26.2041, lng: 28.0473, label: "Johannesburg" }, end: { lat: -15.7797, lng: -47.9297, label: "Brasilia" }, color: "#0ea5e9" }, // Africa-SA
  { start: { lat: 30.0444, lng: 31.2357, label: "Cairo" }, end: { lat: 34.0522, lng: -118.2437, label: "Los Angeles" }, color: "#7c3aed" }, // Africa-NA
  
  // Oceania
  { start: { lat: -40.9006, lng: 174.8860, label: "Wellington" }, end: { lat: 34.0522, lng: -118.2437, label: "Los Angeles" }, color: "#c026d3" }, // Oceania-NA
];

const getRandomConnections = (count: number) => {
  const shuffled = [...allPossibleConnections].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

export function LogisticsMap() {
  const [currentConnections, setCurrentConnections] = useState(() => getRandomConnections(5));

  useEffect(() => {
    const intervalId = setInterval(() => {
      // Simulate new transactions by adding one new random connection and removing an old one
      // or completely refresh the set of connections
      setCurrentConnections(prevConnections => {
        const newConnection = getRandomConnections(1)[0];
        // Ensure the new connection is not already present (optional, for variety)
        if (prevConnections.find(c => c.start.label === newConnection.start.label && c.end.label === newConnection.end.label)) {
          return getRandomConnections(5); // Refresh if duplicate to keep it simple
        }
        const updatedConnections = [newConnection, ...prevConnections.slice(0, 4)];
        return updatedConnections;
      });
    }, 3000); // Update every 3 seconds

    return () => clearInterval(intervalId); // Cleanup on unmount
  }, []);

  return (
    <div className="relative w-full rounded-xl overflow-hidden">
      <WorldMap
        dots={currentConnections}
      />
    </div>
  );
}
