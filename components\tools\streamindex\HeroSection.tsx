"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

import Image from "next/image";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Unlock Real-Time Market Intelligence: The StreamIndex™ Tool by StreamLnk
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Make data-driven decisions with unparalleled precision. The StreamIndex™ tool provides subscribers with live, interactive access to our proprietary benchmarks for industrial material pricing, logistics efficiency, and supply chain risk.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                className="bg-[#004235] hover:bg-[#028475] text-white px-6"
                size="lg"
                asChild
              >
                <Link href="/tools/streamindex#hub"> {/* Placeholder link, assuming login/access will be handled elsewhere or on this page */}
                  ACCESS STREAMINDEX™
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button 
                variant="outline"
                className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white px-6"
                size="lg"
                asChild
              >
                <Link href="/request-demo?tool=streamindex"> {/* Query param to specify demo interest */}
                  REQUEST DEMO
                </Link>
              </Button>
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/tools/streamindex/hero-image.jpg" // Placeholder image
              alt="StreamIndex™ Market Intelligence Tool"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
}