import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export default function CtaSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4 text-center">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Take Control of Your Shipments Today
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            Experience a New Level of Shipment Visibility and Control with StreamLnk.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white px-6"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                Request a Demo of Our Shipment Management Tools
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
              size="lg"
              asChild
            >
              <Link href="/solutions">
                Learn More About Our Integrated Logistics Solutions
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}