import Link from "next/link";
import { ArrowR<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export default function TransparencySection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Transparency in Performance, Available Across StreamLnk
          </h2>

          <div className="space-y-8">
            {/* Basic iScore Visibility */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <h3 className="text-2xl font-bold text-[#004235] mb-4">Basic iScore™ Visibility (Free in Portals)</h3>
              <p className="text-gray-700">
                All users see their own detailed iScore™ and its components. When viewing other entities (e.g., a buyer looking at suppliers), a simplified badge or summary score is typically displayed to provide an initial trust signal.
              </p>
            </div>

            {/* Premium Access */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <h3 className="text-2xl font-bold text-[#004235] mb-4">Premium Access (via StreamResources+ Tiers)</h3>
              <p className="text-gray-700 mb-4">
                Subscribers to relevant StreamResources+ tiers can access:
              </p>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  <span>Searchable iScore™ Directory: Look up the iScore™ profiles of any registered entity.</span>
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  <span>Detailed iScore™ Reports: Comprehensive PDF reports with historical trends, category breakdowns, and key contributing factors (positive/negative).</span>
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  <span>Comparative Analytics: Benchmark potential partners against each other or against industry averages.</span>
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  <span>Watchlist & Alerts: Get notified of significant changes in the iScore™ of key partners.</span>
                </li>
              </ul>
            </div>

            <div className="text-center">
              <Button
                className="bg-[#004235] hover:bg-[#028475] text-white px-6"
                size="lg"
                asChild
              >
                <Link href="/solutions/data-analytics">
                  EXPLORE DETAILED ISCORE™ REPORTS VIA STREAMRESOURCES+
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}