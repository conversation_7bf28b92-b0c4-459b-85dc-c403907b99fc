"use client"

import Link from "next/link"
import Image from "next/image"
import { MainNav } from "@/components/main-nav"
import { BottomFooter } from "@/components/bottom-footer"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, CheckCircle, ChevronRight } from "lucide-react"
import HeroSection from "@/components/solutions/sourcing-procurement/HeroSection"
import ChallengesSection from "@/components/solutions/sourcing-procurement/ChallengesSection"
import PlatformOverviewSection from "@/components/solutions/sourcing-procurement/PlatformOverviewSection"
import FeaturesAndBenefitsSection from "@/components/solutions/sourcing-procurement/FeaturesAndBenefitsSection"
import WorkflowSection from "@/components/solutions/sourcing-procurement/WorkflowSection"
import CallToActionSection from "@/components/solutions/sourcing-procurement/CallToActionSection"

export default function SourcingProcurementPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      {/* Challenges Section */}
      <ChallengesSection />

      {/* Platform Overview Section */}
      <PlatformOverviewSection />

      {/* Features and Benefits Section */}
      <FeaturesAndBenefitsSection />

      {/* Workflow Section */}
      <WorkflowSection />

      {/* CTA Section */}
      <CallToActionSection />

      <BottomFooter />
    </div>
  )
}