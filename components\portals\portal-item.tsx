import type React from "react"
import Image from "next/image"
import { AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion"

interface PortalItemProps {
  id: string
  title: string
  subtitle: string
  icon: React.ReactNode
  color: string
  imageSrc: string
  imageAlt: string
  children: React.ReactNode
}

export default function PortalItem({
  id,
  title,
  subtitle,
  icon,
  color,
  imageSrc,
  imageAlt,
  children,
}: PortalItemProps) {
  return (
    <AccordionItem value={id} className="border border-gray-200 rounded-lg overflow-hidden">
      <AccordionTrigger className="px-6 py-4 hover:no-underline hover:bg-gray-50">
        <div className="flex items-center w-full">
          <div className="p-3 rounded-lg mr-4" style={{ backgroundColor: `${color}15`, color: color }}>
            {icon}
          </div>
          <div className="text-left">
            <h3 className="text-xl font-bold text-[#023025]">{title}</h3>
            <p className="text-sm text-[#404040]">{subtitle}</p>
          </div>
        </div>
      </AccordionTrigger>
      <AccordionContent className="px-0 pt-0">
        <div className="relative h-64 w-full">
          <Image src={imageSrc || "/placeholder.svg"} alt={imageAlt} fill className="object-cover" />
        </div>
        <div className="p-6">{children}</div>
      </AccordionContent>
    </AccordionItem>
  )
}
