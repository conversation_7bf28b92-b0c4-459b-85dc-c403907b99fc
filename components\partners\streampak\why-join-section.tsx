import { Bell, Clock, CreditCard, Layers, Shield, TruckIcon } from "lucide-react"

export function WhyJoinSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="w-10 h-1 bg-[#028475]"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">Why Join StreamPak?</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-16">
          <div className="flex flex-col items-center text-center">
            <div className="bg-[#004235] p-4 rounded-full w-16 h-16 flex items-center justify-center mb-6">
              <TruckIcon className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-bold text-[#004235] mb-4">Direct Connections</h3>
            <p className="text-gray-600">
              Connect directly with top-tier suppliers requiring specialized repackaging, labeling, or regional
              stockholding.
            </p>
          </div>

          <div className="flex flex-col items-center text-center">
            <div className="bg-[#004235] p-4 rounded-full w-16 h-16 flex items-center justify-center mb-6">
              <Layers className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-bold text-[#004235] mb-4">Integrated Ecosystem</h3>
            <p className="text-gray-600">Become an integral part of StreamLnk's end-to-end fulfillment chain.</p>
          </div>

          <div className="flex flex-col items-center text-center">
            <div className="bg-[#004235] p-4 rounded-full w-16 h-16 flex items-center justify-center mb-6">
              <Bell className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-bold text-[#004235] mb-4">Early Notifications & Seamless Coordination</h3>
            <p className="text-gray-600">
              Receive early shipment notifications and benefit from direct integration with our StreamFreight (freight)
              and StreamGlobe (customs) portals.
            </p>
          </div>

          <div className="flex flex-col items-center text-center">
            <div className="bg-[#004235] p-4 rounded-full w-16 h-16 flex items-center justify-center mb-6">
              <Shield className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-bold text-[#004235] mb-4">Build Trust & Reputation</h3>
            <p className="text-gray-600">
              Enhance your market standing through transparent Key Performance Indicator (KPI) tracking and a documented
              service history.
            </p>
          </div>

          <div className="flex flex-col items-center text-center">
            <div className="bg-[#004235] p-4 rounded-full w-16 h-16 flex items-center justify-center mb-6">
              <CreditCard className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-bold text-[#004235] mb-4">Reliable Payments</h3>
            <p className="text-gray-600">
              Get paid on-time with our streamlined invoice dashboard and flexible, milestone-based payout options.
            </p>
          </div>

          <div className="flex flex-col items-center text-center">
            <div className="bg-[#004235] p-4 rounded-full w-16 h-16 flex items-center justify-center mb-6">
              <Clock className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-bold text-[#004235] mb-4">Optimized Operations</h3>
            <p className="text-gray-600">
              Benefit from smooth workflow management and predictable scheduling to maximize resource utilization.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
