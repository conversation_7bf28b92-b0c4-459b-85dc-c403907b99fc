import Image from "next/image";
import { Button } from "@/components/ui/button";

interface HeroSectionProps {
  title: string;
  description: string;
  imageSrc: string;
  imageAlt: string;
  buttonText?: string;
  buttonHref?: string;
}

export default function HeroSection({
  title,
  description,
  imageSrc,
  imageAlt,
  buttonText = "Get Started",
  buttonHref = "#",
}: HeroSectionProps) {
  return (
    <section className="py-20 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="grid gap-12 lg:grid-cols-2 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold tracking-tight text-[#004235] mb-6">
              {title}
            </h1>
            <p className="text-xl text-gray-600 mb-8">{description}</p>
            <Button className="bg-[#028475] hover:bg-[#004235]">{buttonText}</Button>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image src={imageSrc} alt={imageAlt} fill className="object-cover" />
          </div>
        </div>
      </div>
    </section>
  );
}