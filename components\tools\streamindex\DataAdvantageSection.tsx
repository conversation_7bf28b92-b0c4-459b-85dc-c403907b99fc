"use client";

import { TrendingUp, ShieldCheck, Search, Zap, BarChartHorizontal } from "lucide-react";

export default function DataAdvantageSection() {
  const advantages = [
    {
      icon: <TrendingUp className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Make Faster, More Confident Pricing Decisions.",
      description: "Leverage real-time market data to inform your pricing strategies and negotiations, ensuring competitiveness and profitability."
    },
    {
      icon: <BarChartHorizontal className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Optimize Your Sourcing and Logistics Strategies with Real Data.",
      description: "Identify the most cost-effective and efficient sourcing locations, transport modes, and trade lanes based on current performance metrics."
    },
    {
      icon: <ShieldCheck className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Proactively Mitigate Market and Supply Chain Risks.",
      description: "Anticipate potential disruptions, compliance hurdles, and price volatility by monitoring key risk indicators within the StreamIndex™."
    },
    {
      icon: <Zap className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Benchmark Your Performance Against True Market Conditions.",
      description: "Understand how your procurement costs, logistics efficiency, and supplier performance compare to industry averages and best-in-class."
    },
    {
      icon: <Search className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Identify Emerging Opportunities and Trends Before Your Competitors.",
      description: "Gain early insights into shifting demand patterns, new material availabilities, and evolving market dynamics to seize new opportunities."
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            The Data Advantage You Need to Thrive
          </h2>
          <p className="text-xl text-gray-700 mb-12 text-center">
            Why StreamIndex™ is an Essential Tool for Modern Industrial Trade
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {advantages.slice(0, 3).map((advantage, index) => (
              <div key={index} className="bg-[#F2F2F2] p-6 rounded-lg text-center flex flex-col items-center">
                {advantage.icon}
                <h3 className="font-semibold text-[#004235] text-lg mb-2">{advantage.title}</h3>
                <p className="text-gray-600 text-sm">{advantage.description}</p>
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
            {advantages.slice(3).map((advantage, index) => (
              <div key={index} className="bg-[#F2F2F2] p-6 rounded-lg text-center flex flex-col items-center">
                {advantage.icon}
                <h3 className="font-semibold text-[#004235] text-lg mb-2">{advantage.title}</h3>
                <p className="text-gray-600 text-sm">{advantage.description}</p>
              </div>
            ))}
          </div>

        </div>
      </div>
    </section>
  );
}