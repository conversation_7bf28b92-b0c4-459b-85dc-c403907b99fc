import Image from "next/image"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"

export default function DashboardPreview() {
  return (
    <div className="border border-[#f3f4f6] rounded-xl overflow-hidden bg-white shadow-lg">
      <div className="p-4 border-b border-[#f3f4f6] bg-[#f3f4f6]/50">
        <Tabs defaultValue="ar" className="w-full">
          <TabsList className="grid w-full max-w-md mx-auto grid-cols-4">
            <TabsTrigger value="ar">AR Dashboard</TabsTrigger>
            <TabsTrigger value="ap">AP Dashboard</TabsTrigger>
            <TabsTrigger value="create">Create Invoice</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      <div className="relative h-[400px] md:h-[500px] w-full">
        <Image
          src="/placeholder.svg?height=500&width=1000"
          alt="Invoicing dashboard interface"
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
        <div className="absolute bottom-4 left-4 right-4 p-4 bg-white/90 backdrop-blur-sm rounded-lg border border-[#f3f4f6]">
          <h3 className="text-lg font-semibold text-[#004235] mb-2">Accounts Receivable Dashboard</h3>
          <p className="text-gray-600 text-sm">
            Monitor outstanding invoices, aging buckets, and customer payment history with powerful filtering and
            reporting tools.
          </p>
        </div>
      </div>
    </div>
  )
}
