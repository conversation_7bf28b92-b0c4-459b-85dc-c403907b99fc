"use client";

import React from 'react';
import HeroSection from '@/components/portal-features/dashboards/HeroSection';
import WhyDashboardsSection from '@/components/portal-features/dashboards/WhyDashboardsSection';
import RoleSpecificDashboardsSection from '@/components/portal-features/dashboards/RoleSpecificDashboardsSection';
import CommonDashboardElementsSection from '@/components/portal-features/dashboards/CommonDashboardElementsSection';
import BenefitsSection from '@/components/portal-features/dashboards/BenefitsSection';
import { BottomFooter } from '@/components/bottom-footer';
import { MainNav } from '@/components/main-nav';

export default function DashboardsPage() {
  return (
    <div className="flex flex-col min-h-screen bg-white">
      <MainNav />
      <HeroSection />
      <WhyDashboardsSection />
      <RoleSpecificDashboardsSection />
      <CommonDashboardElementsSection />
      <BenefitsSection />
      <BottomFooter />
    </div>
  );
}