"use client";

import { AlertCircle, Clock, DollarSign, FileText, Route, Users } from "lucide-react";

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Is Freight Coordination a Drain on Your Resources?
          </h2>
          <p className="text-lg text-gray-700 mb-10">
            Coordinating freight for industrial materials, especially across different modes of transport and international borders, is a significant operational burden. Common pain points include:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-[#f3f4f6] p-6 rounded-lg">
              <div className="flex items-start">
                <div className="bg-[#004235]/10 p-2 rounded-full flex items-center justify-center mr-4">
                  <Users className="text-[#028475] h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-3">Carrier Selection</h3>
                  <p className="text-gray-700">
                    Difficulty finding reliable and cost-effective carriers for specific routes or material types.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-[#f3f4f6] p-6 rounded-lg">
              <div className="flex items-start">
                <div className="bg-[#004235]/10 p-2 rounded-full flex items-center justify-center mr-4">
                  <Clock className="text-[#028475] h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-3">Time-Consuming Processes</h3>
                  <p className="text-gray-700">
                    Time-consuming negotiations and booking processes with multiple freight providers.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-[#f3f4f6] p-6 rounded-lg">
              <div className="flex items-start">
                <div className="bg-[#004235]/10 p-2 rounded-full flex items-center justify-center mr-4">
                  <DollarSign className="text-[#028475] h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-3">Pricing Transparency</h3>
                  <p className="text-gray-700">
                    Lack of transparency in freight rates and ancillary charges.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-[#f3f4f6] p-6 rounded-lg">
              <div className="flex items-start">
                <div className="bg-[#004235]/10 p-2 rounded-full flex items-center justify-center mr-4">
                  <Route className="text-[#028475] h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-3">Inefficient Routing</h3>
                  <p className="text-gray-700">
                    Inefficient routing leading to longer transit times and higher fuel costs.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-[#f3f4f6] p-6 rounded-lg">
              <div className="flex items-start">
                <div className="bg-[#004235]/10 p-2 rounded-full flex items-center justify-center mr-4">
                  <AlertCircle className="text-[#028475] h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-3">Modal Coordination</h3>
                  <p className="text-gray-700">
                    Poor coordination between different transport modes (e.g., port-to-rail, rail-to-truck).
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-[#f3f4f6] p-6 rounded-lg">
              <div className="flex items-start">
                <div className="bg-[#004235]/10 p-2 rounded-full flex items-center justify-center mr-4">
                  <FileText className="text-[#028475] h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-3">Compliance Management</h3>
                  <p className="text-gray-700">
                    Managing compliance and documentation for diverse freight requirements.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}