"use client"

import Image from "next/image"
import Link from "next/link"
import { ChevronRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export function HeroSlider() {
  return (
    <section className="relative h-[600px] flex items-center justify-center text-white">
      {/* Background Image */}
      <Image
        src="/images/homepage/home page banner image.png"
        alt="Cityscape background for global energy trade"
        fill
        className="object-cover z-0"
        priority
      />
      {/* Overlay */}
      <div className="absolute inset-0 bg-black/50 z-10"></div>
      {/* Content */}
      <div className="container mx-auto px-4 z-20 text-center relative">
        <h1 className="text-4xl md:text-6xl font-bold mb-4 leading-tight">
          Revolutionizing Global Energy Trade <br /> with Breakthrough Digital Solutions
        </h1>
        <p className="text-lg md:text-xl mb-8 max-w-3xl mx-auto">
          A next-gen digital platform transforming the global energy market. Through breakthrough technology
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button variant="cta" size="lg" className="font-medium uppercase">
            Become a supplier
          </Button>
          <Button variant="ctaOutline" size="lg" className="font-medium uppercase flex items-center">
            Explore our services <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      </div>

      <div className="absolute bottom-0 w-full h-1 bg-[#18b793] z-30"></div>
    </section>
  )
}

