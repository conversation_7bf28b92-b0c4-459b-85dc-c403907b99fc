import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

export function CtaSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-[#004235]">
          Expand Your Business with World-Class Digital Infrastructure
        </h2>
        <p className="text-xl max-w-3xl mx-auto mb-8 text-gray-700">
          Take your industrial sales career to the next level. Partner with MyStreamLnk+ and become a leader in the
          digital transformation of global trade.
        </p>
        <Button size="lg" className="bg-[#004235] text-white hover:bg-[#028475] transition-colors" asChild>
          <Link href="/apply-agent">Apply to Become a StreamLnk+ Agent or Distributor Today</Link>
        </Button>
        <p className="text-sm mt-4 text-gray-600">
          <Link href="/login" className="hover:underline">Portal Access: MyStreamLnk+</Link>
        </p>
      </div>
    </section>
  )
}
