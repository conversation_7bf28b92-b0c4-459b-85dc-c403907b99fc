import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FileT<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>r<PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"
import { Timeline } from "@/components/ui/timeline";

export function JoinProcessSection() {
  const steps = [
    {
      icon: <ClipboardList className="h-8 w-8 text-white" />,
      title: "Apply",
      description: "Submit your application detailing your CV, sales background, and relevant industry experience.",
    },
    {
      icon: <FileText className="h-8 w-8 text-white" />,
      title: "Agreement",
      description: "Review and sign the MyStreamLnk+ agent agreement and Non-Disclosure Agreement (NDA).",
    },
    {
      icon: <Unlock className="h-8 w-8 text-white" />,
      title: "Portal Access & Onboarding",
      description: "Get access to your personalized MyStreamLnk+ portal and receive comprehensive onboarding.",
    },
    {
      icon: <UserPlus className="h-8 w-8 text-white" />,
      title: "Start Onboarding Buyers",
      description: "Begin registering your verified industrial buyers onto the StreamLnk platform.",
    },
    {
      icon: <BarChart className="h-8 w-8 text-white" />,
      title: "<PERSON><PERSON>, <PERSON><PERSON> & Earn",
      description: "Monitor client activity, respond to RFQs, facilitate transactions, and earn commissions.",
    },
  ]

  return (
    <section id="apply" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">How to Join MyStreamLnk+</h2>
        </div>

        <div className="max-w-4xl mx-auto">
          <Timeline steps={steps} />
        </div>
      </div>
    </section>
  )
}
