export default function PowerOfDataSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            The Power of Data-Driven Market Understanding
          </h2>
          <p className="text-lg text-gray-700">
            Why In-Depth Market Analysis is Crucial for Success
          </p>
        </div>
        <div className="max-w-4xl mx-auto">
          <p className="text-lg text-gray-700 mb-8 text-center">
            In today's volatile and interconnected global economy, having access to accurate and timely market analysis is no longer a luxury but a necessity for:
          </p>
          <ul className="space-y-4 text-gray-700 text-lg">
            {[{
              title: "Identifying emerging opportunities and potential threats."
            }, {
              title: "Optimizing pricing and sourcing strategies based on true market dynamics."
            }, {
              title: "Mitigating risks associated with price volatility, supply disruptions, and geopolitical shifts."
            }, {
              title: "Making informed investment and capacity planning decisions."
            }, {
              title: "Understanding competitive landscapes and regional nuances."
            }, {
              title: "Developing resilient and agile supply chain strategies."
            }].map((item, index) => (
              <li key={index} className="flex items-start">
                <svg className="flex-shrink-0 h-6 w-6 text-[#028475] mr-3 mt-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                </svg>
                <span>{item.title}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  );
}