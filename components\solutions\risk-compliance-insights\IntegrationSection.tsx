import Link from "next/link";
import { ArrowR<PERSON>, CheckCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function IntegrationSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Integrated Insights via StreamResources+ and Your Portal
          </h2>

          <div className="grid md:grid-cols-2 gap-8 mb-10">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Basic Risk Indicators & iScore™ Badges</h3>
                  <p className="text-gray-600">Visible within MyStreamLnk, E-Stream, and other operational portals to provide immediate trust signals.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">StreamResources+ Premium Tiers</h3>
                  <p className="text-gray-600">Offer access to detailed iScore™ reports, full StreamIndex™ Risk & Compliance modules, advanced risk dashboards, predictive analytics, customizable alerts, and API access for integrating risk data into enterprise systems.</p>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center">
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white px-6"
              size="lg"
              asChild
            >
              <Link href="/solutions/data-analytics">
                EXPLORE STREAMRESOURCES+ FOR RISK MANAGEMENT
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}