"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Eye, FileText, Database, ArrowRight } from 'lucide-react';

export default function AccessingIscoreSection() {
  const accessPoints = [
    {
      icon: <Eye className="h-8 w-8 text-[#028475] mb-3" />,
      title: "Basic iScore™ Visibility",
      description: "Simplified iScore™ badges or summary ratings are displayed next to entity profiles within the relevant StreamLnk operational portals (MyStreamLnk, E-Stream, etc.) to provide an immediate trust signal. All users can see their own detailed iScore™ breakdown and contributing factors."
    },
    {
      icon: <FileText className="h-8 w-8 text-[#028475] mb-3" />,
      title: "Premium iScore™ Reports (via StreamResources+)",
      description: "Subscribers to relevant StreamResources+ tiers can access a searchable iScore™ Directory and download Comprehensive iScore™ Reports for any platform participant. These reports offer deep dives into historical performance, detailed metric breakdowns, trend analysis, and peer benchmarks."
    },
    {
      icon: <Database className="h-8 w-8 text-[#028475] mb-3" />,
      title: "API Access (Enterprise Tier)",
      description: "Integrate iScore™ data into your own risk management or procurement systems."
    }
  ];

  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-4 text-center">
            Accessing iScore™ Insights
          </h2>
          <p className="text-xl text-gray-700 mb-12 text-center">
            Transparency in Performance, Available Across StreamLnk
          </p>

          <div className="space-y-8">
            {accessPoints.map((point, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
                <div className="flex items-start">
                  <div className="mr-4 flex-shrink-0">{point.icon}</div>
                  <div>
                    <h3 className="text-xl font-semibold text-[#004235] mb-2">{point.title}</h3>
                    <p className="text-gray-600 text-sm">{point.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-12 text-center">
            <Button
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white px-6 py-3 text-md"
              size="lg"
              asChild
            >
              <Link href="/solutions/streamresources#iscore-reports"> {/* Placeholder link - update if specific anchor exists */}
                EXPLORE ISCORE™ REPORTS
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}