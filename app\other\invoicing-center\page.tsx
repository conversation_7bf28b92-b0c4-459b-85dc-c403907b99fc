import Image from "next/image"
import {
  <PERSON><PERSON><PERSON>,
  FileText,
  BarChart3,
  Clock,
  CheckCircle2,
  Globe,
  Shield,
  FileCheck,
  Building2,
  Users,
  Truck,
  DollarSign,
  Link,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import FeatureCard from "./components/feature-card"
import ComplianceFeature from "./components/compliance-feature"
import IntegrationFeature from "./components/integration-feature"
import UseCaseCard from "./components/use-case-card"
import DashboardPreview from "./components/dashboard-preview"

export default function InvoicingCenterPage() {
  return (
    <main className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-[#F2F2F2] py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
            <div className="flex flex-col justify-center space-y-4">
              <div className="inline-block rounded-lg bg-[#004235]/10 px-3 py-1 text-sm text-[#004235]">
                Financial Management
              </div>
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235]">
                Invoicing Center (AR/AP Management)
              </h1>
              <p className="text-gray-600 md:text-xl">
                A unified workspace to manage financial documents, monitor payment status, and streamline global AR/AP
                processes—your single source of truth.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button className="bg-[#004235] hover:bg-[#004235]/90">
                  Access Invoicing Center <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                <Button variant="outline" className="border-[#028475] text-[#028475]">
                  Learn More
                </Button>
              </div>
            </div>
            <div className="flex justify-center">
              <div className="relative w-full max-w-[500px] h-[300px] md:h-[400px]">
                <Image
                  src="/placeholder.svg?height=400&width=500"
                  alt="Invoicing center illustration"
                  fill
                  className="object-contain"
                  priority
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Dashboard Preview Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Comprehensive Invoicing Management
            </h2>
            <p className="text-gray-600 max-w-3xl">
              The StreamLnk Invoicing Center provides a complete view of your accounts receivable and accounts payable.
            </p>
          </div>

          <DashboardPreview />
        </div>
      </section>

      {/* Key Features Section */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Key Features</h2>
            <p className="text-gray-600 max-w-3xl">
              Our Invoicing Center offers powerful tools to manage your financial documents and processes.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <FeatureCard
              icon={<FileText className="h-8 w-8 text-[#028475]" />}
              title="Create & Send Invoices"
              features={[
                "Generate invoices manually or auto-generate from orders and shipments",
                "Customize with tax IDs, PO numbers, and document attachments",
                "Send invoices directly through the platform with tracking",
              ]}
            />

            <FeatureCard
              icon={<BarChart3 className="h-8 w-8 text-[#028475]" />}
              title="Accounts Receivable (AR) Dashboard"
              features={[
                "Monitor outstanding invoices, aging buckets, and customer payment history",
                "Filter by region, partner type, currency, and due date",
                "Generate AR reports and forecasts",
              ]}
            />

            <FeatureCard
              icon={<Clock className="h-8 w-8 text-[#028475]" />}
              title="Accounts Payable (AP) Dashboard"
              features={[
                "View all open payables, payment due dates, and related documentation",
                "Get alerts on upcoming deadlines and late penalties",
                "Schedule and track payments",
              ]}
            />

            <FeatureCard
              icon={<CheckCircle2 className="h-8 w-8 text-[#028475]" />}
              title="Invoice Matching & Validation"
              features={[
                "Auto-match payments and proof of payment uploads to open invoices",
                "Flag discrepancies for review or escalation",
                "Streamline reconciliation processes",
              ]}
            />

            <FeatureCard
              icon={<Globe className="h-8 w-8 text-[#028475]" />}
              title="Multi-Currency Support"
              features={[
                "Manage invoices in over 25 currencies with real-time conversion reference",
                "Handle multi-currency reconciliation",
                "Track exchange rate fluctuations",
              ]}
            />

            <FeatureCard
              icon={<FileCheck className="h-8 w-8 text-[#028475]" />}
              title="Document Management"
              features={[
                "Centralized storage for all invoice-related documents",
                "Searchable document repository",
                "Automated document organization and tagging",
              ]}
            />
          </div>
        </div>
      </section>

      {/* Compliance & Audit Support Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Compliance & Audit Support</h2>
            <p className="text-gray-600 max-w-3xl">
              Enterprise-grade compliance features ensure your financial data meets regulatory requirements.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <ComplianceFeature
              icon={<FileCheck className="h-8 w-8 text-[#028475]" />}
              title="Document Traceability"
              description="Built-in document traceability and change logs for complete audit trails"
            />
            <ComplianceFeature
              icon={<FileText className="h-8 w-8 text-[#028475]" />}
              title="Exportable Reports"
              description="Exportable reports for tax audits, financial reviews, or internal reporting"
            />
            <ComplianceFeature
              icon={<Shield className="h-8 w-8 text-[#028475]" />}
              title="Secure Infrastructure"
              description="GDPR- and SOC2-compliant infrastructure for data security and privacy"
            />
          </div>
        </div>
      </section>

      {/* Integrated Across StreamLnk Section */}
      <section className="py-16 md:py-24 bg-[#F2F2F2]">
        <div className="container px-4 md:px-6">
          <div className="grid gap-12 lg:grid-cols-2 items-center">
            <div>
              <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-6">Integrated Across StreamLnk</h2>
              <p className="text-gray-600 mb-8">
                The Invoicing Center is seamlessly integrated with StreamLnk's comprehensive ecosystem.
              </p>

              <div className="space-y-4">
                <IntegrationFeature
                  icon={<Link className="h-6 w-6 text-[#028475]" />}
                  title="Payment Workflow Integration"
                  description="Linked to payment workflows (wire, escrow, BNPL)"
                />
                <IntegrationFeature
                  icon={<Building2 className="h-6 w-6 text-[#028475]" />}
                  title="Portal Embedding"
                  description="Embedded in MyStreamLnk, E-Stream, StreamFreight, and StreamPak portals"
                />
                <IntegrationFeature
                  icon={<DollarSign className="h-6 w-6 text-[#028475]" />}
                  title="Treasury Dashboard Sync"
                  description="Syncs with Treasury Dashboard for payment visibility"
                />
              </div>
            </div>
            <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
              <Image
                src="/placeholder.svg?height=400&width=600"
                alt="Integration diagram"
                fill
                className="object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Use Cases</h2>
            <p className="text-gray-600 max-w-3xl">
              The Invoicing Center serves various roles across your supply chain.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <UseCaseCard
              icon={<Building2 className="h-8 w-8 text-[#028475]" />}
              title="Suppliers"
              description="Bill for shipped goods and track buyer payment compliance"
              features={["Generate invoices from shipments", "Track payment status", "Manage receivables efficiently"]}
            />
            <UseCaseCard
              icon={<Users className="h-8 w-8 text-[#028475]" />}
              title="Buyers"
              description="Centralize payable tracking and automate invoice reconciliation"
              features={["Organize incoming invoices", "Schedule payments", "Match invoices to purchase orders"]}
            />
            <UseCaseCard
              icon={<DollarSign className="h-8 w-8 text-[#028475]" />}
              title="Agents & Distributors"
              description="Monitor commission-based invoicing and payout cycles"
              features={["Track commission invoices", "Monitor payment schedules", "Generate commission reports"]}
            />
            <UseCaseCard
              icon={<Truck className="h-8 w-8 text-[#028475]" />}
              title="Freight & Packaging Partners"
              description="Submit and validate service invoices tied to shipment IDs"
              features={["Link invoices to shipments", "Track service billing", "Manage logistics-related payments"]}
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-[#F2F2F2]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Simplify Financial Operations Across Your Supply Chain
            </h2>
            <p className="text-gray-600 max-w-3xl mb-8">
              With the StreamLnk Invoicing Center, you'll spend less time chasing paperwork and more time optimizing
              your cash flow.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button className="bg-[#004235] hover:bg-[#004235]/90 px-8 py-6 text-lg">
                Get Started <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button variant="outline" className="border-[#028475] text-[#028475] px-8 py-6 text-lg">
                Request Demo
              </Button>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
