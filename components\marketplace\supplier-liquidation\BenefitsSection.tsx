import { Banknote, <PERSON><PERSON><PERSON>, Clock, DollarSign, Package, ShoppingBag, Warehouse } from "lucide-react";

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">Maximize Recovery, Minimize Hassle, Optimize Cash Flow</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-10">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <Clock className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Faster Inventory Turnover</h4>
              </div>
              <p className="text-gray-700">Quickly sell surplus, offgrade, or seasonal stock.</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <DollarSign className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Maximized Price Realization</h4>
              </div>
              <p className="text-gray-700">Competitive bidding from a global pool of buyers can achieve better prices than traditional liquidation channels.</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <Warehouse className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Reduced Holding Costs & Tax Burdens</h4>
              </div>
              <p className="text-gray-700">Minimize warehousing costs and avoid year-end inventory taxes.</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <ShoppingBag className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Access to New Buyers</h4>
              </div>
              <p className="text-gray-700">Reach opportunistic buyers and new market segments you might not otherwise connect with.</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <Package className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Efficient & Transparent Process</h4>
              </div>
              <p className="text-gray-700">Automated auction management reduces administrative overhead.</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <Banknote className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Improved Cash Flow</h4>
              </div>
              <p className="text-gray-700">Convert slow-moving assets into working capital quickly.</p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm col-span-1 md:col-span-2 lg:col-span-3">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <BarChart className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Data-Driven Insights</h4>
              </div>
              <p className="text-gray-700">Use post-auction analytics to understand market appetite for different types of liquidated stock.</p>
            </div>
          </div>

          <div className="text-center mt-12">
            <h3 className="text-2xl font-bold text-[#004235] mb-4">Turn Your Surplus Stock into a Strategic Advantage with StreamLnk Auctions.</h3>
          </div>
        </div>
      </div>
    </section>
  );
}