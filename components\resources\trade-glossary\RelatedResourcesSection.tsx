"use client";

import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { BookMarked, MessageSquare, Newspaper, ArrowRight } from 'lucide-react';

interface ResourceLink {
  href: string;
  icon: React.ElementType;
  title: string;
  description: string;
}

const resources: ResourceLink[] = [
  {
    href: "/resources/faqs",
    icon: BookMarked,
    title: "Frequently Asked Questions",
    description: "Find answers to common questions about StreamLnk services and platform features."
  },
  {
    href: "/support",
    icon: MessageSquare,
    title: "Support Hub",
    description: "Access our comprehensive support center for assistance and guidance."
  },
  {
    href: "/resources/latest-articles",
    icon: Newspaper,
    title: "Latest Articles & Insights",
    description: "Stay informed with 'Delivered by StreamLnk' - our hub for industry news and analysis."
  }
];

export default function RelatedResourcesSection() {
  return (
    <section className="py-16 md:py-20 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-10 text-center">
            Continue Your Learning Journey
          </h2>
          <p className="text-lg text-gray-700 mb-12 text-center">
            Explore other helpful resources to deepen your understanding of global trade and the StreamLnk platform.
          </p>

          <div className="grid md:grid-cols-3 gap-8">
            {resources.map((resource) => (
              <div key={resource.title} className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 flex flex-col border border-gray-200">
                <div className="flex items-center text-[#028475] mb-4">
                  <resource.icon className="h-8 w-8 mr-3" />
                  <h3 className="text-xl font-semibold text-[#004235]">{resource.title}</h3>
                </div>
                <p className="text-gray-600 mb-6 flex-grow">
                  {resource.description}
                </p>
                <Button variant="outline" className="mt-auto border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full" asChild>
                  <Link href={resource.href}>
                    Explore Now
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}