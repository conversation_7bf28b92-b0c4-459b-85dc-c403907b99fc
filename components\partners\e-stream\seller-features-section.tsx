import { BarChart3, Globe, Package, Server, Shield } from "lucide-react"

const features = [
  {
    title: "RFQ & Quote Management",
    description: "Respond in-platform or set up auto-pricing based on volume, region, and buyer type.",
    icon: <BarChart3 className="h-10 w-10 text-[#028475]" />,
  },
  {
    title: "Auction Participation",
    description: "List inventory in flash auctions to move stock quickly, especially during year-end periods or slowdowns.",
    icon: <Server className="h-10 w-10 text-[#028475]" />,
  },
  {
    title: "Tiered Pricing Controls",
    description: "Set different pricing for buyers by country, order size, or buyer classification.",
    icon: <Globe className="h-10 w-10 text-[#028475]" />,
  },
  {
    title: "Supplier Dashboard",
    description: "Monitor sales per SKU, quote success rate, pending payments, and document expiry in one place.",
    icon: <Package className="h-10 w-10 text-[#028475]" />,
  },
  {
    title: "Buyer Segmentation",
    description: "View anonymous buyer types (location, industry, tier) without exposing your own data.",
    icon: <Shield className="h-10 w-10 text-[#028475]" />,
  },
  {
    title: "Real-time Market Analytics",
    description: "Gain insights to optimize your offerings and strategy with demand forecasts.",
    icon: <BarChart3 className="h-10 w-10 text-[#028475]" />,
  },
]

export function SellerFeaturesSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">Built for Industrial Sellers</h2>
        <p className="text-lg text-center max-w-3xl mx-auto mb-12">
          Unlike consumer marketplaces, E-Stream is engineered for the complexity of industrial sales.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="bg-[#F2F2F2] rounded-lg p-6 flex flex-col">
              <div className="mb-4">{feature.icon}</div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">{feature.title}</h3>
              <p className="text-gray-700 flex-grow">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
