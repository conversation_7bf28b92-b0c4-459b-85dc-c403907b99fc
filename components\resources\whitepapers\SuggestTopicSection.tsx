"use client";

import { <PERSON><PERSON> } from '@/components/ui/button';
import { ArrowRight, Lightbulb } from 'lucide-react'; // Added ArrowRight for new buttons
import Link from 'next/link';

export default function SuggestTopicSection() {
  return (
    <section className="py-16 bg-white md:py-24">
      <div className="container mx-auto px-4 text-center">
        <div className="max-w-3xl mx-auto">
          {/* <Lightbulb className="h-12 w-12 text-[#028475] mx-auto mb-6" /> */}
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            What Insights Are You Looking For?
          </h2>
          <p className="text-xl text-gray-700 mb-8">
            Is there a specific challenge or trend in industrial trade you'd like to see StreamLnk explore in a future whitepaper? Let us know your suggestions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
              size="lg"
              asChild
            >
              <a href="mailto:<EMAIL>?subject=Whitepaper Topic Suggestion">
                SUBMIT TOPIC IDEA
                <ArrowRight className="ml-2 h-5 w-5" />
              </a>
            </Button>
            <Button 
              variant="outline" 
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/resources/whitepapers"> {/* Assuming this is the whitepapers index page */}
                BROWSE WHITEPAPERS
              </Link>
            </Button>
          </div>
          <div className="mt-6">
            <Button 
              variant="link" 
              className="text-[#028475] hover:text-[#004235]"
              asChild
            >
              <Link href="/contact-us">
                TALK TO EXPERT
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}