"use client";

import * as Icons from "lucide-react";
import { MegaMenuNavItem, NavItemBase } from "./types";

// Define Navigation Data (Centralized) - Content structure based on the custom type
export const navigationItems: MegaMenuNavItem[] = [
  {
    id: "solutions",
    trigger: "Solutions",
    content: {
      type: 'custom',
      maxWidthClass: 'max-w-6xl',
      title: "Core Solutions",
      description: "Comprehensive solutions for your global trade and supply chain needs.",
      sidebarLinks: [
        { href: "/solutions", label: "View All Solutions", icon: <Icons.PackageSearch className="h-4 w-4" /> },
        { href: "/request-demo", label: "Request Demo", icon: <Icons.PresentationIcon className="h-4 w-4" /> },
        { href: "/case-studies", label: "View Cases", icon: <Icons.FileText className="h-4 w-4" /> },
      ],
      mainColumns: [
        {
          title: "Core Solutions",
          items: [
            { href: "/solutions/sourcing-procurement", description: "Find and manage suppliers efficiently", label: "Sourcing & Procurement" },
            { href: "/solutions/verified-suppliers", description: "Pre-vetted quality suppliers", label: "Verified Suppliers" },
            { href: "/solutions/product-discovery", description: "Find the right products", label: "Product Discovery" },
            { href: "/solutions/rfq-instant-pricing", description: "Quick quotes and pricing", label: "RFQs & Instant Pricing" },
            { href: "/solutions/global-logistics", description: "End-to-end logistics management", label: "Global Logistics" },
            { href: "/solutions/logistics-fulfillment", description: "End-to-end logistics management", label: "Logistics & Fulfillment" },
            { href: "/solutions/shipment-management", description: "Track and manage shipments", label: "Shipment Management" },
            { href: "/solutions/freight-coordination", description: "Coordinate freight efficiently", label: "Freight Coordination" },
            { href: "/solutions/customs-automation", description: "Streamline customs processes", label: "Customs Automation" },
            { href: "/solutions/warehousing-packaging", description: "Storage and packaging solutions", label: "Warehousing & Packaging" },
          ],
        },
        {
          title: "Payments & Trade Finance",
          items: [
            { href: "/solutions/multi-currency", description: "Global payment solutions", label: "Multi-Currency" },
            { href: "/solutions/escrow-milestones", description: "Secure transaction management", label: "Escrow & Milestones" },
            { href: "/solutions/b2b-bnpl", description: "Buy now, pay later for businesses", label: "B2B BNPL" },
            { href: "/solutions/ar-ap-management", description: "Manage receivables and payables", label: "AR/AP Management" },
          ],
        },
        {
          title: "Value-Added",
          items: [
            { href: "/solutions/data-analytics", description: "Actionable business insights", label: "Data & Analytics" },
            { href: "/solutions/streamindex-benchmarks", description: "Industry benchmarking tools", label: "StreamIndex™ Benchmarks" },
            { href: "/solutions/demand-supply-forecasts", description: "Predictive market analytics", label: "Demand & Supply Forecasts" },
            { href: "/solutions/risk-compliance-insights", description: "Risk management data", label: "Risk & Compliance Insights" },
            { href: "/solutions/risk-compliance", description: "Compliance management tools", label: "Risk & Compliance" },
            { href: "/solutions/iscore-partner-ratings", description: "Supplier reliability metrics", label: "iScore™ Partner Ratings" },
            { href: "/solutions/kyc-aml-verification", description: "Identity verification services", label: "KYC/AML Verification" },
            { href: "/solutions/esg-tracking", description: "Environmental and social governance", label: "ESG Tracking" },
          ],
        },
      ],
      bottomGrid: {
        title: "Auction Marketplace",
        description: "Connect buyers and sellers through our specialized auction platforms.",
        sections: [
          {
            title: "Marketplace Solutions",
            links: [
              { href: "/marketplace/supplier-liquidation", label: "Supplier Liquidation" },
              { href: "/marketplace/buyer-sourcing", label: "Buyer Sourcing" },
            ],
          },
          {
            title: "Why Us?",
            links: [
              { href: "/why-us/ecosystem-integration", label: "Ecosystem Integration" },
              { href: "/why-us/ai-efficiency", label: "AI Efficiency" },
              { href: "/why-us/global-network", label: "Global Network" },
            ],
          },
          {
            title: "Get Started",
            links: [
              { href: "/get-started/transparency-trust", label: "Transparency & Trust" },
            ],
          },
        ],
      },
      footer: {
        text: "Streamlining your global trade operations",
        link: "/solutions",
        linkText: "Explore all solutions →"
      },
    },
  },
  {
    id: "portals",
    trigger: "Portals",
    content: {
      type: 'custom',
      maxWidthClass: 'max-w-6xl',
      title: "Customer Portals",
      description: "Access our specialized platforms designed for different user roles in the supply chain.",
      sidebarLinks: [
        { href: "/portals", label: "Portal Overview", icon: <Icons.LayoutDashboard className="h-4 w-4" /> },
        { href: "/portal-support", label: "Portal Support", icon: <Icons.HelpCircle className="h-4 w-4" /> },
      ],
      mainColumns: [
        {
          title: "Customer Portals",
          items: [
            { href: "/portals/mystreamlnk", description: "For enterprise customers", label: "MyStreamLnk" },
            { href: "/portals/mystreamlnk-plus", description: "For agents and partners", label: "MyStreamLnk+" },
            { href: "/portals/e-stream", description: "For suppliers and vendors", label: "E-StreamSupplier" },
          ],
        },
        {
          title: "Specialized Portals",
          items: [
            { href: "/portals/streampak", description: "For packaging and warehouse partners", label: "StreamPak" },
            { href: "/portals/streamglobe-sea", description: "For sea carriers", label: "StreamGlobe" },
            { href: "/portals/streamglobe-customs", description: "For customs agents", label: "StreamGlobe" },
            { href: "/portals/streamfreight", description: "For freight management", label: "StreamFreight" },
            { href: "/portals/streamresources-plus", description: "For data and insights", label: "StreamResources+" },
          ],
        },
        {
          title: "Portal Features",
          items: [
            { href: "/portal-features/dashboards", description: "Customized user dashboards", label: "Personalized Dashboards" },
            { href: "/portal-features/reporting", description: "Advanced reporting tools", label: "Reporting & Analytics" },
            { href: "/portal-features/integrations", description: "Connect with your systems", label: "API & Integrations" },
            { href: "/portal-features/mobile", description: "Access on any device", label: "Mobile Access" },
          ],
        },
      ],
      footer: {
        text: "Secure, specialized access for every role in the supply chain",
        link: "/portals",
        linkText: "Learn more about our portals →"
      },
    },
  },
  {
    id: "industries",
    trigger: "Industries",
    content: {
      type: 'custom',
      maxWidthClass: 'max-w-6xl',
      title: "Industry Solutions",
      description: "Specialized solutions tailored to the unique needs of different industries.",
      sidebarLinks: [
        { href: "/industries", label: "All Industries", icon: <Icons.Factory className="h-4 w-4" /> },
        { href: "/industry-insights", label: "Industry Insights", icon: <Icons.LineChart className="h-4 w-4" /> },
      ],
      mainColumns: [
        {
          title: "Core Industries",
          items: [
            { href: "/industries/polymers-plastics", description: "Solutions for polymer manufacturers and processors", label: "Polymers & Plastics" },
            { href: "/industries/industrial-chemicals", description: "Chemical supply chain management", label: "Industrial Chemicals" },
            { href: "/industries/energy", description: "Energy sector logistics and procurement", label: "Energy" },
            { href: "/industries/automotive", description: "Automotive supply chain solutions", label: "Automotive" },
            { href: "/industries/packaging", description: "Packaging industry solutions", label: "Packaging" },
          ],
        },
        {
          title: "Expanding Verticals",
          items: [
            { href: "/industries/engineering-manufacturing", description: "Solutions for manufacturers", label: "Engineering & Manufacturing" },
            { href: "/industries/life-sciences-healthcare", description: "Healthcare supply chain management", label: "Life Sciences & Healthcare" },
            { href: "/industries/construction-materials", description: "Building materials logistics", label: "Construction Materials" },
            { href: "/industries/agri-commodities", description: "Agricultural product solutions", label: "Agri Commodities" },
            { href: "/industries/recycling-sustainability", description: "Sustainable supply chain solutions", label: "Recycling & Sustainability" },
            { href: "/industries/tech-electronics", description: "Electronics supply chain management", label: "Tech & Electronics" },
          ],
        },
        {
          title: "Featured Case Study",
          items: [
            { href: "/case-studies/automotive-transformation", description: "How we transformed an automotive supply chain", label: "Automotive Supply Chain Transformation" },
            { href: "/case-studies/chemical-logistics", description: "Optimizing chemical logistics operations", label: "Chemical Logistics Optimization" },
            { href: "/case-studies/sustainable-packaging", description: "Implementing sustainable packaging solutions", label: "Sustainable Packaging Initiative" },
          ],
        },
      ],
      footer: {
        text: "Industry-specific expertise for your unique challenges",
        link: "/industries",
        linkText: "Explore all industries →"
      },
    },
  },
  {
    id: "resources",
    trigger: "Resources",
    content: {
      type: 'custom',
      maxWidthClass: 'max-w-6xl',
      title: "Resource Center",
      description: "Knowledge, tools, and support to help you succeed.",
      sidebarLinks: [
        { href: "/resources", label: "Resource Hub", icon: <Icons.Library className="h-4 w-4" /> },
        { href: "/support", label: "Contact Support", icon: <Icons.HeadphonesIcon className="h-4 w-4" /> },
      ],
      mainColumns: [
        {
          title: "Insights & Learning",
          items: [
            { href: "/resources/blog", description: "Latest articles and updates", label: "Blog/Magazine" },
            { href: "/resources/latest-articles", description: "Recent publications", label: "Latest Articles" },
            { href: "/resources/market-analysis", description: "Industry market trends", label: "Market Analysis" },
            { href: "/resources/tech-trends", description: "Technology innovations", label: "Tech Trends" },
            { href: "/resources/whitepapers", description: "In-depth research", label: "Whitepapers & Reports" },
            { href: "/case-studies", description: "Customer success stories", label: "Case Studies" }, // Update to /case-studies
            { href: "/resources/trade-glossary", description: "Industry terminology", label: "Trade Glossary" },
          ],
        },
        {
          title: "Tools & Data",
          items: [
            { href: "/resources/streamindex", description: "Market intelligence platform", label: "StreamIndex™ Intel" },
            { href: "/resources/iscore", description: "Partner reliability metrics", label: "iScore™ Ratings" },
            { href: "/resources/esg-reporting", description: "Sustainability reporting tools", label: "ESG Reporting" },
            { href: "/resources/subscriptions", description: "Premium data subscriptions", label: "StreamResources+ Subscriptions" },
          ],
        },
        {
          title: "Support & Dev",
          items: [
            { href: "/support/faqs", description: "Frequently asked questions", label: "FAQs" },
            { href: "/developers", description: "API documentation and resources", label: "Developer Portal" },
            { href: "/support/customer-service", description: "Get help with our platform", label: "Customer Service" },
            { href: "/support/compliance-guides", description: "Regulatory compliance resources", label: "Compliance Guides" },
          ],
        },
      ],
      bottomGrid: {
        title: "Featured Resources",
        description: "Explore our most valuable resources for supply chain professionals.",
        sections: [
          {
            title: "Popular Resources",
            links: [
              { href: "/resources/supply-chain-optimization", label: "Supply Chain Optimization Guide" },
              { href: "/resources", label: "Digital Transformation Playbook" }, // Update to /resources or remove if not a real page
            ],
          },
          {
            title: "Trending Topics",
            links: [
              { href: "/resources/sustainability", label: "Sustainable Supply Chains" }, // Update to /resources or remove if not a real page
              { href: "/resources", label: "AI in Logistics" }, // Update to /resources or remove if not a real page
            ],
          },
          {
            title: "Support",
            links: [
              { href: "/support", label: "Contact Support" },
            ],
          },
        ],
      },
      footer: {
        text: "Stay informed with our latest resources",
        link: "/resources",
        linkText: "View all resources →"
      },
    },
  },
  {
    id: "partners",
    trigger: "Partners",
    content: {
      type: 'custom',
      maxWidthClass: 'max-w-6xl',
      title: "Partner Programs",
      description: "Join our ecosystem and grow your business with StreamLnk.",
      sidebarLinks: [
        { href: "/partners", label: "Partner Overview", icon: <Icons.Users className="h-4 w-4" /> },
        { href: "/partners/opportunities", label: "View All Opportunities", icon: <Icons.Briefcase className="h-4 w-4" /> },
      ],
      mainColumns: [
        {
          title: "Join Us",
          items: [
            { href: "/partners/sell-on-estream", description: "Become a supplier on our platform", label: "Sell on E-Stream" },
            { href: "/partners/offer-freight", description: "Provide freight services", label: "Offer Freight" },
            { href: "/partners/sea-freight", description: "Join as a sea freight provider", label: "Provide Sea Freight" },
            { href: "/partners/customs", description: "Offer customs services", label: "Join Customs" },
            { href: "/partners/packaging-warehouse", description: "List your packaging or warehouse services", label: "List Packaging/Warehouse" },
            { href: "/partners/agent", description: "Become a StreamLnk agent", label: "Register Agent" },
            { href: "/partners/data-api", description: "Partner for data or API integration", label: "Become Data/API Partner" },
          ],
        },
        {
          title: "Programs",
          items: [
            { href: "/partners/strategic-alliances", description: "Form a strategic partnership", label: "Strategic Alliances" },
            { href: "/partners/tech-integrations", description: "Technology integration partnerships", label: "Tech Integrations" },
            { href: "/partners/channel-reseller", description: "Resell our solutions", label: "Channel & Reseller" },
            { href: "/rewards-program", description: "Partner benefits program", label: "Tier & Rewards Program" }, // Update to /rewards-program
            { href: "/partners/supplier-diversity", description: "Diversity and inclusion initiatives", label: "Supplier Diversity" },
          ],
        },
        {
          title: "Resources",
          items: [
            { href: "/partners/login", description: "Access partner portals", label: "Partner Logins" }, // Update to /login or remove if not a real page
            { href: "/partners", description: "Get started as a partner", label: "Onboarding Guides" }, // Update to /partners or remove if not a real page
            { href: "/partners", description: "Marketing materials for partners", label: "Marketing Assets" }, // Update to /partners or remove if not a real page
            { href: "/support", description: "Get help as a partner", label: "Partner Support" }, // Update to /support or remove if not a real page
          ],
        },
      ],
      footer: {
        text: "Join our global network of partners",
        link: "/partners",
        linkText: "Become a partner →"
      },
    },
  },
  {
    id: "company",
    trigger: "Company",
    content: {
      type: 'custom',
      maxWidthClass: 'max-w-6xl',
      title: "About StreamLnk",
      description: "Learn about our company, mission, and the team behind StreamLnk.",
      sidebarLinks: [
        { href: "/about-us", label: "About Us", icon: <Icons.Building2 className="h-4 w-4" /> },
        { href: "/contact-us", label: "Contact Us", icon: <Icons.Mail className="h-4 w-4" /> },
      ],
      mainColumns: [
        {
          title: "About",
          items: [
            { href: "/about-us", description: "Our purpose and vision", label: "Mission & Vision" },
            { href: "/about-us", description: "Meet our leadership team", label: "Leadership" },
            { href: "/investors", description: "Information for investors", label: "Investor Relations" }, // Keep as is, assuming it's an external link or future page
            { href: "/about-us", description: "Our commitment to sustainability", label: "ESG & Sustainability" },
            { href: "/about-us", description: "Diversity in our supply chain", label: "Supplier Diversity" },
          ],
        },
        {
          title: "Engage",
          items: [
            { href: "/careers", description: "Join our team", label: "Careers" },
            { href: "/press-center", description: "News and media resources", label: "Press & Media" },
            { href: "/events", description: "Upcoming events and webinars", label: "Events & Webinars" },
            { href: "/partners", description: "Brand partnership opportunities", label: "Brand Partnerships" }, // Update to /partners as /partnerships doesn't exist
            { href: "/contact-us", description: "Get in touch with us", label: "Contact Us" },
          ],
        },
        {
          title: "Featured News",
          items: [
            { href: "/news/latest-announcement", description: "Our latest company announcement", label: "StreamLnk Expands Global Presence" },
            { href: "/news/latest-announcement", description: "Our new sustainability program", label: "New Sustainability Initiative Launched" }, // Update to latest-announcement or remove if not a real page
            { href: "/news/latest-announcement", description: "Recent platform enhancements", label: "Platform Technology Update" }, // Update to latest-announcement or remove if not a real page
          ],
        },
      ],
      footer: {
        text: "Transforming global trade since 2015",
        link: "/about",
        linkText: "Learn more about us →"
      },
    },
  },
];

// Using the same portal links as defined in customer-portal-logins.tsx
// This is just for the mobile menu display
export const customerPortalLinks: NavItemBase[] = [
  { href: "/portals/mystreamlnk", label: "MyStreamLnk (Customer)" },
  { href: "/portals/mystreamlnk-plus", label: "MyStreamLnk+ (Agent)" },
  { href: "/portals/e-stream", label: "E-Stream (Supplier)" },
  { href: "/portals/streampak", label: "StreamPak (Packaging & Warehouse)" },
  { href: "/portals/streamglobe-sea", label: "StreamGlobe (Sea Carrier)" },
  { href: "/portals/streamglobe-customs", label: "StreamGlobe (Customs Agent)" },
  { href: "/portals/streamfreight", label: "StreamFreight (Freight Management)" },
  { href: "/portals", label: "StreamResources+ (Data Insights)" },
];

// Location selection link - kept for reference in mobile menu
export const locationLink: NavItemBase = { href: "/location", label: "Select Location" };