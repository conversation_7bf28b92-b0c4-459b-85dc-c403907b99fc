"use client"

import { Users, Factory, TrendingUp } from "lucide-react"

export default function StrategicBenefitsSection() {
  const userBenefits = [
    {
      title: "For Suppliers (E-Stream Users)",
      icon: <Users className="h-10 w-10 text-[#028475]" />,
      benefits: [
        "Optimize production schedules based on predicted demand surges.",
        "Adjust pricing strategies proactively in response to anticipated market shifts.",
        "Strategically time inventory liquidation auctions during forecasted demand peaks or before anticipated lulls.",
        "Identify underserved regions with growing demand for their products."
      ]
    },
    {
      title: "For Buyers/Manufacturers (MyStreamLnk Users)",
      icon: <Factory className="h-10 w-10 text-[#028475]" />,
      benefits: [
        "Secure critical materials in advance of predicted price increases or supply shortages.",
        "Adjust inventory holding strategies based on supply forecasts.",
        "Plan procurement budgets more accurately.",
        "Identify optimal times to enter the market for spot purchases."
      ]
    },
    {
      title: "For Traders & Financial Institutions",
      icon: <TrendingUp className="h-10 w-10 text-[#028475]" />,
      benefits: [
        "Inform trading strategies and risk management for commodity-linked assets.",
        "Assess market sentiment and potential investment opportunities."
      ]
    }
  ]

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Turn Foresight into Strategic Action
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamLnk's forecasting empowers strategic decisions:
          </p>
          
          <div className="grid md:grid-cols-3 gap-8">
            {userBenefits.map((userType, index) => (
              <div key={index} className="bg-[#f3f4f6] p-6 rounded-lg">
                <div className="flex flex-col items-center mb-6 text-center">
                  <div className="mb-4">{userType.icon}</div>
                  <h3 className="text-xl font-semibold text-[#004235]">{userType.title}</h3>
                </div>
                <ul className="space-y-3">
                  {userType.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex} className="flex items-start">
                      <span className="text-[#028475] mr-2 font-bold">•</span>
                      <span className="text-gray-700">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}