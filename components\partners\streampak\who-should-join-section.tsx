import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { FileCheck, Layers, Package, TruckIcon, Warehouse } from "lucide-react"

export function WhoShouldJoinSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="w-10 h-1 bg-[#028475]"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">Who Should Join StreamPak?</h2>
          <p className="text-gray-700">We are looking for reliable and professional partners specializing in:</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <Card className="border-none shadow-lg">
            <CardContent className="p-8">
              <div className="bg-[#004235] p-4 rounded-full w-16 h-16 flex items-center justify-center mb-6">
                <Package className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-[#004235] mb-4">Packaging and Repackaging Facilities</h3>
              <p className="text-gray-600">Services for breaking bulk, custom packaging, and material conversion.</p>
            </CardContent>
          </Card>

          <Card className="border-none shadow-lg">
            <CardContent className="p-8">
              <div className="bg-[#004235] p-4 rounded-full w-16 h-16 flex items-center justify-center mb-6">
                <FileCheck className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-[#004235] mb-4">Bagging and Labeling Operators</h3>
              <p className="text-gray-600">Expertise in precise bagging, accurate labeling, and compliance marking.</p>
            </CardContent>
          </Card>

          <Card className="border-none shadow-lg">
            <CardContent className="p-8">
              <div className="bg-[#004235] p-4 rounded-full w-16 h-16 flex items-center justify-center mb-6">
                <Warehouse className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-[#004235] mb-4">Warehousing and Fulfillment Centers</h3>
              <p className="text-gray-600">Secure storage, inventory management, and order fulfillment capabilities.</p>
            </CardContent>
          </Card>

          <Card className="border-none shadow-lg">
            <CardContent className="p-8">
              <div className="bg-[#004235] p-4 rounded-full w-16 h-16 flex items-center justify-center mb-6">
                <Layers className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-[#004235] mb-4">Contract Storage Providers</h3>
              <p className="text-gray-600">
                Offering short-term or long-term storage solutions for various industrial materials.
              </p>
            </CardContent>
          </Card>

          <Card className="border-none shadow-lg">
            <CardContent className="p-8">
              <div className="bg-[#004235] p-4 rounded-full w-16 h-16 flex items-center justify-center mb-6">
                <TruckIcon className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-[#004235] mb-4">Third-Party Logistics Firms (3PLs)</h3>
              <p className="text-gray-600">Companies providing integrated warehousing and value-added services.</p>
            </CardContent>
          </Card>

          <div className="bg-[#004235] rounded-lg shadow-lg p-8 flex flex-col justify-center">
            <h3 className="text-xl font-bold text-white mb-4">Is Your Business a Good Fit?</h3>
            <p className="text-gray-200 mb-6">
              If your business excels in handling goods and ensuring they are ready for the next stage of their journey,
              StreamPak is your platform to connect with a global marketplace.
            </p>
            <Button
              variant="outline"
              className="text-white border-white hover:text-[#004235]"
            >
              Learn More About Requirements
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
