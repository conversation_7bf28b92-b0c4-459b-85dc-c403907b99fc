"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, FileText } from "lucide-react";
import Link from "next/link";

export default function FeaturedReportsSection() {
  const featuredItems = [
    {
      type: "Delivered Article",
      title: "The Impact of Geopolitics on Global Polymer Supply Chains in 2024",
      excerpt: "A look at how recent global events are reshaping sourcing strategies, impacting material availability, and influencing price volatility within the polymer industry.",
      buttonText: "READ ARTICLE",
      buttonLink: "/resources/blog/geopolitics-polymer-supply-chains-2024", // Placeholder link
      isPrimaryButton: true
    },
    {
      type: "StreamResources+ Report Teaser",
      title: "StreamIndex™ Q3 Report: Pricing Volatility in European Industrial Chemicals",
      excerpt: "Key findings from our latest premium report, offering subscribers deep insights into market dynamics, price benchmarks, and future outlooks for the chemical sector.",
      buttonText: "LEARN MORE / ACCESS REPORT",
      buttonLink: "/solutions/streamresources/reports/q3-european-chemicals", // Placeholder link
      isPrimaryButton: true
    },
    {
      type: "Delivered Article",
      title: "Navigating CSRD: A Guide for Industrial Supply Chains",
      excerpt: "Understand the implications of the Corporate Sustainability Reporting Directive (CSRD) and how StreamLnk can help you prepare for enhanced ESG disclosures.",
      buttonText: "READ ARTICLE",
      buttonLink: "/resources/blog/csrd-guide-industrial-supply-chains", // Placeholder link
      isPrimaryButton: true
    }
  ];

  return (
    <section className="py-16 md:py-24 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Don't Miss Our Latest Analysis
          </h2>
          <p className="text-xl text-gray-700">
            Featured Insights & Reports
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 items-stretch">
          {featuredItems.map((item, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-lg flex flex-col h-full">
              <FileText className="h-10 w-10 text-[#028475] mb-4 self-start" />
              <p className="text-sm text-[#028475] font-semibold mb-1">{item.type.toUpperCase()}</p>
              <h3 className="text-xl font-semibold text-[#004235] mb-3 min-h-[3em]">{item.title}</h3>
              <p className="text-gray-600 text-sm mb-6 flex-grow">{item.excerpt}</p>
              <div className="mt-auto">
                <Button 
                  className={`${item.isPrimaryButton ? 'bg-[#004235] hover:bg-[#028475] text-white' : 'border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white'} w-full`}
                  size="lg"
                  asChild
                >
                  <Link href={item.buttonLink}>
                    {item.buttonText}
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}