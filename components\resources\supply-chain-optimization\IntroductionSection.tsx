"use client";

import { Lightbulb, Unplug, Network, BrainCircuit } from "lucide-react";

export default function IntroductionSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        {/* Centered Heading Block */}
        <div className="max-w-4xl mx-auto text-center">
          <Lightbulb className="h-12 w-12 text-[#028475] mx-auto mb-6 md:mb-8" />
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            The Imperative of a Modernized Supply Chain
          </h2>
          <p className="text-xl text-[#028475] font-semibold mb-2">
            Navigating Complexity, Driving Efficiency
          </p>
          <div className="w-24 h-1 bg-[#028475] mx-auto mb-8 md:mb-10"></div>
        </div>

        {/* Single Column for Feature Cards */}
        <div className="mt-10 md:mt-12 max-w-3xl mx-auto space-y-8 md:space-y-10">
          {/* Card 1 */}
          <div className="flex items-start space-x-4 p-6 bg-slate-50 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300">
            <Unplug className="h-10 w-10 text-[#028475] mt-1 flex-shrink-0" />
            <div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">
                Overcome Operational Bottlenecks
              </h3>
              <p className="text-gray-700 leading-relaxed">
                Move beyond traditional manual processes and siloed information
                that lead to inefficiencies, increased operational costs, and
                critical missed opportunities.
              </p>
            </div>
          </div>

          {/* Card 2 */}
          <div className="flex items-start space-x-4 p-6 bg-slate-50 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300">
            <Network className="h-10 w-10 text-[#028475] mt-1 flex-shrink-0" />
            <div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">
                Build a Resilient Digital Supply Chain
              </h3>
              <p className="text-gray-700 leading-relaxed">
                Leverage cutting-edge platforms like StreamLnk to create agile,
                data-driven operations that span procurement, logistics,
                inventory management, and compliance.
              </p>
            </div>
          </div>

          {/* Card 3 */}
          <div className="flex items-start space-x-4 p-6 bg-slate-50 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300">
            <BrainCircuit className="h-10 w-10 text-[#028475] mt-1 flex-shrink-0" />
            <div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">
                Harness the Power of AI
              </h3>
              <p className="text-gray-700 leading-relaxed">
                Strategically implement transformative technologies like
                Artificial Intelligence for smarter, predictive supply chain
                management and to unlock new levels of efficiency.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}