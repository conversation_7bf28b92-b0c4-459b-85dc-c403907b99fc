import { ChevronRight } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

export function WhoWeAre() {
  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row gap-12 items-center justify-between">
          <div className="md:w-1/2 max-w-lg">
            <div className="flex items-center mb-6">
              <div className="w-12 h-1 bg-[#18b793] mr-3"></div>
              <h2 className="text-3xl font-bold text-[#004235]">Who we are</h2>
            </div>
            <p className="text-gray-700 mb-4">
              StreamLnk is a next-gen digital platform transforming the global energy market. Through breakthrough technology, we streamline the sourcing, trading, and delivery of energy products, creating a fully integrated, transparent, and secure marketplace.
            </p>
            <p className="text-gray-700 mb-6">
              Our platform offers real-time auctions, enabling buyers and suppliers to engage in dynamic pricing and make instant, data-driven decisions, enhancing the overall trading experience. With our real-time, data-driven insights, businesses can respond quickly to market shifts, assess supply chain risks, and detect fraud, ensuring efficiency, security, and smarter decision-making across the global energy ecosystem.
            </p>
            <Link href="/about-us" className="text-[#18b793] font-medium flex items-center mt-8">
              LEARN MORE <ChevronRight className="h-4 w-4 ml-1" />
            </Link>
          </div>
          <div className="md:w-1/2 flex justify-center">
            <div className="relative rounded-lg overflow-hidden max-w-lg">
              <Image
                src="/images/homepage/Ship into section.png"
                alt="Container ship in ocean - global energy trade"
                width={600}
                height={400}
                className="w-full h-auto"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

