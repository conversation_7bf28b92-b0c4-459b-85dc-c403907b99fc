"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, BrainCircuit } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Decoding the Future: Key Technology Trends Shaping Industrial Trade
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Stay ahead of the technological curve. StreamLnk explores the innovations that are redefining efficiency, transparency, and resilience in global sourcing, logistics, and manufacturing.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                className="bg-[#004235] hover:bg-[#028475] text-white px-6"
                size="lg"
                asChild
              >
                <Link href="/blog"> {/* Assuming '/blog' or a similar route for tech insights */}
                  <BookOpen className="mr-2 h-5 w-5" />
                  LATEST INSIGHTS
                </Link>
              </Button>
              <Button 
                variant="outline"
                className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white px-6"
                size="lg"
                asChild
              >
                <Link href="/solutions/ai-in-supply-chain"> {/* Assuming a page for AI solutions */}
                  <BrainCircuit className="mr-2 h-5 w-5" />
                  STREAMLNK AI
                </Link>
              </Button>
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/resources/tech-trends/hero-placeholder.jpg" // Placeholder image, to be updated
              alt="Key Technology Trends Shaping Industrial Trade by StreamLnk"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
}