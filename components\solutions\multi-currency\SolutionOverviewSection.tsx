import { CheckCircle, Globe, DollarSign, CreditCard, Shield } from "lucide-react";

export default function SolutionOverviewSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Global Trade, Simplified Payments: How StreamLnk Handles Multiple Currencies
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            StreamLnk is engineered to facilitate smooth and cost-effective international financial transactions. Our platform integrates multi-currency functionality directly into your procurement and sales workflows:
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-semibold text-[#004235] mb-4 flex items-center">
                <Globe className="h-6 w-6 text-[#028475] mr-3" />
                Multi-Currency Quoting & Invoicing
              </h3>
              <ul className="space-y-2 text-gray-700">
                <li>• Suppliers (on E-Stream) can list products and issue quotes in their preferred local currency or major trade currencies (USD, EUR, etc.).</li>
                <li>• Buyers (on MyStreamLnk) can view indicative prices in their local currency and receive final invoices in agreed-upon transaction currencies.</li>
              </ul>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-semibold text-[#004235] mb-4 flex items-center">
                <DollarSign className="h-6 w-6 text-[#028475] mr-3" />
                Transparent FX Management
              </h3>
              <ul className="space-y-2 text-gray-700">
                <li>• StreamLnk partners with leading global payment processors and FX providers to offer competitive, near-real-time exchange rates.</li>
                <li>• Indicative FX rates are displayed during the quoting process where applicable, providing transparency before commitment.</li>
              </ul>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-semibold text-[#004235] mb-4 flex items-center">
                <CreditCard className="h-6 w-6 text-[#028475] mr-3" />
                Centralized Treasury Operations
              </h3>
              <p className="text-gray-700">StreamLnk manages secure multi-currency accounts to facilitate efficient collection from buyers and payouts to suppliers and service providers globally.</p>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="text-xl font-semibold text-[#004235] mb-4 flex items-center">
                <Shield className="h-6 w-6 text-[#028475] mr-3" />
                Localized Payment Instructions
              </h3>
              <p className="text-gray-700">Buyers receive clear payment instructions tailored to their country and the transaction currency, minimizing transfer errors.</p>
            </div>
          </div>
          
          <div className="mt-8 space-y-4">
            {[
              "Automated Reconciliation: Our system helps reconcile payments across different currencies, simplifying accounting for both buyers and suppliers.",
              "Reduced Transaction Fees: By leveraging specialized FX partners and optimizing payment routing, StreamLnk aims to reduce the overall cost of cross-border transactions compared to traditional banking methods.",
              "Support for Major Trade Currencies: Full support for transactions in USD, EUR, GBP, JPY, CNY, AED, and a growing list of other key global and regional currencies."
            ].map((feature, index) => (
              <div key={index} className="flex items-start">
                <div className="bg-[#028475]/10 p-2 rounded-full mr-4 mt-1">
                  <CheckCircle className="h-5 w-5 text-[#028475]" />
                </div>
                <p className="text-gray-700">{feature}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}