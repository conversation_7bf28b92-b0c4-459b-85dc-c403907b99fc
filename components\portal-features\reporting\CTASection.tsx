import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import { ArrowRight } from "lucide-react";

export default function CTASection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">Unlock Your Trade Data Power</h2>
          {/* <h3 className="text-xl font-semibold text-[#028475] mb-8">Accessing Your Reports & Analytics</h3> */}
          <p className="text-xl text-gray-700 mb-8">
            Access reports in your portal. Explore StreamResources+ for advanced analytics.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                REQUEST REPORTING DEMO
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button 
              variant="outline" 
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
              size="lg"
              asChild
            >
              <Link href="/solutions/data-analytics"> {/* Assuming StreamResources+ relates to data-analytics */}
                EXPLORE STREAMRESOURCES+
              </Link>
            </Button>
          </div>
          <div className="mt-6">
            <Button 
              variant="link" 
              className="text-[#028475] hover:text-[#004235]"
              asChild
            >
              <Link href="/resources/sample-reports"> {/* Assuming a page for sample reports */}
                VIEW SAMPLE REPORTS
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}