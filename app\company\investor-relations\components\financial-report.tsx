import { FileText, Download } from "lucide-react"
import { Button } from "@/components/ui/button"

interface FinancialReportProps {
  title: string
  date: string
  description: string
  fileSize: string
  downloadLink: string
}

export default function FinancialReport({ title, date, description, fileSize, downloadLink }: FinancialReportProps) {
  return (
    <div className="p-6 bg-white rounded-lg border border-[#f3f4f6] flex flex-col md:flex-row md:items-center justify-between gap-4">
      <div className="flex items-start gap-4">
        <div className="p-2 rounded-full bg-[#004235]/10 flex-shrink-0">
          <FileText className="h-6 w-6 text-[#028475]" />
        </div>
        <div>
          <h3 className="font-semibold text-[#004235] mb-1">{title}</h3>
          <p className="text-sm text-gray-500 mb-1">{date}</p>
          <p className="text-sm text-gray-600">{description}</p>
        </div>
      </div>
      <div className="flex items-center gap-2 md:flex-shrink-0">
        <span className="text-sm text-gray-500">{fileSize}</span>
        <Button variant="outline" size="sm" className="border-[#028475] text-[#028475]" asChild>
          <a href={downloadLink}>
            <Download className="mr-2 h-4 w-4" />
            Download
          </a>
        </Button>
      </div>
    </div>
  )
}
