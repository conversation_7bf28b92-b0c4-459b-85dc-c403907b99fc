"use client"

import Image from "next/image";
import Link from "next/link";
import { ChevronDown } from "lucide-react";

export function CareersMainNav() {
  return (
    <>
      <header className="sticky top-0 z-50 w-full border-b bg-white">
        <div className="container flex h-16 items-center justify-between">
          {/* StreamLnk Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <Image
              src="/images/logos/dark logo dark white background.png"
              alt="StreamLnk Logo"
              width={160}
              height={35}
              priority
            />
          </Link>

          {/* Combined Navigation and Saved Jobs */}
          <div className="flex items-center space-x-6">
            {/* Main Navigation Links */}
            <nav className="hidden items-center space-x-6 text-sm font-medium md:flex">
              {/* Who We Are Dropdown */}
              <div className="relative group">
                <button className="text-gray-600 hover:text-gray-900 flex items-center py-4">
                  Who We Are <ChevronDown className="ml-1 h-4 w-4" />
                </button>
                <div className="absolute left-0 top-full z-50 mt-1 w-56 rounded-md bg-white p-2 shadow-lg ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  <Link href="#" className="block rounded-md px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                    Who We Are
                  </Link>
                  <Link href="#" className="block rounded-md px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                    Diversity Inclusion & Belonging
                  </Link>
                  <Link href="#" className="block rounded-md px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                    How We Operate
                  </Link>
                </div>
              </div>

              {/* Our Career Paths Dropdown */}
              <div className="relative group">
                <button className="text-gray-600 hover:text-gray-900 flex items-center py-4">
                  Our Career Paths <ChevronDown className="ml-1 h-4 w-4" />
                </button>
                <div className="absolute left-0 top-full z-50 mt-1 w-56 rounded-md bg-white p-2 shadow-lg ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  <Link href="#" className="block rounded-md px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                    Our Career Paths
                  </Link>
                  <Link href="#" className="block rounded-md px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                    Early Careers
                  </Link>
                  <Link href="#" className="block rounded-md px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                    Experienced Careers
                  </Link>
                  <Link href="#" className="block rounded-md px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                    We Value Veterans
                  </Link>
                  <Link href="#" className="block rounded-md px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                    What We Look For
                  </Link>
                </div>
              </div>

              {/* You & StreamLnk Dropdown */}
              <div className="relative group">
                <button className="text-gray-600 hover:text-gray-900 flex items-center py-4">
                  You & StreamLnk <ChevronDown className="ml-1 h-4 w-4" />
                </button>
                <div className="absolute left-0 top-full z-50 mt-1 w-56 rounded-md bg-white p-2 shadow-lg ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  <Link href="#" className="block rounded-md px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                    You & StreamLnk
                  </Link>
                  <Link href="#" className="block rounded-md px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                    Rewards & Benefits
                  </Link>
                </div>
              </div>

              {/* Regular Links */}
              <Link href="#" className="text-gray-600 hover:text-gray-900">
                Join Our Talent Community
              </Link>
              <Link href="#" className="text-gray-600 hover:text-gray-900">
                Events
              </Link>
              <Link href="#" className="text-gray-600 hover:text-gray-900">
                Application Status
              </Link>
            </nav>

            {/* Divider */}
            <span className="hidden h-6 w-px bg-gray-300 md:inline-block" aria-hidden="true"></span>

            {/* Saved Jobs */}
            <div className="flex items-center space-x-4">
              <Link href="#" className="text-sm font-medium text-gray-600 hover:text-gray-900 flex items-center">
                Saved jobs
                <span className="ml-2 inline-flex h-5 w-5 items-center justify-center rounded-full bg-[#00A991] text-xs font-bold text-white">
                  0
                </span>
              </Link>
              {/* Add Login/Profile button if needed */}
            </div>
          </div>
        </div>
      </header>
    </>
  );
}