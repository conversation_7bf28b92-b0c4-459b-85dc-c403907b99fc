import { Building2, <PERSON><PERSON><PERSON>, Wallet, Clock } from "lucide-react";
import EligibilityCard from "@/components/finance-payments/bnpl/eligibility-card";

export default function EligibilityRiskControlsSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Eligibility & Risk Controls</h2>
          <p className="text-gray-600 max-w-3xl">
            StreamLnk maintains high standards for BNPL eligibility to ensure a healthy ecosystem.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <EligibilityCard
            icon={<Building2 className="h-8 w-8 text-[#028475]" />}
            title="Verified Business Accounts"
            description="Available only to verified business accounts"
          />
          <EligibilityCard
            icon={<LineChart className="h-8 w-8 text-[#028475]" />}
            title="StreamIndex Score"
            description="StreamIndex score must meet threshold"
          />
          <EligibilityCard
            icon={<Wallet className="h-8 w-8 text-[#028475]" />}
            title="BNPL Limits"
            description="BNPL limits assigned based on transaction volume and payment history"
          />
          <EligibilityCard
            icon={<Clock className="h-8 w-8 text-[#028475]" />}
            title="Late Payment Policies"
            description="Late fees or suspensions may apply for past-due accounts"
          />
        </div>
      </div>
    </section>
  );
}