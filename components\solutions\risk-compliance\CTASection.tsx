import { <PERSON><PERSON><PERSON> } from 'lucide-react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'

export default function CTASection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Building the Future of Trusted Industrial Trade
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            StreamLnk is dedicated to creating the most secure, compliant, and trustworthy digital ecosystem for global industrial trade. We continuously invest in technology, processes, and partnerships to uphold these standards and provide our users with a platform where they can conduct business with maximum confidence and minimal risk.
          </p>
            
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white"
              size="lg"
              asChild
            >
              <Link href="/security">
                LEARN MORE ABOUT OUR SECURITY PROTOCOLS
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
              
            <Button 
              variant="outline" 
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                REQUEST A DEMO
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}