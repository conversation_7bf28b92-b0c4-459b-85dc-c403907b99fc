"use client";

import { <PERSON><PERSON><PERSON><PERSON>, TrendingUp, Users, Zap, CheckCircle } from "lucide-react";

export default function ValuePropositionSection() {
  const propositions = [
    {
      icon: <ShieldCheck className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Enhanced Due Diligence",
      description: "Quickly and objectively vet potential new suppliers, buyers, or logistics providers using comprehensive, data-driven iScores™."
    },
    {
      icon: <TrendingUp className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Risk-Based Decision Making",
      description: "Prioritize engagement with higher-scoring partners and apply appropriate oversight or terms to those with lower scores, minimizing potential risks."
    },
    {
      icon: <Users className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Supplier/Partner Performance Management",
      description: "Utilize objective iScore™ data as a foundation for constructive performance discussions and collaborative improvement initiatives."
    },
    {
      icon: <Zap className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Building Resilient Supply Chains",
      description: "Select partners with proven reliability and strong iScores™ to minimize disruptions and ensure smoother operations."
    },
    {
      icon: <CheckCircle className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Improving Ecosystem Quality",
      description: "Incentivizes all platform participants to maintain high operational and compliance standards to achieve and sustain better iScores™."
    }
  ];

  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Mitigate Risk, Select with Confidence, Foster Quality
          </h2>
          <p className="text-xl text-gray-700 mb-12 text-center">
            How iScore™ Drives Value in Your Business
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {propositions.slice(0, 3).map((proposition, index) => (
              <div key={index} className="bg-white p-6 rounded-lg text-center flex flex-col items-center shadow-md">
                {proposition.icon}
                <h3 className="font-semibold text-[#004235] text-lg mb-2">{proposition.title}</h3>
                <p className="text-gray-600 text-sm">{proposition.description}</p>
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
            {propositions.slice(3).map((proposition, index) => (
              <div key={index} className="bg-white p-6 rounded-lg text-center flex flex-col items-center shadow-md">
                {proposition.icon}
                <h3 className="font-semibold text-[#004235] text-lg mb-2">{proposition.title}</h3>
                <p className="text-gray-600 text-sm">{proposition.description}</p>
              </div>
            ))}
          </div>

        </div>
      </div>
    </section>
  );
}