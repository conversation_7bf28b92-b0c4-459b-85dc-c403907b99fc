"use client"

import { useState } from "react";
import { ChevronUp } from "lucide-react";

interface FaqItem {
  id: string;
  question: string;
  answer: string;
}

const faqData: FaqItem[] = [
  {
    id: "when-tracking-appear",
    question: "When Will My Tracking Information Appear?",
    answer:
      "Tracking becomes visible after the shipment is processed and confirmed by the freight or customs partner. This typically occurs within 12-24 hours after order confirmation.",
  },
  {
    id: "why-no-movement",
    question: "Why Is My Tracking Number Not Showing Movement Yet?",
    answer:
      "This may be caused by a delay in pickup, customs clearance, or document validation. Your portal reflects the most up-to-date status. Please contact support if there's no change after 24 hours.",
  },
  {
    id: "track-without-number",
    question: "Can I Track My Shipment Without a Tracking Number?",
    answer: "Yes. Customers and agents can use PO Number, Order ID, or Invoice Reference. Suppliers can track based on contract ID, packaging schedule, or stream-assigned delivery batch numbers."
  },
  {
    id: "view-invoices-payment",
    question: "How Can I View My Invoices and Payment Status?",
    answer: "Customers: Use your MyStreamLnk dashboard under Invoices & Documents to view, pay, and upload proof. Suppliers: Access your E-Stream dashboard to view all fulfilled orders, pending payouts, and upload shipping documentation to release payments."
  },
  {
    id: "report-issues",
    question: "How Can I Report Product or Service Issues?",
    answer: "Customers: Report issues directly in your shipment dashboard under Report Product Concern. Suppliers: Log quality concerns or shipment inconsistencies under Report Delivery Exception in E-Stream."
  },
  {
    id: "agent-add-clients",
    question: "I’m an Agent – Can I Add New Clients?",
    answer: "Yes. Agents in MyStreamLnk+ can onboard new buyers via Manage Portfolio. Once verified, the customer will be linked to your account."
  },
  {
    id: "supplier-list-products",
    question: "I’m a Supplier – How Do I List New Products?",
    answer: "Navigate to Product Center in your E-Stream portal. Upload product specs, certifications, packaging info, pricing terms (CIF, CFR, DDP, etc.), and country availability. Products are reviewed and approved within 48 hours."
  },
  {
    id: "update-documents",
    question: "I Need Help Updating My Documents – Where Do I Go?",
    answer: "Customers and agents can upload missing compliance docs via My Profile > Compliance Center. Suppliers should update insurance, certifications, and packing standards under E-Stream > Partner Compliance."
  },
  {
    id: "overdue-invoice-document",
    question: "What Happens If My Invoice or Document Is Overdue?",
    answer: "Customers with overdue invoices will be notified 7/3/1 days in advance. StreamLnk will restrict new order placements and pause shipments until resolution. Suppliers with expired documents may be removed from active listings or blocked from receiving new RFQs until updates are completed."
  }
];

export function FaqSection() {
  const [expandedFaq, setExpandedFaq] = useState<string | null>(
    faqData.length > 0 ? faqData[0].id : null
  );

  const toggleFaq = (id: string) => {
    if (expandedFaq === id) {
      setExpandedFaq(null);
    } else {
      setExpandedFaq(id);
    }
  };

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="flex flex-col items-start mb-8">
          <div className="w-10 h-1 bg-[#028475] mb-3"></div>
          <h2 className="text-3xl font-bold text-[#004235]">
            Frequently Asked Questions
          </h2>
        </div>

        <div className="border-t border-gray-200">
          {faqData.map((item) => (
            <div key={item.id} className="border-b border-gray-200">
              <button
                onClick={() => toggleFaq(item.id)}
                className="w-full text-left py-6 flex justify-between items-center focus:outline-none"
              >
                <h3 className="text-xl font-medium text-[#004235]">
                  {item.question}
                </h3>
                <ChevronUp
                  className={`h-5 w-5 text-[#028475] transition-transform ${
                    expandedFaq === item.id ? "" : "rotate-180"
                  }`}
                />
              </button>
              <div
                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  expandedFaq === item.id ? "max-h-96 pb-6" : "max-h-0"
                }`}
              >
                <p className="text-gray-600">{item.answer}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}