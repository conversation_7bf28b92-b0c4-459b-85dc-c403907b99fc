import type { Metadata } from "next"
import HeroSection from "@/components/partners/auctions/hero-section"
import WhatIsSection from "@/components/partners/auctions/what-is-section"
import ParticipantsSection from "@/components/partners/auctions/participants-section"
import BenefitsSection from "@/components/partners/auctions/benefits-section"
import HowItWorksSection from "@/components/partners/auctions/how-it-works-section"
import AuctionLogicSection from "@/components/partners/auctions/auction-logic-section"
import AdvancedFeaturesSection from "@/components/partners/auctions/advanced-features-section"
import WhyItMattersSection from "@/components/partners/auctions/why-it-matters-section"
import CtaSection from "@/components/partners/auctions/cta-section"
import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"

export const metadata: Metadata = {
  title: "Join StreamLnk's Smart Auction Marketplace | StreamLnk",
  description:
    "Unlock dynamic inventory movement & competitive sourcing through real-time auctions on StreamLnk's AI-driven auction marketplace.",
}

export default function AuctionsPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <MainNav />
      <main>
      <HeroSection />
      <WhatIsSection />
      <ParticipantsSection />
      <BenefitsSection />
      <HowItWorksSection />
      <AuctionLogicSection />
      <AdvancedFeaturesSection />
      <WhyItMattersSection />
      <CtaSection />
      </main>
      <MainFooter />
    </div>
  )
}
