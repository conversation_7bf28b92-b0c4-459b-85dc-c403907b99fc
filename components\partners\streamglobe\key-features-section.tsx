import { <PERSON>Text, Play, Package, Clock, Bell, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

export function KeyFeaturesSection() {
  return (
    <section id="features" className="py-16 md:py-24 bg-white">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235]">
              Key Features & Tools in the StreamGlobe Portal
            </h2>
            <p className="mx-auto max-w-[700px] text-gray-600 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              Powerful tools designed to streamline your customs clearance workflow
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 mt-12">
          {[
            {
              icon: <FileText className="h-10 w-10 text-[#028475]" />,
              title: "Document Preview & Management",
              description:
                "View all assigned files (invoice, B/L, packing list, COA, etc.) with a single click before initiating clearance.",
            },
            {
              icon: <Play className="h-10 w-10 text-[#028475]" />,
              title: '"Start Clearance" Trigger',
              description: "Formally declare job acceptance and kick off the customs workflow within the system.",
            },
            {
              icon: <Package className="h-10 w-10 text-[#028475]" />,
              title: "Auto-Generated Document Package",
              description: "Receive a consolidated package of necessary documents, minimizing manual collection.",
            },
            {
              icon: <Clock className="h-10 w-10 text-[#028475]" />,
              title: "Real-Time Status Update Tool",
              description:
                'Easily update shipment clearance stages (e.g., "Documents Received," "Duty Paid," "Released from Customs").',
            },
            {
              icon: <Bell className="h-10 w-10 text-[#028475]" />,
              title: "AI-Powered Reminders & Alerts",
              description: "Get notified about upcoming deadlines, missing documentation, or required actions.",
            },
            {
              icon: <FileCheck className="h-10 w-10 text-[#028475]" />,
              title: "Certification & Compliance Document Center",
              description:
                "A dedicated space to upload and manage your customs license, POA templates, and other compliance paperwork.",
            },
            {
              icon: <BarChart className="h-10 w-10 text-[#028475]" />,
              title: "KPI Dashboard",
              description:
                "Track your performance metrics, including average clearance time, document accuracy rate, and partner satisfaction scores.",
            },
          ].map((item, index) => (
            <Card key={index} className="border border-gray-200 shadow-sm">
              <CardContent className="p-6 flex flex-col items-center text-center">
                <div className="rounded-full bg-[#F2F2F2] p-3 mb-4">{item.icon}</div>
                <h3 className="text-xl font-bold mb-2 text-[#004235]">{item.title}</h3>
                <p className="text-gray-600">{item.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
