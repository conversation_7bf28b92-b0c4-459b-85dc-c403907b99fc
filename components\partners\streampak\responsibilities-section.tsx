import { Check } from "lucide-react"
import Image from "next/image"

export function ResponsibilitiesSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div>
            <div className="flex items-center mb-4">
              <div className="w-10 h-1 bg-[#028475] mr-3"></div>
              <span className="text-[#028475] font-medium">PARTNER RESPONSIBILITIES</span>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-8">What You'll Be Responsible For</h2>

            <div className="space-y-6">
              <div className="flex items-start">
                <div className="bg-[#004235] rounded-full p-2 mr-4 mt-1 flex-shrink-0">
                  <Check className="h-4 w-4 text-white" />
                </div>
                <p className="text-gray-700">
                  Receiving and promptly confirming packaging, labeling, or storage job assignments.
                </p>
              </div>

              <div className="flex items-start">
                <div className="bg-[#004235] rounded-full p-2 mr-4 mt-1 flex-shrink-0">
                  <Check className="h-4 w-4 text-white" />
                </div>
                <p className="text-gray-700">
                  Providing real-time updates on material status: receiving, processing (repackaging, labeling), and
                  reloading/dispatch.
                </p>
              </div>

              <div className="flex items-start">
                <div className="bg-[#004235] rounded-full p-2 mr-4 mt-1 flex-shrink-0">
                  <Check className="h-4 w-4 text-white" />
                </div>
                <p className="text-gray-700">
                  Uploading and maintaining mandatory insurance documents, operational licenses, and relevant Standard
                  Operating Procedures (SOPs).
                </p>
              </div>

              <div className="flex items-start">
                <div className="bg-[#004235] rounded-full p-2 mr-4 mt-1 flex-shrink-0">
                  <Check className="h-4 w-4 text-white" />
                </div>
                <p className="text-gray-700">
                  Accurately maintaining inventory logs and communicating any damages, discrepancies, or quality
                  concerns.
                </p>
              </div>

              <div className="flex items-start">
                <div className="bg-[#004235] rounded-full p-2 mr-4 mt-1 flex-shrink-0">
                  <Check className="h-4 w-4 text-white" />
                </div>
                <p className="text-gray-700">
                  Flagging inbound shortages or overages upon arrival and confirming final packed/stored units.
                </p>
              </div>

              <div className="flex items-start">
                <div className="bg-[#004235] rounded-full p-2 mr-4 mt-1 flex-shrink-0">
                  <Check className="h-4 w-4 text-white" />
                </div>
                <p className="text-gray-700">
                  Ensuring all handling and documentation complies with customs, logistics, and client-specific
                  requirements for supply chain continuity.
                </p>
              </div>
            </div>
          </div>

          <div className="relative">
            <Image
              src="/images/partner/streampak/StreamPak responsibilities.webp"
              alt="StreamPak responsibilities"
              width={700}
              height={600}
              className="rounded-lg shadow-xl"
            />
          </div>
        </div>
      </div>
    </section>
  )
}
