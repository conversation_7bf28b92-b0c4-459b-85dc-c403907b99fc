import {
  Wallet,
  Lock,
  DollarSign,
  Bell,
} from "lucide-react"
import IntegrationFeature from "@/components/finance-payments/treasury-dashboard/integration-feature"

export default function TreasuryToolsIntegrationSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Treasury Tools Integration</h2>
          <p className="text-gray-600 max-w-3xl">
            Seamlessly integrated with StreamLnk's comprehensive financial ecosystem.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <IntegrationFeature
            icon={<Wallet className="h-8 w-8 text-[#028475]" />}
            title="BNPL Integration"
            description="Monitor Buy Now Pay Later transactions and upcoming payment schedules"
          />
          <IntegrationFeature
            icon={<Lock className="h-8 w-8 text-[#028475]" />}
            title="Escrow Integration"
            description="Track escrow funds and milestone-based payment releases"
          />
          <IntegrationFeature
            icon={<DollarSign className="h-8 w-8 text-[#028475]" />}
            title="Payment Workflows"
            description="Integrated with all StreamLnk payment methods and processes"
          />
          <IntegrationFeature
            icon={<Bell className="h-8 w-8 text-[#028475]" />}
            title="Real-Time Transaction Syncing"
            description="Directly linked to MyStreamLnk and E-Stream portals"
          />
        </div>
      </div>
    </section>
  )
}