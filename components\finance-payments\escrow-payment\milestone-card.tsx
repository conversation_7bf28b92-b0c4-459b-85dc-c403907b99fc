import type { ReactNode } from "react"
import { Card, CardContent } from "@/components/ui/card"

interface MilestoneCardProps {
  icon: ReactNode
  title: string
  description: string
}

export default function MilestoneCard({ icon, title, description }: MilestoneCardProps) {
  return (
    <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
      <CardContent className="p-4 flex flex-col items-center text-center space-y-3">
        <div className="p-2 rounded-full bg-[#004235]/10 mt-2">{icon}</div>
        <h3 className="font-semibold text-[#004235]">{title}</h3>
        <p className="text-sm text-gray-600">{description}</p>
      </CardContent>
    </Card>
  )
}
