"use client";
import { Check, TrendingUp, Shield, Sliders, MapPin, Users, Briefcase, BarChart3 } from "lucide-react";
import ReusableTabs, { type ReusableTabData } from "@/components/ui/reusable-tabs";

const SupplierBenefits = () => (
  <div className="border-2 border-[#F2F2F2] rounded-lg p-6 shadow-sm">
    <h3 className="text-xl font-semibold text-[#004235] mb-4 flex items-center">
      <Check className="h-6 w-6 text-[#028475] mr-2" />
      For Suppliers
    </h3>
    <div className="grid md:grid-cols-2 gap-4">
      <div className="flex items-start">
        <TrendingUp className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
        <div>
          <h4 className="font-medium text-[#004235]">Rapid Inventory Movement</h4>
          <p className="text-gray-600 text-sm">
            Move stock quickly, especially slow-moving or surplus items, at strategic pricing points.
          </p>
        </div>
      </div>
      <div className="flex items-start">
        <Shield className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
        <div>
          <h4 className="font-medium text-[#004235]">Financial Optimization</h4>
          <p className="text-gray-600 text-sm">
            Effectively manage end-of-year inventory to potentially optimize financial outcomes.
          </p>
        </div>
      </div>
      <div className="flex items-start">
        <Sliders className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
        <div>
          <h4 className="font-medium text-[#004235]">Brand Control</h4>
          <p className="text-gray-600 text-sm">
            Choose to maintain brand anonymity or reveal identity based on the listing type and strategy.
          </p>
        </div>
      </div>
      <div className="flex items-start">
        <MapPin className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
        <div>
          <h4 className="font-medium text-[#004235]">Targeted Reach</h4>
          <p className="text-gray-600 text-sm">
            List by specific region and offer flexible Incoterms (CIF, CFR, EXW, DDP, etc.).
          </p>
        </div>
      </div>
    </div>
  </div>
);

const BuyerBenefits = () => (
  <div className="border-2 border-[#F2F2F2] rounded-lg p-6 shadow-sm">
    <h3 className="text-xl font-semibold text-[#004235] mb-4 flex items-center">
      <Check className="h-6 w-6 text-[#028475] mr-2" />
      For Buyers
    </h3>
    <div className="grid md:grid-cols-2 gap-4">
      <div className="flex items-start">
        <TrendingUp className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
        <div>
          <h4 className="font-medium text-[#004235]">Access Competitive Offers</h4>
          <p className="text-gray-600 text-sm">
            Discover limited-time, competitively priced offers on essential industrial materials.
          </p>
        </div>
      </div>
      <div className="flex items-start">
        <Shield className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
        <div>
          <h4 className="font-medium text-[#004235]">Informed Bidding</h4>
          <p className="text-gray-600 text-sm">
            Filter auctions by detailed product specifications, quality grade, region, and supplier ratings.
          </p>
        </div>
      </div>
      <div className="flex items-start">
        <Sliders className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
        <div>
          <h4 className="font-medium text-[#004235]">Transparent Costs</h4>
          <p className="text-gray-600 text-sm">
            View delivery terms and estimated associated costs instantly.
          </p>
        </div>
      </div>
      <div className="flex items-start">
        <MapPin className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
        <div>
          <h4 className="font-medium text-[#004235]">Personalized Alerts</h4>
          <p className="text-gray-600 text-sm">
            Get notified for auctions specific to categories of interest (e.g., PET, HDPE, specific chemicals).
          </p>
        </div>
      </div>
    </div>
  </div>
);

const AgentBenefits = () => (
  <div className="border-2 border-[#F2F2F2] rounded-lg p-6 shadow-sm">
    <h3 className="text-xl font-semibold text-[#004235] mb-4 flex items-center">
      <Check className="h-6 w-6 text-[#028475] mr-2" />
      For Agents (MyStreamLnk+)
    </h3>
    <div className="grid md:grid-cols-2 gap-4">
      <div className="flex items-start">
        <TrendingUp className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
        <div>
          <h4 className="font-medium text-[#004235]">Activate Client Portfolios</h4>
          <p className="text-gray-600 text-sm">
            Directly engage your clients by inviting them to participate in auctions that match their needs.
          </p>
        </div>
      </div>
      <div className="flex items-start">
        <Shield className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
        <div>
          <h4 className="font-medium text-[#004235]">Curated Deal Invites</h4>
          <p className="text-gray-600 text-sm">
            Send direct invitations to specific clients for particularly relevant deals or campaigns.
          </p>
        </div>
      </div>
      <div className="flex items-start">
        <Sliders className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
        <div>
          <h4 className="font-medium text-[#004235]">Performance Tracking</h4>
          <p className="text-gray-600 text-sm">
            Monitor win/loss rates and participation levels across your client portfolio.
          </p>
        </div>
      </div>
      <div className="flex items-start">
        <MapPin className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
        <div>
          <h4 className="font-medium text-[#004235]">Regional Exclusivity</h4>
          <p className="text-gray-600 text-sm">
            Benefit from rules designed to avoid market saturation, potentially including regional exclusivity for certain campaigns.
          </p>
        </div>
      </div>
    </div>
  </div>
);

const benefitsTabsData: ReusableTabData[] = [
  {
    id: "suppliers",
    title: "For Suppliers",
    icon: Users,
    contentComponent: SupplierBenefits,
  },
  {
    id: "buyers",
    title: "For Buyers",
    icon: Briefcase,
    contentComponent: BuyerBenefits,
  },
  {
    id: "agents",
    title: "For Agents",
    icon: BarChart3,
    contentComponent: AgentBenefits,
  },
];

export default function BenefitsSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container px-4 md:px-6">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Key Benefits Tailored for Each Partner Type
          </h2>
          <p className="text-lg text-gray-700">
            Our auction marketplace offers unique advantages for suppliers, buyers, and agents.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <ReusableTabs
            tabsData={benefitsTabsData}
            defaultTabId="suppliers"
            tabContentClassName="mt-0" // Ensures no extra margin from ReusableTabs itself
            underlineColor="bg-[#004235]"
            activeTabTextColor="text-[#004235]"
            inactiveTabTextColor="text-[#004235]"
            activeTabBgColor="bg-white"
            inactiveTabBgColor="bg-[#f3f4f6]"
            hoverTabBgColor="hover:bg-gray-200"
          />
        </div>
      </div>
    </section>
  );
}
