import type React from "react"
import { <PERSON>, Clock, FileCheck } from "lucide-react"
import BenefitCard from "@/components/finance-payments/escrow-payment/benefit-card"

function Globe(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="10" />
      <line x1="2" x2="22" y1="12" y2="12" />
      <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
    </svg>
  )
}

export default function EscrowBenefitsSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">StreamLnk Escrow Benefits</h2>
          <p className="text-gray-600 max-w-3xl">
            Our escrow system provides comprehensive benefits to ensure secure and transparent transactions.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <BenefitCard
            icon={<Shield className="h-8 w-8 text-[#028475]" />}
            title="Risk Mitigation"
            description="Lower risk of fraud, payment delays, or non-performance"
          />
          <BenefitCard
            icon={<Clock className="h-8 w-8 text-[#028475]" />}
            title="Audit Trail"
            description="Timestamped activity logs, document uploads, and status confirmations"
          />
          <BenefitCard
            icon={<Globe className="h-8 w-8 text-[#028475]" />}
            title="Global Support"
            description="Works across regions, Incoterms, and currencies"
          />
          <BenefitCard
            icon={<FileCheck className="h-8 w-8 text-[#028475]" />}
            title="Integrated Workflow"
            description="Escrow status updates are shown inside MyStreamLnk and E-Stream portals"
          />
        </div>
      </div>
    </section>
  )
}