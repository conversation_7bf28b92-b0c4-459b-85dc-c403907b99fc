"use client"

import Image from "next/image"
import Link from "next/link"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { MainNav } from "@/components/main-nav"
import { BottomFooter } from "@/components/bottom-footer"
import { Headphones, Globe, Package, Truck, Briefcase, Users, Database, CheckCircle } from "lucide-react"
import PortalsSection from "@/components/portals-section"

export default function BusinessAccountPage() {
  const [activeTab, setActiveTab] = useState("streamfreight")

  const portalData = {
    streamfreight: {
      title: "STREAMFREIGHT",
      description: "For Truckers and Freight Forwarders: Access STREAMFREIGHT to find and bid on available shipments, manage your compliance documents, upload invoices, and track your earnings seamlessly within the StreamLnk network.",
      icon: <Truck className="h-12 w-12 text-[#00A991]" />
    },
    streamglobe: {
      title: "STREAMGLOBE",
      description: "For Global Sea Freight Carriers: STREAMGLOBE is your integration point with StreamLnk. Manage booking requests, sync vessel schedules via API, exchange shipping documents, and provide real-time tracking updates for seamless global operations.",
      icon: <Globe className="h-12 w-12 text-[#00A991]" />
    },
    "streamglobe-plus": {
      title: "STREAMGLOBE+",
      description: "For Customs Clearance Agents: Log in to STREAMGLOBE+ to manage assigned shipments, securely access documentation, submit declarations, update clearance statuses for customer visibility, and handle invoicing for your services.",
      icon: <Database className="h-12 w-12 text-[#00A991]" />
    },
    "e-stream": {
      title: "E-STREAM",
      description: "For Energy & Industrial Material Suppliers: Use E-STREAM to register, list your products with AI-driven readiness checks, manage compliance documentation, respond to quotes, and coordinate logistics for global fulfillment through StreamLnk.",
      icon: <Package className="h-12 w-12 text-[#00A991]" />
    },
    "mystreamlnk-plus": {
      title: "MySTREAMLNK+",
      description: "For Independent Agents & Assigned Distributors: MySTREAMLNK+ is your dedicated portal to manage customer portfolios, generate quotes using real-time data, place orders, track commissions, and grow your business with StreamLnk.",
      icon: <Briefcase className="h-12 w-12 text-[#00A991]" />
    },
    mystreamlnk: {
      title: "MySTREAMLNK",
      description: "For Customers: Access MySTREAMLNK to browse our product catalog (including supplier and brand info), request intelligent quotes, place orders, gain real-time AI-powered visibility into your shipments, and manage your account documents.",
      icon: <Users className="h-12 w-12 text-[#00A991]" />
    },
    streampak: {
      title: "STREAMPAK",
      description: "For Packaging & 3PL Warehouse Partners: STREAMPAK centralizes your finishing operations. Manage packaging and storage job assignments, update inventory status, ensure document compliance, and submit invoices for services rendered within the StreamLnk supply chain.",
      icon: <Package className="h-12 w-12 text-[#00A991]" />
    },
    "streamresources-plus": {
      title: "STREAMRESOURCES+",
      description: "For Resource Providers: STREAMRESOURCES+ helps you manage your resources, track utilization, and coordinate with partners in the StreamLnk ecosystem.",
      icon: <Package className="h-12 w-12 text-[#00A991]" />
    }
  }

  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      {/* Hero Section */}
      <section className="bg-[#F2F2F2] text-[#004235] py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl">
            <h1 className="text-4xl font-bold mb-4">StreamLnk Portals</h1>
            <p className="text-xl mb-8">Access specialized platforms for your business role in our ecosystem</p>
            <div className="w-24 h-1 bg-[#00A991]"></div>
          </div>
        </div>
      </section>

      {/* Portal Benefits Section */}
      <section className="py-16 bg-white border-t border-gray-200">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
            {/* Benefit 1 */}
            <div className="flex flex-col items-start">
              <div className="flex items-center mb-4">
                <Image 
                  src="/images/icons/preferential-rates.svg" 
                  alt="Specialized Access" 
                  width={48} 
                  height={48}
                />
                <h3 className="text-lg font-semibold ml-4">Specialized Access</h3>
              </div>
              <p className="text-gray-600">
                Each portal is tailored to your specific role in the supply chain, with customized tools and interfaces.
              </p>
            </div>

            {/* Benefit 2 */}
            <div className="flex flex-col items-start">
              <div className="flex items-center mb-4">
                <Image 
                  src="/images/icons/support.svg" 
                  alt="Seamless Integration" 
                  width={48} 
                  height={48}
                />
                <h3 className="text-lg font-semibold ml-4">Seamless Integration</h3>
              </div>
              <p className="text-gray-600">
                All portals connect to the StreamLnk ecosystem, ensuring smooth data flow and real-time visibility.
              </p>
            </div>

            {/* Benefit 3 */}
            <div className="flex flex-col items-start">
              <div className="flex items-center mb-4">
                <Image 
                  src="/images/icons/world-class.svg" 
                  alt="Intelligent Tools" 
                  width={48} 
                  height={48}
                />
                <h3 className="text-lg font-semibold ml-4">Intelligent Tools</h3>
              </div>
              <p className="text-gray-600">
                AI-powered features help optimize your operations, from document processing to predictive analytics.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* StreamLnk Portals Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">Find Your Dedicated Portal</h2>
          <p className="text-xl text-gray-600 mb-8">Select the portal that matches your role in the StreamLnk ecosystem</p>
          <PortalsSection />
        </div>
      </section>



      <BottomFooter />
    </div>
  )
}