import { CheckCircle, Globe, Package, Shield, Server } from "lucide-react"

const categories = [
  {
    title: "Polymers",
    description: "PET, HDPE, LDPE, PP, and more",
    icon: <Package className="h-12 w-12 text-[#028475]" />,
  },
  {
    title: "Industrial Chemicals",
    description: "Solvents, stabilizers, catalysts",
    icon: <Server className="h-12 w-12 text-[#028475]" />,
  },
  {
    title: "Additives",
    description: "Anti-stats, UV inhibitors, flame retardants",
    icon: <Shield className="h-12 w-12 text-[#028475]" />,
  },
  {
    title: "Processing Aids",
    description: "Masterbatches and manufacturing aids",
    icon: <Globe className="h-12 w-12 text-[#028475]" />,
  },
]

const productFeatures = [
  "Full technical specs (HS Code, CAS number, grade, MFI)",
  "Safety & compliance document upload (MSDS, REACH, FDA, ISO)",
  "Country of origin, available Incoterms (CIF, EXW, DDP, CFR, etc.)",
  "Packaging type and lead time",
  "Region-specific availability filters",
]

export function ProductCategoriesSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-[#004235] mb-12 text-center">What Can You Sell on E-Stream?</h2>
        <p className="text-lg text-center max-w-3xl mx-auto mb-12">
          We support a wide range of product types essential to modern manufacturing and trade. List your polymers,
          chemicals, offgrades, or energy-based commodities.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {categories.map((category, index) => (
            <div key={index} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
              <div className="mb-4">{category.icon}</div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">{category.title}</h3>
              <p className="text-gray-700">{category.description}</p>
            </div>
          ))}
        </div>

        <div className="mt-12 bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold text-[#004235] mb-4">Every product listing allows:</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {productFeatures.map((feature, index) => (
              <div key={index} className="flex items-start gap-2">
                <CheckCircle className="h-5 w-5 text-[#028475] flex-shrink-0 mt-1" />
                <p className="text-gray-700">{feature}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
