"use client"

import { Globe, CreditCard, Truck, ShieldCheck, BarChart3 } from "lucide-react";

export default function SolutionsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-10 text-center">
            Powering Efficiency & Transparency Across the Energy Value Chain
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamLnk's Tailored Solutions for the Energy Sector
          </p>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamLnk provides a secure, data-driven platform designed to address the specific needs of participants in the oil & gas, petrochemical feedstock, and renewable energy sectors:
          </p>

          <div className="space-y-8">
            {/* E-Stream for Energy Product Suppliers */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Globe className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">E-Stream for Energy Product Suppliers</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>List crude oil grades, refined products, LNG, LPG, petrochemical feedstocks, renewable energy credits (RECs), carbon offsets, and materials for renewable energy infrastructure (e.g., specialized polymers for turbine blades, metals for solar panels).</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Manage availability, set pricing (spot or contract-based indications), and utilize StreamIndex™ Energy Pricing Benchmarks for market context.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Securely share detailed specifications and compliance documentation (e.g., certificates of quality, origin).</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* MyStreamLnk for Energy Buyers */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <CreditCard className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">MyStreamLnk for Energy Buyers</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Source diverse energy products and renewable energy materials from a global network of verified suppliers.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Submit RFQs for bulk energy purchases or long-term supply contracts.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Utilize StreamIndex™ to benchmark offers and track market price trends.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Manage orders with integrated logistics (tanker chartering via StreamGlobe, pipeline/rail via StreamFreight) and secure payment options (Escrow for high-value trades).</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Specialized Logistics for Energy */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Truck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Specialized Logistics for Energy</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Coordination with vessel owners, pipeline operators, and specialized carriers for bulk liquid, gas, and oversized project cargo.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Real-time tracking of energy shipments and vessel ETAs.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Automated documentation for bills of lading, charter parties, and customs declarations.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Compliance & ESG for Energy */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <ShieldCheck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Compliance & ESG for Energy</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Tools to manage compliance with maritime regulations (IMO 2020), environmental standards, and safety protocols.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Features to support the sourcing and traceability of sustainable aviation fuels (SAF), renewable diesel, and other green energy products.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Track and report carbon intensity of shipments and sourced energy products (ESG Impact Center).</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* StreamResources+ for Energy Market Intelligence */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <BarChart3 className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">StreamResources+ for Energy Market Intelligence</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Access detailed StreamIndex™ Energy Price Forecasts, supply/demand balances, and geopolitical risk analysis impacting energy markets.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Utilize iScore™ to assess the reliability of energy suppliers and logistics partners.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Track trends in renewable energy project development and material sourcing.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>
      </section>
  );
}