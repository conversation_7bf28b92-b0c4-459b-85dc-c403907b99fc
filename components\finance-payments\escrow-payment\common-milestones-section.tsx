import { File<PERSON><PERSON><PERSON>, <PERSON>, Building2, CheckCircle2 } from "lucide-react"
import MilestoneCard from "@/components/finance-payments/escrow-payment/milestone-card"

export default function CommonMilestonesSection() {
  return (
    <section className="py-16 md:py-24 bg-[#f3f4f6]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Common Milestones Supported</h2>
          <p className="text-gray-600 max-w-3xl">
            Buyers and sellers can define milestones flexibly at the RFQ or contract stage.
          </p>
        </div>

        <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-5">
          <MilestoneCard
            icon={<FileCheck className="h-6 w-6 text-[#028475]" />}
            title="Order confirmation"
            description="Initial milestone when the order is confirmed by both parties"
          />
          <MilestoneCard
            icon={<Clock className="h-6 w-6 text-[#028475]" />}
            title="Shipment dispatch"
            description="When goods are dispatched from the supplier's facility"
          />
          <MilestoneCard
            icon={<Building2 className="h-6 w-6 text-[#028475]" />}
            title="Customs clearance"
            description="When goods have cleared customs in the destination country"
          />
          <MilestoneCard
            icon={<CheckCircle2 className="h-6 w-6 text-[#028475]" />}
            title="Delivery received"
            description="When buyer confirms receipt of the goods"
          />
          <MilestoneCard
            icon={<FileCheck className="h-6 w-6 text-[#028475]" />}
            title="Document package approved"
            description="When all required documentation is approved (B/L, COA, invoice)"
          />
        </div>
      </div>
    </section>
  )
}