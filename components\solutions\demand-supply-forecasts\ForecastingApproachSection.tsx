"use client"

import { Line<PERSON><PERSON>, Bar<PERSON>hart3, Truck, ExternalLink } from "lucide-react"

export default function ForecastingApproachSection() {
  const forecastingCategories = [
    {
      title: "Buyer Demand Forecasting",
      icon: <BarChart3 className="h-8 w-8 text-[#028475]" />,
      features: [
        "Analyzes aggregated RFQ volumes, product search trends, auction participation intensity, and order conversion rates by product category, grade, and region.",
        "Identifies emerging demand hotspots and predicts potential shifts in buyer interest.",
        "Provides short-term (e.g., next 30-90 days) and medium-term (e.g., next 6 months) demand outlooks."
      ]
    },
    {
      title: "Supplier Inventory & Availability Forecasting",
      icon: <LineChart className="h-8 w-8 text-[#028475]" />,
      features: [
        "Monitors listed inventory levels, production lead times, and supplier responsiveness on E-Stream.",
        "Predicts potential supply constraints or surplus situations for specific materials in key regions.",
        "Analyzes supplier auction activity as an indicator of inventory pressure."
      ]
    },
    {
      title: "Price Trend & Volatility Forecasting",
      icon: <ExternalLink className="h-8 w-8 text-[#028475]" />,
      features: [
        "Extrapolates future price movements based on current StreamIndex™ trends, demand/supply imbalances, and external market factors.",
        "Alerts users to potential significant price volatility."
      ]
    },
    {
      title: "Logistics Bottleneck Prediction",
      icon: <Truck className="h-8 w-8 text-[#028475]" />,
      features: [
        "Analyzes data from StreamFreight and StreamGlobe to forecast potential port congestion, carrier capacity issues, or regional logistics disruptions that could impact supply.",
        "Impact of External Factors (Future Enhancement): Integrating news feeds, weather data, and geopolitical risk indices to further refine forecast accuracy."
      ]
    }
  ]

  return (
    <section className="py-16 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            See a Clearer Future: How StreamLnk Forecasts Demand & Supply
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamLnk's StreamResources+ division leverages the vast, real-time data flowing through our entire ecosystem (E-Stream, MyStreamLnk, Auction Hub, logistics portals) and combines it with advanced AI/ML models to provide sophisticated demand and supply forecasts:
          </p>
          
          <div className="grid md:grid-cols-2 gap-8 mt-10">
            {forecastingCategories.map((category, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-sm">
                <div className="flex items-center mb-4">
                  {category.icon}
                  <h3 className="text-xl font-semibold text-[#004235] ml-3">{category.title}</h3>
                </div>
                <ul className="space-y-3">
                  {category.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <span className="text-[#028475] mr-2 font-bold">•</span>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}