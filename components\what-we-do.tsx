"use client"

import { useState } from "react"
import Image from "next/image"
import { ChevronRight, DollarSign, Eye, BarChart3, Search, Clock, Wrench } from "lucide-react"
import Link from "next/link"

// Define the tab data structure
interface TabData {
  id: string
  title: string
  content: {
    description: string
    featured: Array<{
      id: string
      title: string
      description: string
      image: string
    }>
  }
}

// Tab data
const tabs: TabData[] = [
  {
    id: "cost-optimization",
    title: "Smart Cost Optimization",
    content: {
      description: "Empower your business with intelligent technology that minimizes procurement and logistics costs through dynamic pricing, data-driven transportation solutions, and real-time auction features.",
      featured: [
        {
          id: "dynamic-pricing",
          title: "Dynamic Pricing Engine",
          description: "Our AI-powered pricing engine optimizes costs in real-time based on market conditions, supply availability, and demand patterns.",
          image: "/images/What we do/Dynamic Pricing Engine.webp"
        },
        {
          id: "logistics-optimization",
          title: "Logistics Cost Optimization",
          description: "Data-driven transportation solutions that reduce shipping costs while maintaining reliability and performance.",
          image: "/images/What we do/Logistics Cost Optimization.webp"
        }
      ]
    }
  },
  {
    id: "supply-chain-transparency",
    title: "Complete Supply Chain Transparency",
    content: {
      description: "Achieve full visibility into your supply chain with real-time tracking, data insights, and digital monitoring—ensuring control and transparency from start to finish.",
      featured: [
        {
          id: "real-time-tracking",
          title: "Real-Time Tracking Dashboard",
          description: "Monitor your entire supply chain with comprehensive visibility tools that provide instant status updates and alerts.",
          image: "/images/What we do/Real-Time Tracking Dashboard.webp"
        },
        {
          id: "digital-monitoring",
          title: "Digital Monitoring System",
          description: "Advanced monitoring tools that provide complete transparency across all stages of your energy supply chain.",
          image: "/images/What we do/Digital Monitoring System.webp"
        }
      ]
    }
  },
  {
    id: "data-insights",
    title: "Real-Time, Data-Driven Insights",
    content: {
      description: "Leverage live, data-driven insights and analytics to make informed decisions quickly, assess supply chain risks, and detect fraud, optimizing every aspect of your energy trading and procurement process.",
      featured: [
        {
          id: "analytics-platform",
          title: "Advanced Analytics Platform",
          description: "Powerful analytics tools that transform complex data into actionable insights for better decision-making.",
          image: "/images/What we do/Advanced Analytics Platform.webp"
        },
        {
          id: "risk-detection",
          title: "Risk & Fraud Detection",
          description: "AI-powered systems that identify potential risks and fraudulent activities before they impact your business.",
          image: "/images/What we do/Risk & Fraud Detection.webp"
        }
      ]
    }
  },
  {
    id: "digital-sourcing",
    title: "Effortless Digital Sourcing",
    content: {
      description: "Access an expansive, verified network of energy suppliers through an intuitive, AI-powered platform that makes finding the right products faster, easier, and more reliable.",
      featured: [
        {
          id: "supplier-network",
          title: "Verified Supplier Network",
          description: "Connect with pre-vetted, reliable energy suppliers through our comprehensive digital marketplace.",
          image: "/images/What we do/Verified Supplier Network.webp"
        },
        {
          id: "ai-matching",
          title: "AI-Powered Product Matching",
          description: "Intelligent algorithms that match your specific requirements with the ideal products and suppliers.",
          image: "/images/What we do/AI-Powered Product Matching.webp"
        }
      ]
    }
  },
  {
    id: "real-time-auctions",
    title: "Real-Time Auctions",
    content: {
      description: "Engage in live, dynamic auctions that allow for competitive bidding and real-time decision-making, optimizing pricing and procurement processes.",
      featured: [
        {
          id: "auction-platform",
          title: "Dynamic Auction Platform",
          description: "Participate in real-time bidding events that create fair market pricing and efficient transactions.",
          image: "/images/What we do/Dynamic Auction Platform.webp"
        },
        {
          id: "bidding-tools",
          title: "Intelligent Bidding Tools",
          description: "Advanced tools that help you make strategic bidding decisions based on market data and historical patterns.",
          image: "/images/What we do/Intelligent Bidding Tools.webp"
        }
      ]
    }
  },
  {
    id: "technical-services",
    title: "Comprehensive Technical Services",
    content: {
      description: "Benefit from advanced technical services, including AI-driven analytics, custom reporting, and digital tools that help you optimize every stage of your energy trade—from procurement to logistics.",
      featured: [
        {
          id: "custom-reporting",
          title: "Custom Reporting Solutions",
          description: "Tailored reporting tools that provide exactly the insights you need for your specific business requirements.",
          image: "/images/What we do/Custom Reporting Solutions.webp"
        },
        {
          id: "optimization-tools",
          title: "Trade Optimization Tools",
          description: "Specialized digital tools that enhance efficiency at every stage of the energy trading process.",
          image: "/images/What we do/Trade Optimization Tools.webp"
        }
      ]
    }
  }
]

export function WhatWeDo() {
  // State to track the active tab
  const [activeTab, setActiveTab] = useState(tabs[0].id)

  // Get the active tab data
  const activeTabData = tabs.find(tab => tab.id === activeTab) || tabs[0]

  return (
    <section className="py-16 md:py-24 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="mb-12">
          <div className="flex items-center mb-6">
            <div className="w-12 h-1 bg-[#18b793] mr-3"></div>
            <h2 className="text-3xl font-bold text-[#004235]">What we do</h2>
          </div>
          <p className="text-gray-700 mb-4 max-w-3xl">
            We develop and deploy the most advanced technologies to serve energy and industrial companies looking for more efficient, more reliable and cleaner solutions. Our diverse portfolio of technologies and solutions are transforming how industry works today and in the future.
          </p>
        </div>

        <div className="flex flex-col md:flex-row gap-0">
          {/* Wrapper with fixed height */}
          <div className="md:flex md:h-[700px]">
            {/* Left column - Tabs */}
            <div className="md:w-1/3 lg:w-1/4 flex">
              <div className="bg-gray-100 flex-1 flex flex-col">
                {tabs.map((tab, index) => (
                  <div key={tab.id}>
                    <button
                      onClick={() => setActiveTab(tab.id)}
                      className={`block w-full text-left py-6 px-6 transition-colors relative ${activeTab === tab.id ? 'bg-white font-medium text-[#004235]' : 'text-gray-600 hover:bg-gray-50'}`}
                    >
                      {activeTab === tab.id && <div className="absolute left-0 top-0 bottom-0 w-1.5 bg-[#18b793]"></div>}
                      <div className="flex items-center">
                        <div className="w-8 h-8 mr-3 flex-shrink-0 flex items-center justify-center">
                          {tab.id === "cost-optimization" && <DollarSign className="h-5 w-5 text-[#18b793]" />}
                          {tab.id === "supply-chain-transparency" && <Eye className="h-5 w-5 text-[#18b793]" />}
                          {tab.id === "data-insights" && <BarChart3 className="h-5 w-5 text-[#18b793]" />}
                          {tab.id === "digital-sourcing" && <Search className="h-5 w-5 text-[#18b793]" />}
                          {tab.id === "real-time-auctions" && <Clock className="h-5 w-5 text-[#18b793]" />}
                          {tab.id === "technical-services" && <Wrench className="h-5 w-5 text-[#18b793]" />}
                        </div>
                        {tab.title}
                      </div>
                    </button>
                  </div>
                ))}
                <div className="flex-grow bg-gray-100"></div>
              </div>
            </div>

            {/* Right column - Content */}
            <div className="md:w-2/3 lg:w-3/4 bg-white p-8 overflow-y-auto">
              <p className="text-gray-700 mb-8">
                {activeTabData.content.description}
              </p>

              {/* Featured section */}
              <div className="mb-6">
                <h3 className="text-xl font-medium text-[#004235] mb-6 flex items-center">
                  <div className="w-24 h-0.5 bg-[#18b793] mr-3"></div>
                  Featured
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {activeTabData.content.featured.map(item => (
                    <div key={item.id} className="border border-gray-200 overflow-hidden">
                      <div className="relative h-48">
                        <Image
                          src={item.image}
                          alt={item.title}
                          fill
                          className="object-cover"
                          onError={(e) => {
                            // Fallback for missing images
                            const target = e.target as HTMLImageElement;
                            target.src = "/images/What we do/Advanced Analytics Platform.webp";
                          }}
                        />
                      </div>
                      <div className="p-6">
                        <h4 className="text-lg font-medium text-[#004235] mb-2">{item.title}</h4>
                        <p className="text-gray-600 text-sm mb-4">{item.description}</p>
                        <Link href="#" className="text-[#18b793] font-medium flex items-center text-sm">
                          READ MORE <ChevronRight className="h-4 w-4 ml-1" />
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
