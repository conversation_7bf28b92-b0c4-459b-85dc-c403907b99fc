import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Navigate Global Trade with Unwavering Confidence: StreamLnk's Risk & Compliance Framework
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              In the complex world of international industrial trade, managing risk and ensuring compliance are paramount. StreamLnk embeds robust security, verification, and regulatory adherence into every aspect of its platform.
            </p>
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white px-6"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                DISCOVER OUR COMPREHENSIVE COMPLIANCE SOLUTIONS – REQUEST DEMO
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/solutions/risk-compliance/hero-placeholder.svg" // Placeholder image path
              alt="Risk & Compliance Framework"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
}