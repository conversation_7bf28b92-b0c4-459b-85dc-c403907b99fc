import {
  Body,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
  Row,
  Column,
} from "@react-email/components"
import { Tailwind } from "@react-email/tailwind"

interface ConfirmationEmailProps {
  confirmationUrl: string
  email: string
  portal?: string
}

export function ConfirmationEmail({ confirmationUrl, email, portal }: ConfirmationEmailProps) {
  const currentYear = new Date().getFullYear()
  
  return (
    <Html>
      <Head>
        <title>Confirm your StreamLnk account</title>
        <style>
          {`
            body { 
              font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; 
              line-height: 1.6; 
              color: #4a4a4a; 
              margin: 0;
              padding: 0;
              background-color: #f9f9f9;
            }
          `}
        </style>
      </Head>
      <Preview>Confirm your StreamLnk account</Preview>
      <Body style={{
        fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif",
        lineHeight: 1.6,
        color: "#4a4a4a",
        margin: 0,
        padding: 0,
        backgroundColor: "#f9f9f9"
      }}>
        <Container style={{
          maxWidth: "600px",
          margin: "0 auto",
          backgroundColor: "#ffffff",
          borderRadius: "8px",
          overflow: "hidden",
          boxShadow: "0 1px 3px rgba(0,0,0,0.1)"
        }}>
          {/* Header with gradient background */}
          <Section style={{
            background: "linear-gradient(to right, #004235, #07BC94)",
            padding: "30px 20px",
            textAlign: "center",
            color: "white"
          }}>
            <Img
              src="https://calyairmrgtnjwvixvjn.supabase.co/storage/v1/object/sign/streamlnk-assets/svg%20logo/wihte%20logo%20for%20dark%20screen.svg?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1cmwiOiJzdHJlYW1sbmstYXNzZXRzL3N2ZyBsb2dvL3dpaHRlIGxvZ28gZm9yIGRhcmsgc2NyZWVuLnN2ZyIsImlhdCI6MTc0NDA3MzY2MywiZXhwIjoxNzc1NjA5NjYzfQ.N5m6lxVgRXqV9HA2fNjd1mkz4leh3-pMnCo4CuvP-hw"
              alt="StreamLnk Logo"
              width="200"
              height="auto"
              style={{
                display: "block",
                margin: "0 auto",
                maxWidth: "200px",
                width: "100%"
              }}
            />
          </Section>
          
          {/* Content section */}
          <Section style={{
            padding: "40px 30px",
            backgroundColor: "#ffffff"
          }}>
            <Text style={{ marginBottom: "20px" }}>
              Hello,
            </Text>
            
            <Text style={{ marginBottom: "20px" }}>
              Thank you for signing up with StreamLnk. We're thrilled to welcome you to our innovative platform for petrochemical commerce and logistics! {portal && `Your account is being created with your specified portal: ${portal}.`}
            </Text>
            
            {/* Info box */}
            <Section style={{
              backgroundColor: "#f9f9f9",
              borderLeft: "4px solid #07BC94",
              padding: "15px",
              margin: "20px 0"
            }}>
              <Text style={{ margin: "0 0 10px 0" }}>
                Please confirm your email address ({email}) to activate your account and get started.
              </Text>
            </Section>
            
            {/* Button */}
            <Section style={{ textAlign: "center", margin: "30px 0" }}>
              <Button
                href={confirmationUrl}
                style={{
                  background: "linear-gradient(to right, #004235, #07BC94)",
                  color: "#ffffff",
                  textDecoration: "none",
                  padding: "14px 30px",
                  borderRadius: "4px",
                  fontWeight: "bold",
                  display: "inline-block",
                  boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
                }}
              >
                Confirm My Email
              </Button>
            </Section>
            
            <Text style={{ fontSize: "14px", color: "#666", marginBottom: "10px" }}>
              If the button above doesn't work, you can copy and paste the following link into your browser:
            </Text>
            
            <Text style={{ fontSize: "12px", color: "#07BC94", marginBottom: "20px", wordBreak: "break-all" }}>
              <Link href={confirmationUrl} style={{ color: "#07BC94" }}>
                {confirmationUrl}
              </Link>
            </Text>
            
            {/* Divider */}
            <Hr style={{ borderTop: "1px solid #eaeaea", margin: "30px 0" }} />
            
            <Text style={{ marginBottom: "20px" }}>
              This link will expire in 24 hours. If you didn't create an account with StreamLnk, you can safely ignore this email.
            </Text>
            
            <Text style={{ marginBottom: "0" }}>
              Best regards,<br />The StreamLnk Team
            </Text>
          </Section>
          
          {/* Footer */}
          <Section style={{
            backgroundColor: "#f5f5f5",
            padding: "20px 30px",
            textAlign: "center",
            fontSize: "12px",
            color: "#666"
          }}>
            <Text style={{ margin: "0 0 10px 0" }}>© {currentYear} StreamLnk. All rights reserved.</Text>
            <Text style={{ margin: "0 0 10px 0" }}>123 Business Avenue, Suite 100, New York, NY 10001</Text>
            
            <Section style={{ margin: "20px 0 10px 0" }}>
              <Link href="https://linkedin.com/company/streamlnk" style={{ color: "#07BC94", margin: "0 10px", textDecoration: "none" }}>LinkedIn</Link> •
              <Link href="https://twitter.com/streamlnk" style={{ color: "#07BC94", margin: "0 10px", textDecoration: "none" }}>Twitter</Link> •
              <Link href="https://facebook.com/streamlnk" style={{ color: "#07BC94", margin: "0 10px", textDecoration: "none" }}>Facebook</Link>
            </Section>
            
            <Text style={{ margin: "10px 0 0 0" }}>This is an automated email, please do not reply.</Text>
          </Section>
        </Container>
      </Body>
    </Html>
  )
}

