"use client";

import { DatabaseZap, Cpu, Sigma, <PERSON>fresh<PERSON>w, LockKeyhole } from 'lucide-react';

const processSteps = [
  {
    icon: <DatabaseZap className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Comprehensive Data Ingestion",
    description: "StreamResources+ securely collects and anonymizes vast transactional, operational, and behavioral data from all StreamLnk portals."
  },
  {
    icon: <Cpu className="h-10 w-10 text-[#028475] mb-4" />,
    title: "AI-Powered Analysis & Normalization",
    description: "Advanced algorithms and machine learning models process, clean, and normalize this diverse data."
  },
  {
    icon: <Sigma className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Proprietary Index Calculation",
    description: "Sophisticated statistical methods and AI are used to calculate the various StreamIndex™ components, ensuring relevance and accuracy."
  },
  {
    icon: <RefreshCw className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Real-Time Updates",
    description: "Indices are continuously updated to reflect the latest market activities and conditions."
  },
  {
    icon: <LockKeyhole className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Secure Delivery",
    description: "StreamIndex™ insights are delivered to subscribers via interactive dashboards in StreamResources+, premium features in operational portals, DaaS APIs, and curated reports."
  }
];

export default function HowItWorksSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            From Ecosystem Data to Actionable Benchmarks: Our AI-Driven Process
          </h2>
          <p className="text-xl text-gray-700">
            How StreamIndex™ Works – The Technology Behind the Intelligence
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {processSteps.map((step, index) => (
            <div key={index} className="bg-[#F2F2F2] p-6 rounded-lg shadow-md flex flex-col items-center text-center hover:shadow-lg transition-shadow duration-300">
              {step.icon}
              <h3 className="font-semibold text-[#004235] text-lg mb-2">{step.title}</h3>
              <p className="text-gray-600 text-sm">{step.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}