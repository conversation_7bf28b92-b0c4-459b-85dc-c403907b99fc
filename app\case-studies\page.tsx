"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/case-studies/HeroSection";
import ImpactSection from "@/components/case-studies/ImpactSection";
import FilterSection from "@/components/case-studies/FilterSection";
import CaseStudyShowcaseSection from "@/components/case-studies/CaseStudyShowcaseSection";
import ShareStorySection from "@/components/case-studies/ShareStorySection";

export default function CaseStudiesPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <ImpactSection />
      <FilterSection />
      <CaseStudyShowcaseSection />
      <ShareStorySection />

      <BottomFooter />
    </div>
  );
}