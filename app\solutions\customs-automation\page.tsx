"use client"

import Link from "next/link"
import Image from "next/image"
import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, CheckCircle, ChevronRight, Globe, FileText, BarChart3, ShieldCheck, Clock, AlertTriangle, Layers } from "lucide-react"
import HeroSection from "@/components/solutions/customs-automation/HeroSection"
import ChallengesSection from "@/components/solutions/customs-automation/ChallengesSection"
import SolutionOverviewSection from "@/components/solutions/customs-automation/SolutionOverviewSection"
import WorkflowSection from "@/components/solutions/customs-automation/WorkflowSection"
import BenefitsSection from "@/components/solutions/customs-automation/BenefitsSection"

export default function CustomsAutomationPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <ChallengesSection />

      <SolutionOverviewSection />

      <WorkflowSection />

      <BenefitsSection />

      {/* Call to Action Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">Ready to Simplify Your Customs Process?</h2>
          <p className="text-lg text-gray-700 mb-8">
            Experience the efficiency and compliance of StreamLnk's Customs Automation solution.
          </p>
          <Button 
            className="bg-[#004235] hover:bg-[#028475] text-white px-8"
            size="lg"
            asChild
          >
            <Link href="/request-demo">
              REQUEST A DEMO
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </section>

      <MainFooter />
    </div>
  )
}