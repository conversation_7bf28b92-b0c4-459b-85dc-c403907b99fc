import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

export function CtaSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-[#004235]">
          Ready to Drive the Future of Industrial Logistics?
        </h2>
        <p className="text-xl max-w-3xl mx-auto mb-8 text-gray-700">
          Join StreamFreight today and become an essential part of the global supply chain, connecting with verified
          shipment opportunities while managing your business more efficiently.
        </p>
        <Button size="lg" className="bg-[#004235] text-white hover:bg-[#028475] transition-colors" asChild>
          <Link href="/signup?portal=stream-freight">Apply Now to Become a Verified StreamFreight Partner</Link>
        </Button>
        <p className="text-sm mt-4 text-gray-600">Portal Access: StreamFreight</p>
      </div>
    </section>
  )
}
