// components/portal-features/mobile/InTheMeantimeSection.tsx
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

export default function InTheMeantimeSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">Access StreamLnk via Mobile Web</h2>
          <h3 className="text-xl font-semibold text-[#028475] mb-6">Responsive Design for Access Today</h3>
          <p className="text-xl text-gray-700 mb-8">
            While dedicated apps are developing, StreamLnk portals are mobile-responsive for access on your smartphone or tablet.
          </p>
          <Button 
            className="bg-[#004235] hover:bg-[#028475] text-white"
            size="lg"
            asChild
          >
            <Link href="/login"> {/* Assuming a general login page */}
              LOGIN TO PORTAL
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}