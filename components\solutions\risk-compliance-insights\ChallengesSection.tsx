import { AlertTriangle } from "lucide-react";

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Are Unseen Risks Threatening Your Supply Chain?
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            Operating in the global industrial materials market inherently involves navigating a complex web of potential risks:
          </p>

          <div className="space-y-4 max-w-3xl mx-auto">
            {[ "Counterparty Risk: Uncertainty about the reliability, financial stability, and compliance history of new suppliers, buyers, or service providers.", "Operational Risk: Potential for shipment delays, quality issues, inventory discrepancies, or disruptions at ports or with carriers.", "Compliance Risk: Difficulty keeping up with diverse and changing international trade regulations, customs requirements, and product certifications.", "Financial Risk: Exposure to payment defaults, FX volatility, or fraud in cross-border transactions.", "Geopolitical & Market Risk: Impact of regional instability, trade policy changes, or sudden market shocks on supply and pricing.", "Lack of Centralized Risk Data: Difficulty in accessing consolidated, real-time information to assess overall risk exposure." ].map((challenge, index) => (
              <div key={index} className="flex items-start p-4 bg-gray-50 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
                <p className="text-gray-800">{challenge}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}