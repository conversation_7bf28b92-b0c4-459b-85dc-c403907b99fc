import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

export default function GettingStartedSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Getting Started with StreamLnk Integrations
          </h2>
          <div className="w-20 h-1 bg-[#028475] mx-auto mb-8"></div>
          <p className="text-lg text-gray-700 mb-12 text-center">
            Our team and comprehensive API documentation make integration straightforward.
          </p>

          <div className="space-y-6 mb-12">
            <div className="flex items-start bg-white p-6 rounded-lg shadow-sm">
              <div className="bg-[#004235] text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 flex-shrink-0">
                1
              </div>
              <div>
                <h4 className="font-semibold text-[#004235] mb-2">Assess Your Needs</h4>
                <p className="text-gray-700">Identify the key data points and workflows you want to connect between StreamLnk and your systems.</p>
              </div>
            </div>

            <div className="flex items-start bg-white p-6 rounded-lg shadow-sm">
              <div className="bg-[#004235] text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 flex-shrink-0">
                2
              </div>
              <div>
                <h4 className="font-semibold text-[#004235] mb-2">Explore Our Developer Portal</h4>
                <p className="text-gray-700">Access detailed API documentation, SDKs (if available), and sandbox environments.</p>
              </div>
            </div>

            <div className="flex items-start bg-white p-6 rounded-lg shadow-sm">
              <div className="bg-[#004235] text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 flex-shrink-0">
                3
              </div>
              <div>
                <h4 className="font-semibold text-[#004235] mb-2">Consult with Our Integration Specialists</h4>
                <p className="text-gray-700">Our team can help you design the optimal integration architecture.</p>
              </div>
            </div>

            <div className="flex items-start bg-white p-6 rounded-lg shadow-sm">
              <div className="bg-[#004235] text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 flex-shrink-0">
                4
              </div>
              <div>
                <h4 className="font-semibold text-[#004235] mb-2">Develop & Test</h4>
                <p className="text-gray-700">Utilize our APIs to build and test your connections.</p>
              </div>
            </div>

            <div className="flex items-start bg-white p-6 rounded-lg shadow-sm">
              <div className="bg-[#004235] text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 flex-shrink-0">
                5
              </div>
              <div>
                <h4 className="font-semibold text-[#004235] mb-2">Go Live & Optimize</h4>
                <p className="text-gray-700">Deploy your integration and continue to refine based on performance and feedback.</p>
              </div>
            </div>
          </div>

          <div className="text-center">
            <p className="text-xl text-gray-700 mb-8">
              Ready to streamline your operations with powerful integrations?
            </p>
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white"
              size="lg"
              asChild
            >
              <Link href="/contact-us"> {/* Assuming this links to a contact page */}
                TALK TO AN EXPERT
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}