"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, BarChart3, Globe, ShieldCheck, Truck, CreditCard } from "lucide-react"

export default function CoreSolutionsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-10 text-center">
            Our Core Solution Areas
          </h2>
            
          <div className="space-y-8">
            {/* Sourcing & Procurement */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Globe className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Sourcing & Procurement</h3>
                  <p className="text-gray-700 mb-4">
                    Discover, connect with, and procure industrial materials from a global network of verified suppliers. Leverage AI-powered matching, real-time quoting, and market price benchmarks (StreamIndex™) to optimize costs and streamline your buying process.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button 
                      variant="outline" 
                      className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white"
                      asChild
                    >
                      <Link href="/solutions/sourcing-procurement">
                        Explore Sourcing & Procurement
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                    <Button 
                      variant="outline" 
                      className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white"
                      asChild
                    >
                      <Link href="/solutions/verified-suppliers">
                        Access Verified Suppliers
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Global Logistics & Fulfillment */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Truck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Global Logistics & Fulfillment</h3>
                  <p className="text-gray-700 mb-4">
                    Manage the physical movement of goods with end-to-end control. Coordinate multi-modal freight (land, sea), automate customs clearance, and integrate warehousing and packaging services through our network of vetted partners, all with real-time visibility.
                  </p>
                  <Button 
                    variant="outline" 
                    className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white"
                    asChild
                  >
                    <Link href="/solutions/global-logistics">
                      Explore Global Logistics Solutions
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
            
            {/* Trade Finance */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <CreditCard className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Trade Finance</h3>
                  <p className="text-gray-700 mb-4">
                    Simplify international transactions with integrated financial solutions. Access secure multi-currency payments, B2B Buy Now, Pay Later (BNPL) options, Escrow services, and automated AR/AP management to improve cash flow and reduce financial risk.
                  </p>
                  <Button 
                    variant="outline" 
                    className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white"
                    asChild
                  >
                    <Link href="/solutions/trade-finance">
                      Explore Trade Finance Solutions
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
            
            {/* Data Analytics & Insights */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <BarChart3 className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Data Analytics & Insights</h3>
                  <p className="text-gray-700 mb-4">
                    Turn data into a strategic asset. Access unparalleled market intelligence powered by our ecosystem data (StreamResources+), including real-time benchmarks (StreamIndex™), predictive forecasts, and data-driven insights into trends and opportunities.
                  </p>
                  <Button 
                    variant="outline" 
                    className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white"
                    asChild
                  >
                    <Link href="/solutions/data-analytics">
                      Explore Data Analytics Solutions
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
            
            {/* Risk Management & Compliance */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <ShieldCheck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Risk Management & Compliance</h3>
                  <p className="text-gray-700 mb-4">
                    Build trust and ensure security in every transaction. Our platform includes rigorous KYC/AML verification, automated document management, robust security protocols, and the iScore™ partner rating system to help you navigate the complexities of global trade safely and compliantly.
                  </p>
                  <Button 
                    variant="outline" 
                    className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white"
                    asChild
                  >
                    <Link href="/solutions/risk-compliance">
                      Explore Risk & Compliance Solutions
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>
      </section>
  )
}