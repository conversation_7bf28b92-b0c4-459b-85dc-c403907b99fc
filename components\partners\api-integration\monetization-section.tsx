import { DollarSign, BarChart4, PieChart, Globe } from "lucide-react"

export function MonetizationSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <div className="mb-12 text-center">
            <h2 className="text-3xl font-bold text-[#004235] mb-4">Monetization & Value Exchange Options</h2>
            <div className="w-20 h-1 bg-[#028475] mx-auto mb-6"></div>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              We believe in partnerships that create mutual benefit
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="bg-[#004235]/10 p-3 rounded-full mr-4">
                  <DollarSign className="h-6 w-6 text-[#004235]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Revenue Share Models</h3>
              </div>
              <p className="text-gray-700 mb-4">
                For premium data access or co-developed services, we offer flexible revenue sharing arrangements that
                benefit both parties.
              </p>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  Percentage-based commission on transactions
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  Tiered revenue sharing based on volume
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  Co-marketing and lead generation incentives
                </li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="bg-[#004235]/10 p-3 rounded-full mr-4">
                  <BarChart4 className="h-6 w-6 text-[#004235]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">API Call Volume Licensing</h3>
              </div>
              <p className="text-gray-700 mb-4">
                For high-volume data consumption, we provide scalable licensing models that grow with your needs.
              </p>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  Tiered pricing based on API call volume
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  Subscription-based access to premium endpoints
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  Volume discounts for long-term commitments
                </li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="bg-[#004235]/10 p-3 rounded-full mr-4">
                  <PieChart className="h-6 w-6 text-[#004235]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Custom Dashboard Development</h3>
              </div>
              <p className="text-gray-700 mb-4">
                Offer white-labeled analytics dashboards to your clients, powered by StreamLnk data.
              </p>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  White-labeled visualization solutions
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  Customizable reporting interfaces
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  Industry-specific analytics templates
                </li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="bg-[#004235]/10 p-3 rounded-full mr-4">
                  <Globe className="h-6 w-6 text-[#004235]" />
                </div>
                <h3 className="text-xl font-bold text-[#004235]">Regional Data Resale</h3>
              </div>
              <p className="text-gray-700 mb-4">
                Opportunities for joint marketing and resale of aggregated, anonymized data with regional focus.
              </p>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  Geographic-specific market insights
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  Industry trend reports for specific regions
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  Co-branded market intelligence products
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
