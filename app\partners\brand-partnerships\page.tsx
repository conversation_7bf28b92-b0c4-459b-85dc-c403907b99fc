import type React from "react"
import Image from "next/image"
import {
  ArrowRight,
  Handshake,
  Globe,
  BarChart3,
  Lightbulb,
  Users,
  Building2,
  Layers,
  Database,
  LineChart,
  CheckCircle2,
  MessageSquare,
  Send,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import PartnershipValue from "./components/partnership-value"
import PartnershipType from "./components/partnership-type"
import PartnerQuality from "./components/partner-quality"
import PartnerSpotlight from "./components/partner-spotlight"
import ContactForm from "./components/contact-form"

export default function BrandPartnershipsPage() {
  return (
    <main className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-[#F2F2F2] py-16 md:py-24 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="/placeholder.svg?height=800&width=1600"
            alt="Global brands connecting in a digital network"
            fill
            className="object-cover"
          />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <div className="inline-block rounded-lg bg-[#004235]/10 px-3 py-1 text-sm text-[#004235] mb-4">
              Global Brand Partnerships
            </div>
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235] mb-4">
              Partner with StreamLnk: Forge Strategic Alliances, Unlock Global Opportunities
            </h1>
            <p className="text-gray-600 md:text-xl mb-8">
              We are building a global ecosystem for industrial trade, and we believe in the power of partnership.
              Collaborate with StreamLnk to enhance your brand, access new markets, and co-innovate solutions for a
              smarter supply chain.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button className="bg-[#004235] hover:bg-[#004235]/90">
                Explore Partnership Opportunities <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button variant="outline" className="border-[#028475] text-[#028475]">
                Contact Our Partnerships Team
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Section 1: Why Partner with StreamLnk? The Value of Alliance */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
                Synergy in a Connected World: The StreamLnk Partnership Advantage
              </h2>
            </div>
            <div className="space-y-6 text-gray-600">
              <p>
                StreamLnk is more than a platform; it's a rapidly expanding global network of suppliers, buyers,
                logistics providers, financial institutions, and technology innovators, all focused on the industrial
                materials and energy sectors. We understand that the future of global trade will be built on
                collaboration and integrated solutions.
              </p>
              <p>Partnering with StreamLnk offers your brand a unique opportunity to:</p>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mt-8">
              <PartnershipValue
                icon={<Globe className="h-8 w-8 text-[#028475]" />}
                title="Access a Targeted Global Audience"
                description="Connect with thousands of verified B2B buyers, suppliers, and service providers actively engaged in industrial procurement and logistics."
              />

              <PartnershipValue
                icon={<Building2 className="h-8 w-8 text-[#028475]" />}
                title="Enhance Brand Visibility & Credibility"
                description="Align your brand with a leader in supply chain digitization, AI-driven trade, and sustainable commerce."
              />

              <PartnershipValue
                icon={<Lightbulb className="h-8 w-8 text-[#028475]" />}
                title="Co-Develop Innovative Solutions"
                description="Collaborate on integrated offerings that provide enhanced value to our mutual customers."
              />

              <PartnershipValue
                icon={<BarChart3 className="h-8 w-8 text-[#028475]" />}
                title="Drive New Revenue Streams"
                description="Explore co-marketing initiatives, referral programs, and integrated service models."
              />

              <PartnershipValue
                icon={<LineChart className="h-8 w-8 text-[#028475]" />}
                title="Gain Strategic Market Insights"
                description="Leverage our data capabilities (via StreamResources+) to understand market trends and identify new opportunities."
              />
            </div>

            <div className="mt-8 p-6 bg-white rounded-lg border border-[#f3f4f6]">
              <p className="text-gray-600 text-center">
                We are seeking strategic, like-minded organizations to join us in reshaping the landscape of global
                industrial trade.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Section 2: Types of Brand Partnerships We Offer */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
                Tailored Collaborations for Mutual Growth
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                StreamLnk offers a variety of partnership models designed to suit different organizational goals and
                capabilities.
              </p>
            </div>

            <div className="space-y-8">
              <PartnershipType
                icon={<Handshake className="h-8 w-8 text-[#028475]" />}
                title="Strategic Alliances with Industry Leaders"
                focus="Collaborations with major players in polymers, chemicals, energy, logistics, finance, and technology."
                examples={[
                  "Joint go-to-market strategies",
                  "Co-development of specialized industry solutions",
                  "Preferred partner integrations",
                  "Joint thought leadership initiatives",
                ]}
                benefit="Deep market penetration, enhanced brand leadership, access to StreamLnk's growing user base and data insights."
              />

              <PartnershipType
                icon={<NetworkIcon className="h-8 w-8 text-[#028475]" />}
                title="Technology & Integration Partnerships"
                focus="Collaborations with ERP providers, SCM software companies, freight visibility platforms (e.g., Project44), payment gateways, compliance tech firms, and AI/ML solution providers."
                examples={[
                  "API integrations to create seamless workflows for users",
                  "Co-marketing of integrated solutions",
                  'Development of certified "StreamLnk Ready" integrations',
                ]}
                benefit="Access to StreamLnk's transaction flow, expanded user base for their services, enhanced product offerings."
              />

              <PartnershipType
                icon={<Users className="h-8 w-8 text-[#028475]" />}
                title="Ecosystem & Service Partnerships"
                focus="Collaborations with complementary service providers such as inspection agencies, insurance companies, sustainability certifiers, trade associations, and market research firms."
                examples={[
                  "Listing in our StreamResources+ Partner Directory",
                  "Referral programs",
                  "Joint webinars or content creation",
                  "Co-sponsorship of industry events",
                ]}
                benefit="Targeted exposure to StreamLnk users, lead generation, brand association with an innovative platform."
              />

              <PartnershipType
                icon={<Layers className="h-8 w-8 text-[#028475]" />}
                title="Channel & Reseller Partnerships"
                focus="Collaborations with regional distributors, sales agencies, and consultants who can represent and sell StreamLnk services or onboard users in specific geographies or market segments."
                examples={[
                  "Official distributor agreements",
                  "Agent commission programs",
                  "Co-branded regional portals",
                ]}
                benefit="New revenue streams, access to a cutting-edge digital platform to offer their clients."
              />

              <PartnershipType
                icon={<Database className="h-8 w-8 text-[#028475]" />}
                title="Data & Insights Partnerships"
                focus="Collaborations with data providers, market analysts, and research institutions to enrich StreamLnk's data offerings or co-create new intelligence products."
                examples={[
                  "Licensing StreamLnk's anonymized data",
                  "Integrating third-party data feeds into StreamResources+",
                  "Joint market reports",
                ]}
                benefit="Access to unique industrial trade data, new product development opportunities, co-branding of insights."
              />
            </div>

            <div className="mt-12 relative h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-lg">
              <Image
                src="/placeholder.svg?height=400&width=800"
                alt="Partnership ecosystem infographic"
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end">
                <div className="p-6 text-white">
                  <h3 className="text-lg font-semibold mb-2">The StreamLnk Partnership Ecosystem</h3>
                  <p className="text-sm opacity-90">
                    Visualizing how different partnership types interconnect within our global network
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 3: What We Look For in a Brand Partner */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
                Building a Network of Excellence and Trust
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                StreamLnk is committed to building high-quality, mutually beneficial partnerships. We seek partners who
                share our:
              </p>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              <PartnerQuality
                icon={<Lightbulb className="h-8 w-8 text-[#028475]" />}
                title="Commitment to Innovation"
                description="A forward-thinking approach and a desire to leverage technology to solve industry challenges."
              />

              <PartnerQuality
                icon={<Users className="h-8 w-8 text-[#028475]" />}
                title="Focus on Customer Value"
                description="A dedication to providing exceptional service and tangible benefits to end-users."
              />

              <PartnerQuality
                icon={<Globe className="h-8 w-8 text-[#028475]" />}
                title="Global Perspective & Regional Expertise"
                description="Brands with established reach or deep understanding of specific markets."
              />

              <PartnerQuality
                icon={<CheckCircle2 className="h-8 w-8 text-[#028475]" />}
                title="Reputation for Excellence & Integrity"
                description="A strong track record and commitment to ethical business practices."
              />

              <PartnerQuality
                icon={<Handshake className="h-8 w-8 text-[#028475]" />}
                title="Strategic Alignment"
                description="A clear synergy between your offerings and the StreamLnk ecosystem, with the potential for genuine value co-creation."
              />

              <PartnerQuality
                icon={<MessageSquare className="h-8 w-8 text-[#028475]" />}
                title="Willingness to Collaborate"
                description="An open and proactive approach to working together on joint initiatives."
              />
            </div>
          </div>
        </div>
      </section>

      {/* Section 4: Success Stories & Current Partners */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Powering Progress, Together</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                StreamLnk is proud to collaborate with a growing network of leading organizations across the globe. Our
                partners leverage the StreamLnk platform to reach new industrial buyers, integrate cutting-edge
                logistics solutions, and gain unparalleled market intelligence.
              </p>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
              <PartnerSpotlight
                logo="/placeholder.svg?height=100&width=200"
                name="Major Logistics Partner"
                quote="Integrating with StreamLnk has streamlined our B2B freight operations and opened up a new channel of qualified industrial shippers."
              />

              <PartnerSpotlight
                logo="/placeholder.svg?height=100&width=200"
                name="Data Analytics Firm"
                quote="The depth of StreamLnk's trade data provides a unique foundation for building next-generation market intelligence tools."
              />
            </div>

            <div className="mt-8 text-center">
              <Button variant="outline" className="border-[#028475] text-[#028475]">
                Read Our Partnership Case Studies
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Section 5: How to Become a StreamLnk Brand Partner */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
                Let's Build the Future of Industrial Trade, Together
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                If you believe your organization aligns with StreamLnk's vision and can contribute to our growing
                ecosystem, we would be delighted to explore partnership opportunities.
              </p>
            </div>

            <div className="grid gap-8 md:grid-cols-3 mb-12">
              <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
                <CardContent className="pt-6 flex flex-col items-center text-center space-y-4">
                  <div className="p-3 rounded-full bg-[#004235]/10">
                    <Globe className="h-8 w-8 text-[#028475]" />
                  </div>
                  <h3 className="text-lg font-semibold text-[#004235]">Explore Our Ecosystem</h3>
                  <p className="text-gray-600">
                    Familiarize yourself with our various portals (E-Stream, MyStreamLnk, StreamGlobe, etc.) and our
                    data intelligence arm, StreamResources+.
                  </p>
                </CardContent>
              </Card>

              <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
                <CardContent className="pt-6 flex flex-col items-center text-center space-y-4">
                  <div className="p-3 rounded-full bg-[#004235]/10">
                    <Handshake className="h-8 w-8 text-[#028475]" />
                  </div>
                  <h3 className="text-lg font-semibold text-[#004235]">Identify Synergy</h3>
                  <p className="text-gray-600">
                    Consider how your products, services, or brand could complement or enhance the StreamLnk platform
                    and user experience.
                  </p>
                </CardContent>
              </Card>

              <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
                <CardContent className="pt-6 flex flex-col items-center text-center space-y-4">
                  <div className="p-3 rounded-full bg-[#004235]/10">
                    <Send className="h-8 w-8 text-[#028475]" />
                  </div>
                  <h3 className="text-lg font-semibold text-[#004235]">Reach Out</h3>
                  <p className="text-gray-600">
                    Contact our Global Partnerships team with your proposal or to discuss potential collaboration areas.
                  </p>
                </CardContent>
              </Card>
            </div>

            <div className="bg-white p-8 rounded-lg border border-[#f3f4f6] shadow-sm">
              <h3 className="text-xl font-semibold text-[#004235] mb-6 text-center">Submit a Partnership Proposal</h3>
              <ContactForm />
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}

function NetworkIcon(props: React.ComponentProps<"svg">) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect x="16" y="16" width="6" height="6" rx="1" />
      <rect x="2" y="16" width="6" height="6" rx="1" />
      <rect x="9" y="2" width="6" height="6" rx="1" />
      <path d="M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3" />
      <path d="M12 12V8" />
    </svg>
  )
}
