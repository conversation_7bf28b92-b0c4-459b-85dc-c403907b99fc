import Image from "next/image"
import {
  ArrowR<PERSON>,
  BarChart3,
  DollarSign,
  Globe,
  LineChart,
  Lock,
  FileCheck,
  Building2,
  Users,
  Wallet,
  Bell,
  ShieldCheck,
  Layers,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import DashboardFeature from "./components/dashboard-feature"
import SecurityFeature from "./components/security-feature"
import CurrencyCard from "./components/currency-card"
import IntegrationFeature from "./components/integration-feature"
import UseCaseCard from "./components/use-case-card"
import DashboardPreview from "./components/dashboard-preview"

export default function TreasuryDashboardPage() {
  return (
    <main className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-[#F2F2F2] py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
            <div className="flex flex-col justify-center space-y-4">
              <div className="inline-block rounded-lg bg-[#004235]/10 px-3 py-1 text-sm text-[#004235]">
                Financial Management
              </div>
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235]">
                Treasury & FX Dashboard Access
              </h1>
              <p className="text-gray-600 md:text-xl">
                Get real-time insights into payment activity, currency positions, and liquidity status—so you can manage
                working capital and reduce FX risk with precision.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button className="bg-[#004235] hover:bg-[#004235]/90">
                  Access Dashboard <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                <Button variant="outline" className="border-[#028475] text-[#028475]">
                  Request Demo
                </Button>
              </div>
            </div>
            <div className="flex justify-center">
              <div className="relative w-full max-w-[500px] h-[300px] md:h-[400px]">
                <Image
                  src="/placeholder.svg?height=400&width=500"
                  alt="Treasury dashboard illustration"
                  fill
                  className="object-contain"
                  priority
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Dashboard Preview Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Comprehensive Financial Visibility
            </h2>
            <p className="text-gray-600 max-w-3xl">
              The StreamLnk Treasury & FX Dashboard provides a complete view of your global financial operations.
            </p>
          </div>

          <DashboardPreview />
        </div>
      </section>

      {/* Key Dashboard Capabilities Section */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Key Dashboard Capabilities</h2>
            <p className="text-gray-600 max-w-3xl">
              Our Treasury & FX Dashboard offers powerful tools to manage your global financial operations.
            </p>
          </div>

          <div className="grid gap-8 md:gap-12">
            {/* Live FX Rate Tracking */}
            <DashboardFeature
              icon={<LineChart className="h-8 w-8 text-[#028475]" />}
              title="Live FX Rate Tracking"
              description="Monitor exchange rates in real-time across all major currencies."
              features={[
                "Monitor exchange rates in real-time across all major currencies",
                "Set FX alerts for favorable conversion thresholds",
                "Historical rate trends and analysis",
              ]}
              imageSrc="/placeholder.svg?height=300&width=500"
              imageAlt="FX rate tracking interface"
            />

            {/* Payment Activity Overview */}
            <DashboardFeature
              icon={<BarChart3 className="h-8 w-8 text-[#028475]" />}
              title="Payment Activity Overview"
              description="Comprehensive view of all payment activities across your organization."
              features={[
                "View incoming and outgoing payments by status, partner, and country",
                "Filter transactions by currency, method (wire, BNPL, escrow), or business unit",
                "Real-time payment status updates and notifications",
              ]}
              imageSrc="/placeholder.svg?height=300&width=500"
              imageAlt="Payment activity dashboard"
              imageRight
            />

            {/* Liquidity & Exposure Analysis */}
            <DashboardFeature
              icon={<Wallet className="h-8 w-8 text-[#028475]" />}
              title="Liquidity & Exposure Analysis"
              description="Gain insights into your financial positions across currencies and regions."
              features={[
                "See available funds by currency and country",
                "Detect overexposed or underfunded regions instantly",
                "Forecast cash flow based on pending transactions",
              ]}
              imageSrc="/placeholder.svg?height=300&width=500"
              imageAlt="Liquidity analysis dashboard"
            />

            {/* Reconciliation & Document Matching */}
            <DashboardFeature
              icon={<FileCheck className="h-8 w-8 text-[#028475]" />}
              title="Reconciliation & Document Matching"
              description="Streamline your financial reconciliation processes."
              features={[
                "Match payments with invoices, orders, and proof of payment documents",
                "Export transaction summaries to your ERP or accounting system",
                "Automated reconciliation suggestions and anomaly detection",
              ]}
              imageSrc="/placeholder.svg?height=300&width=500"
              imageAlt="Reconciliation interface"
              imageRight
            />
          </div>
        </div>
      </section>

      {/* Security & Role-Based Access Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Security & Role-Based Access</h2>
            <p className="text-gray-600 max-w-3xl">
              Enterprise-grade security features ensure your financial data remains protected.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <SecurityFeature
              icon={<Users className="h-8 w-8 text-[#028475]" />}
              title="Role-Based Permissions"
              description="Segmented access for treasury, compliance, and regional finance teams"
            />
            <SecurityFeature
              icon={<Lock className="h-8 w-8 text-[#028475]" />}
              title="Two-Factor Authentication"
              description="Enhanced security for sensitive financial operations"
            />
            <SecurityFeature
              icon={<ShieldCheck className="h-8 w-8 text-[#028475]" />}
              title="Audit Logging"
              description="Comprehensive activity tracking for compliance requirements"
            />
            <SecurityFeature
              icon={<Layers className="h-8 w-8 text-[#028475]" />}
              title="Enterprise Financial Controls"
              description="Meets strict enterprise security and compliance standards"
            />
          </div>
        </div>
      </section>

      {/* Multi-Currency Support Section */}
      <section className="py-16 md:py-24 bg-[#F2F2F2]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Multi-Currency Support</h2>
            <p className="text-gray-600 max-w-3xl">
              Comprehensive support for global currencies to facilitate international trade.
            </p>
          </div>

          <div className="grid gap-4 grid-cols-2 md:grid-cols-4 lg:grid-cols-7">
            <CurrencyCard code="USD" name="US Dollar" />
            <CurrencyCard code="EUR" name="Euro" />
            <CurrencyCard code="GBP" name="British Pound" />
            <CurrencyCard code="AED" name="UAE Dirham" />
            <CurrencyCard code="JPY" name="Japanese Yen" />
            <CurrencyCard code="CNY" name="Chinese Yuan" />
            <CurrencyCard code="MXN" name="Mexican Peso" />
          </div>

          <div className="mt-8 text-center">
            <p className="text-gray-600">
              Supports over 25 global currencies with cross-border visibility across regional accounts and subsidiaries.
            </p>
          </div>
        </div>
      </section>

      {/* Treasury Tools Integration Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Treasury Tools Integration</h2>
            <p className="text-gray-600 max-w-3xl">
              Seamlessly integrated with StreamLnk's comprehensive financial ecosystem.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <IntegrationFeature
              icon={<Wallet className="h-8 w-8 text-[#028475]" />}
              title="BNPL Integration"
              description="Monitor Buy Now Pay Later transactions and upcoming payment schedules"
            />
            <IntegrationFeature
              icon={<Lock className="h-8 w-8 text-[#028475]" />}
              title="Escrow Integration"
              description="Track escrow funds and milestone-based payment releases"
            />
            <IntegrationFeature
              icon={<DollarSign className="h-8 w-8 text-[#028475]" />}
              title="Payment Workflows"
              description="Integrated with all StreamLnk payment methods and processes"
            />
            <IntegrationFeature
              icon={<Bell className="h-8 w-8 text-[#028475]" />}
              title="Real-Time Transaction Syncing"
              description="Directly linked to MyStreamLnk and E-Stream portals"
            />
          </div>
        </div>
      </section>

      {/* Common Use Cases Section */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Common Use Cases</h2>
            <p className="text-gray-600 max-w-3xl">
              The Treasury & FX Dashboard serves various roles across your organization.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <UseCaseCard
              icon={<Building2 className="h-8 w-8 text-[#028475]" />}
              title="CFOs"
              description="Monitoring FX exposure by region and making strategic financial decisions"
            />
            <UseCaseCard
              icon={<FileCheck className="h-8 w-8 text-[#028475]" />}
              title="Finance Managers"
              description="Reconciling multi-currency payments and managing cash flow"
            />
            <UseCaseCard
              icon={<ShieldCheck className="h-8 w-8 text-[#028475]" />}
              title="Compliance Teams"
              description="Reviewing transaction logs and proof of payment documentation"
            />
            <UseCaseCard
              icon={<Globe className="h-8 w-8 text-[#028475]" />}
              title="Procurement Leaders"
              description="Tracking cleared vs. pending invoices across global operations"
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-[#F2F2F2]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Gain Financial Command Across Borders
            </h2>
            <p className="text-gray-600 max-w-3xl mb-8">
              Empower your team with StreamLnk's Treasury & FX Dashboard—designed for global visibility, control, and
              peace of mind.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button className="bg-[#004235] hover:bg-[#004235]/90 px-8 py-6 text-lg">
                Request Access <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button variant="outline" className="border-[#028475] text-[#028475] px-8 py-6 text-lg">
                Schedule Demo
              </Button>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
