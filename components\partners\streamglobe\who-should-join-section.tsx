import { Shield } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

export function WhoShouldJoinSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235]">
              Who Should Join StreamGlobe?
            </h2>
            <p className="mx-auto max-w-[700px] text-gray-600 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              We are looking for experienced and licensed professionals
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-4xl grid-cols-1 gap-6 md:grid-cols-2 mt-12">
          {[
            {
              title: "Licensed Customs Brokers and Agencies",
              icon: <Shield className="h-10 w-10 text-[#028475]" />,
            },
            {
              title: "Regional Port-Based Clearance Service Providers",
              icon: <Shield className="h-10 w-10 text-[#028475]" />,
            },
            {
              title: "Specialists in International Trade Compliance",
              icon: <Shield className="h-10 w-10 text-[#028475]" />,
            },
            {
              title: "Inland Bonded Carriers offering customs facilitation services",
              icon: <Shield className="h-10 w-10 text-[#028475]" />,
            },
          ].map((item, index) => (
            <Card key={index} className="border-none shadow-md">
              <CardContent className="p-6 flex items-center">
                <div className="mr-4">{item.icon}</div>
                <h3 className="text-xl font-bold text-[#004235]">{item.title}</h3>
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="mt-8 text-center">
          <p className="text-gray-600 md:text-lg font-medium">
            You must hold valid authorization and licensing in the countries and ports you intend to serve, with
            verifiable proof.
          </p>
        </div>
      </div>
    </section>
  )
}
