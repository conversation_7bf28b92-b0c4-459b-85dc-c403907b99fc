"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { MainNav } from "@/components/main-nav"
import { BottomFooter } from "@/components/bottom-footer"
import { ChevronRight } from "lucide-react"

export default function DeveloperPortalPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      {/* Hero Section - Figma: 325:650 to 325:658 */}
      <section className="bg-[#004235] text-white py-16 md:py-20">
        <div className="container mx-auto px-4 text-center">
          {/* Figma: 325:651 - StreamLnk For Developers */}
          <h2 className="text-2xl font-light uppercase mb-3 tracking-wide">StreamLnk For Developers</h2>
          {/* Figma: 325:652 - Modern REST APIs, SIMPLY DELIVERED */}
          <h1 className="text-3xl md:text-4xl font-extrabold mb-8">Modern REST APIs, SIMPLY DELIVERED</h1>
          <div className="flex flex-wrap justify-center gap-4">
            {/* Figma: 325:654, 325:655 - Sign up Button */}
            <Button 
              size="lg" 
              className="bg-[#028475] hover:bg-[#028475]/90 text-white font-semibold px-8 py-3 rounded-sm"
              asChild
            >
              <Link href="#">Sign up</Link>
            </Button>
            {/* Figma: 325:657, 325:658 - Browse our APIs Button */}
            <Button 
              variant="outline"
              size="lg" 
              className="border-white text-white hover:bg-white hover:text-[#004235] font-semibold uppercase flex items-center px-8 py-3 rounded-sm"
              asChild
            >
              <Link href="#">Browse our APIs <ChevronRight className="h-4 w-4 ml-2" /></Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Services Section - Figma: 325:660 to 325:684 */}
      <section className="py-16 md:py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="mb-12 max-w-xl">
            {/* Figma: 325:661 - Our Services */}
            <h2 className="text-4xl font-bold text-[#004235] mb-4">Our Services</h2>
            {/* Figma: 325:662 - Line 8 */}
            <div className="w-20 h-1 bg-[#028475] mb-4"></div>
            {/* Figma: 325:663 - Dig into our API... */}
            <p className="text-base font-light text-gray-700">Dig into our API reference documentation and quickstarts.</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Service Card 1 - Tracking - Figma: 325:665 to 325:670 */}
            <div className="bg-white p-8 rounded-md shadow-md border border-gray-200 flex flex-col">
              <h3 className="text-xl font-bold text-black mb-2">Tracking</h3>
              <p className="text-sm font-normal text-gray-600 mb-6 flex-grow">Receive your shipment status</p>
              <Button 
                variant="outline"
                className="text-[#028475] border-[#028475] hover:bg-[#028475] hover:text-white w-full font-medium rounded-sm"
                asChild
              >
                <Link href="#">Browse Tracking APIs</Link>
              </Button>
            </div>

            {/* Service Card 2 - Shipping - Figma: 325:672 to 325:677 */}
            <div className="bg-white p-8 rounded-md shadow-md border border-gray-200 flex flex-col">
              <h3 className="text-xl font-bold text-black mb-2">Shipping</h3>
              <p className="text-sm font-normal text-gray-600 mb-6 flex-grow">Create your shipments including labels</p>
              <Button 
                variant="outline" 
                className="text-[#028475] border-[#028475] hover:bg-[#028475] hover:text-white w-full font-medium rounded-sm"
                asChild
              >
                <Link href="#">Browse Shipping APIs</Link>
              </Button>
            </div>

            {/* Service Card 3 - Further Services - Figma: 325:679 to 325:684 */}
            <div className="bg-white p-8 rounded-md shadow-md border border-gray-200 flex flex-col">
              <h3 className="text-xl font-bold text-black mb-2">Further Services</h3>
              <p className="text-sm font-normal text-gray-600 mb-6 flex-grow">Browse our whole API catalog</p>
              <Button 
                variant="outline" 
                className="text-[#028475] border-[#028475] hover:bg-[#028475] hover:text-white w-full font-medium rounded-sm"
                asChild
              >
                <Link href="#">Browse All Services</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* About StreamLnk API Developer Portal - Figma: 325:686 to 325:695 */}
      <section className="py-16 md:py-20 bg-[#F2F2F2]">
        <div className="container mx-auto px-4">
          <div className="mb-8 max-w-xl">
            {/* Figma: 325:687 - About StreamLnk Api Developer Portal */}
            <h2 className="text-4xl font-bold text-[#004235] mb-4">About StreamLnk API Developer Portal</h2>
            {/* Figma: 325:688 - Line 8 */}
            <div className="w-20 h-1 bg-[#028475] mb-4"></div>
          </div>
          
          <div className="max-w-4xl">
            {/* Figma: 325:689 - Text content */}
            <p className="text-base font-light text-gray-800 mb-4">
              The StreamLnk Developer Portal is StreamLnk’s single point of contact for access to APIs from all its business divisions, allowing to signing up for them and making them easy to consume.
            </p>
            <p className="text-base font-light text-gray-800 mb-8">
              Check the API Catalog to see the APIs already released on this portal. Further APIs from other DPDHL divisions will be added in the coming months.
            </p>
            
            <div className="flex flex-wrap gap-4">
              {/* Figma: 325:691, 325:692 - Sign up Button */}
              <Button 
                className="bg-[#028475] hover:bg-[#028475]/90 text-white font-semibold px-8 py-3 rounded-sm"
                asChild
              >
                <Link href="#">Sign up</Link>
              </Button>
              {/* Figma: 325:694, 325:695 - Learn More Button */}
              <Button 
                variant="outline" 
                className="border-[#028475] text-[#028475] bg-white hover:bg-[#028475]/10 font-semibold px-8 py-3 rounded-sm"
                asChild
              >
                <Link href="#">Learn More</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Help Section - Figma: 325:697 to 325:702 */}
      <section className="py-16 md:py-20 bg-[#f3f4f6]">
        <div className="container mx-auto px-4 text-center">
          {/* Figma: 325:698 - Need Help? */}
          <h2 className="text-4xl font-bold text-[#004235] mb-6">Need Help?</h2>
          {/* Figma: 325:699 - Text content */}
          <div className="max-w-2xl mx-auto space-y-2 mb-8 text-base font-light text-gray-700">
            <p>Problems to register in the portal?</p>
            <p>What standards do the DHL APIs follow?</p>
            <p>Check the frequently asked questions</p>
          </div>
          {/* Figma: 325:701, 325:702 - Help Center Button */}
          <Button 
            className="bg-[#028475] hover:bg-[#028475]/90 text-white font-semibold px-8 py-3 rounded-sm"
            asChild
          >
            <Link href="#">Help Center</Link>
          </Button>
        </div>
      </section>

      <BottomFooter />
    </div>
  )
}