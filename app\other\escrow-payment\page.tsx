import type React from "react"
import Image from "next/image"
import {
  <PERSON>,
  <PERSON>R<PERSON>,
  <PERSON><PERSON><PERSON>cle2,
  <PERSON>,
  FileCheck,
  Users,
  Building2,
  <PERSON>riefcase,
  LockKeyhole,
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import MilestoneCard from "./components/milestone-card"
import BenefitCard from "./components/benefit-card"
import UserTypeCard from "./components/user-type-card"
import ActivationStep from "./components/activation-step"

export default function EscrowPaymentPage() {
  return (
    <main className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-[#F2F2F2] py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
            <div className="flex flex-col justify-center space-y-4">
              <div className="inline-block rounded-lg bg-[#004235]/10 px-3 py-1 text-sm text-[#004235]">
                Secure Payment Solutions
              </div>
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235]">
                Escrow & Milestone-Based Payments
              </h1>
              <p className="text-gray-600 md:text-xl">
                Built-in escrow and milestone-based payment solutions designed for high-value, cross-border B2B
                transactions that build trust, ensure delivery, and protect both parties.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button className="bg-[#004235] hover:bg-[#004235]/90">
                  Get Started <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                <Button variant="outline" className="border-[#028475] text-[#028475]">
                  Learn More
                </Button>
              </div>
            </div>
            <div className="flex justify-center">
              <div className="relative w-full max-w-[500px] h-[300px] md:h-[400px]">
                <Image
                  src="/placeholder.svg?height=400&width=500"
                  alt="Escrow payment illustration"
                  fill
                  className="object-contain"
                  priority
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How StreamLnk Escrow Works */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">How StreamLnk Escrow Works</h2>
            <p className="text-gray-600 max-w-3xl">
              Our escrow system provides security and peace of mind for both buyers and suppliers throughout the
              transaction process.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
              <CardContent className="pt-6">
                <div className="flex flex-col items-center text-center space-y-4">
                  <div className="p-3 rounded-full bg-[#004235]/10">
                    <LockKeyhole className="h-8 w-8 text-[#028475]" />
                  </div>
                  <h3 className="text-xl font-semibold text-[#004235]">Funds Held Securely</h3>
                  <p className="text-gray-600">
                    Buyer deposits funds into a protected third-party escrow account, ensuring payment is available but
                    protected.
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
              <CardContent className="pt-6">
                <div className="flex flex-col items-center text-center space-y-4">
                  <div className="p-3 rounded-full bg-[#004235]/10">
                    <CheckCircle2 className="h-8 w-8 text-[#028475]" />
                  </div>
                  <h3 className="text-xl font-semibold text-[#004235]">Release on Milestone Completion</h3>
                  <p className="text-gray-600">
                    Funds are released automatically or manually as key delivery or documentation milestones are met.
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
              <CardContent className="pt-6">
                <div className="flex flex-col items-center text-center space-y-4">
                  <div className="p-3 rounded-full bg-[#004235]/10">
                    <Shield className="h-8 w-8 text-[#028475]" />
                  </div>
                  <h3 className="text-xl font-semibold text-[#004235]">Dispute Resolution Protocols</h3>
                  <p className="text-gray-600">
                    In case of discrepancies, StreamLnk offers built-in resolution pathways with time-stamped logs and
                    document audits.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Common Milestones Section */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Common Milestones Supported</h2>
            <p className="text-gray-600 max-w-3xl">
              Buyers and sellers can define milestones flexibly at the RFQ or contract stage.
            </p>
          </div>

          <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-5">
            <MilestoneCard
              icon={<FileCheck className="h-6 w-6 text-[#028475]" />}
              title="Order confirmation"
              description="Initial milestone when the order is confirmed by both parties"
            />
            <MilestoneCard
              icon={<Clock className="h-6 w-6 text-[#028475]" />}
              title="Shipment dispatch"
              description="When goods are dispatched from the supplier's facility"
            />
            <MilestoneCard
              icon={<Building2 className="h-6 w-6 text-[#028475]" />}
              title="Customs clearance"
              description="When goods have cleared customs in the destination country"
            />
            <MilestoneCard
              icon={<CheckCircle2 className="h-6 w-6 text-[#028475]" />}
              title="Delivery received"
              description="When buyer confirms receipt of the goods"
            />
            <MilestoneCard
              icon={<FileCheck className="h-6 w-6 text-[#028475]" />}
              title="Document package approved"
              description="When all required documentation is approved (B/L, COA, invoice)"
            />
          </div>
        </div>
      </section>

      {/* Why Use Escrow Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Why Use Escrow?</h2>
            <p className="text-gray-600 max-w-3xl">
              Escrow payments provide significant benefits for both buyers and suppliers in B2B transactions.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2">
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <Users className="h-6 w-6 text-[#028475]" />
                <h3 className="text-xl font-semibold text-[#004235]">For Buyers</h3>
              </div>
              <ul className="space-y-3">
                <li className="flex items-start gap-2">
                  <CheckCircle2 className="h-5 w-5 text-[#028475] mt-0.5 flex-shrink-0" />
                  <span className="text-gray-600">Reduce risk in first-time supplier relationships</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle2 className="h-5 w-5 text-[#028475] mt-0.5 flex-shrink-0" />
                  <span className="text-gray-600">
                    Ensure funds are only released when goods or documents meet expectations
                  </span>
                </li>
              </ul>
            </div>

            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <Briefcase className="h-6 w-6 text-[#028475]" />
                <h3 className="text-xl font-semibold text-[#004235]">For Suppliers</h3>
              </div>
              <ul className="space-y-3">
                <li className="flex items-start gap-2">
                  <CheckCircle2 className="h-5 w-5 text-[#028475] mt-0.5 flex-shrink-0" />
                  <span className="text-gray-600">Confirm buyer payment commitment upfront</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle2 className="h-5 w-5 text-[#028475] mt-0.5 flex-shrink-0" />
                  <span className="text-gray-600">Get paid promptly as each phase is fulfilled</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* StreamLnk Escrow Benefits */}
      <section className="py-16 md:py-24 bg-[#F2F2F2]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">StreamLnk Escrow Benefits</h2>
            <p className="text-gray-600 max-w-3xl">
              Our escrow system provides comprehensive benefits to ensure secure and transparent transactions.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <BenefitCard
              icon={<Shield className="h-8 w-8 text-[#028475]" />}
              title="Risk Mitigation"
              description="Lower risk of fraud, payment delays, or non-performance"
            />
            <BenefitCard
              icon={<Clock className="h-8 w-8 text-[#028475]" />}
              title="Audit Trail"
              description="Timestamped activity logs, document uploads, and status confirmations"
            />
            <BenefitCard
              icon={<Globe className="h-8 w-8 text-[#028475]" />}
              title="Global Support"
              description="Works across regions, Incoterms, and currencies"
            />
            <BenefitCard
              icon={<FileCheck className="h-8 w-8 text-[#028475]" />}
              title="Integrated Workflow"
              description="Escrow status updates are shown inside MyStreamLnk and E-Stream portals"
            />
          </div>
        </div>
      </section>

      {/* Who Should Use Escrow Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Who Should Use Escrow & Milestone Payments?
            </h2>
            <p className="text-gray-600 max-w-3xl">
              Our escrow and milestone payment solutions are ideal for various business scenarios.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <UserTypeCard
              icon={<Users className="h-10 w-10 text-[#028475]" />}
              title="Buyers"
              description="Placing high-value or multi-stage orders with new suppliers or in new markets"
            />
            <UserTypeCard
              icon={<Briefcase className="h-10 w-10 text-[#028475]" />}
              title="Suppliers"
              description="Working with new or international clients where payment security is essential"
            />
            <UserTypeCard
              icon={<Building2 className="h-10 w-10 text-[#028475]" />}
              title="Freight & Logistics"
              description="Partners billing across multiple shipment phases or handling high-value cargo"
            />
          </div>
        </div>
      </section>

      {/* How to Activate Section */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">How to Activate</h2>
            <p className="text-gray-600 max-w-3xl">
              Getting started with StreamLnk's escrow and milestone payment system is simple.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-5">
            <ActivationStep
              number={1}
              title="Choose Escrow/Milestone Payment"
              description="Select this option during quote or order confirmation"
            />
            <ActivationStep
              number={2}
              title="Define Key Milestones"
              description="Specify milestones and responsible parties"
            />
            <ActivationStep
              number={3}
              title="Deposit Funds"
              description="Via wire, ACH, or StreamLnk-approved partners"
            />
            <ActivationStep
              number={4}
              title="Track Payment Status"
              description="Monitor release status and notifications"
            />
            <ActivationStep number={5} title="Funds Released" description="Automatically or with mutual confirmation" />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-[#F2F2F2]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Build Trust in Every Transaction</h2>
            <p className="text-gray-600 max-w-3xl mb-8">
              StreamLnk's escrow and milestone tools help you trade with confidence—globally, securely, and
              transparently.
            </p>
            <Button className="bg-[#004235] hover:bg-[#004235]/90 px-8 py-6 text-lg">Get Started Today</Button>
          </div>
        </div>
      </section>
    </main>
  )
}

function Globe(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="10" />
      <line x1="2" x2="22" y1="12" y2="12" />
      <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
    </svg>
  )
}
