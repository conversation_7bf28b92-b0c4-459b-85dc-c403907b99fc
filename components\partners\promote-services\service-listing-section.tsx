import { Card, CardContent } from "@/components/ui/card"
import { Building, ListChecks, Globe, FileText, Mail, Shield, Cpu, BarChart } from "lucide-react"

export function ServiceListingSection() {
  const listingFeatures = [
    {
      icon: <Building className="h-10 w-10 text-[#028475]" />,
      title: "Verified Company Profile",
      description: "Your logo, a compelling introduction, company history, and key differentiators.",
    },
    {
      icon: <ListChecks className="h-10 w-10 text-[#028475]" />,
      title: "Detailed Service Categories",
      description: "Clearly define the specific services you offer and your areas of specialization.",
    },
    {
      icon: <Globe className="h-10 w-10 text-[#028475]" />,
      title: "Operational Coverage",
      description: "List the countries, regions, ports, or trade lanes you serve.",
    },
    {
      icon: <FileText className="h-10 w-10 text-[#028475]" />,
      title: "Supporting Documentation",
      description:
        "Upload PDF brochures, case studies, certifications (ISO, industry-specific), and proof of insurance.",
    },
    {
      icon: <Mail className="h-10 w-10 text-[#028475]" />,
      title: "Lead Contact & Inquiry Management",
      description: "Provide a direct lead contact email or integrate with your ticketing/CRM portal.",
    },
    {
      icon: <Shield className="h-10 w-10 text-[#028475]" />,
      title: "License & Permit Upload",
      description: "Showcase your valid operating licenses and permits, reviewed by StreamLnk.",
    },
    {
      icon: <Cpu className="h-10 w-10 text-[#028475]" />,
      title: "API Capabilities (if applicable)",
      description:
        "Highlight your ability to integrate for live service synchronization (e.g., real-time tracking, instant quoting).",
    },
    {
      icon: <BarChart className="h-10 w-10 text-[#028475]" />,
      title: "Performance Rating & Review Dashboard",
      description:
        "Display aggregated customer feedback, SLA adherence metrics, and StreamLnk verified performance scores.",
    },
  ]

  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            What You Can Include in Your Enhanced Service Listing:
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
            {listingFeatures.map((feature, index) => (
              <Card key={index} className="border-none shadow-md hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6 flex flex-col items-center text-center">
                  <div className="mb-4 rounded-full bg-[#F2F2F2] p-4">{feature.icon}</div>
                  <h3 className="text-lg font-semibold text-[#004235] mb-2">{feature.title}</h3>
                  <p className="text-gray-600 text-sm">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
