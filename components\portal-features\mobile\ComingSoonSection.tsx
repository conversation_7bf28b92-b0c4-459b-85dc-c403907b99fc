// components/portal-features/mobile/ComingSoonSection.tsx
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";

export default function ComingSoonSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-[#004235] mb-4">The StreamLnk Mobile App – Coming Soon to Your Device!</h2>
          <h3 className="text-xl font-semibold text-[#028475] mb-6">Availability & Getting Notified</h3>
          <p className="text-lg text-gray-700">
            We are diligently working on developing our native mobile applications for both iOS and Android to provide you with the best possible on-the-go experience.
          </p>
          <p className="text-lg text-gray-700 mt-4">
            <span className="font-semibold">Expected Launch Timeline:</span> [Targeting launch in QX 202X].
          </p>
        </div>

        <div className="max-w-xl mx-auto bg-white p-8 rounded-lg shadow-md">
          <h4 className="text-xl font-semibold text-[#004235] mb-4 text-center">Be the First to Know</h4>
          <p className="text-gray-700 mb-6 text-center">
            Sign up below to receive exclusive updates on app development progress, beta testing opportunities, and official launch announcements.
          </p>

          <div className="space-y-4">
            <div>
              <Input type="email" placeholder="Your Email Address" className="w-full" />
            </div>

            <div className="flex items-center space-x-8 justify-center">
              <div className="flex items-center space-x-2">
                <Checkbox id="ios" />
                <label htmlFor="ios" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">iOS User</label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="android" />
                <label htmlFor="android" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Android User</label>
              </div>
            </div>

            <Button className="w-full bg-[#004235] hover:bg-[#028475] text-white py-5">
              NOTIFY ME ABOUT APP LAUNCH
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}