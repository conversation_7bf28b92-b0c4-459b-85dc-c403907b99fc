import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"

export default function AdvancedFeaturesSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">Advanced Tools & Features</h2>
          <p className="text-lg text-gray-700">
            Our platform offers sophisticated tools to enhance your auction experience.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          <div className="bg-white rounded-lg p-6 shadow-sm flex flex-col items-center text-center">
            <div className="rounded-full bg-[#028475]/10 w-16 h-16 flex items-center justify-center mb-4">
              <Bot className="h-8 w-8 text-[#028475]" />
            </div>
            <h3 className="text-xl font-semibold text-[#004235] mb-2">Bid Assist AI</h3>
            <p className="text-gray-700">
              Provides insights for suppliers on optimal pricing strategies and for buyers on competitive participation.
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm flex flex-col items-center text-center">
            <div className="rounded-full bg-[#028475]/10 w-16 h-16 flex items-center justify-center mb-4">
              <Lightbulb className="h-8 w-8 text-[#028475]" />
            </div>
            <h3 className="text-xl font-semibold text-[#004235] mb-2">Auto-Bid Parameters</h3>
            <p className="text-gray-700">
              Allows buyers to set minimum/maximum bid limits and let the system bid automatically on their behalf up to
              their threshold.
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm flex flex-col items-center text-center">
            <div className="rounded-full bg-[#028475]/10 w-16 h-16 flex items-center justify-center mb-4">
              <Gauge className="h-8 w-8 text-[#028475]" />
            </div>
            <h3 className="text-xl font-semibold text-[#004235] mb-2">Auction Heat Index</h3>
            <p className="text-gray-700">
              A visual indicator based on current demand, regional activity, and inventory stress levels for a
              particular auction.
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm flex flex-col items-center text-center">
            <div className="rounded-full bg-[#028475]/10 w-16 h-16 flex items-center justify-center mb-4">
              <BellRing className="h-8 w-8 text-[#028475]" />
            </div>
            <h3 className="text-xl font-semibold text-[#004235] mb-2">Smart Alerts & Campaign Boosts</h3>
            <p className="text-gray-700">
              Notifications for new auctions, last-minute deals, cost-saving opportunities, or new product launches
              featured in auctions.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
