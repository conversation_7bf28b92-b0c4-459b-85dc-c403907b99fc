import { BarChart3, Globe, Package, Shield, Truck } from "lucide-react"
import { StandardizedTimeline } from "@/components/ui/standardized-timeline";

const steps = [
  {
    title: "Freight Matching",
    description: "Integrated with StreamFreight for trucking, rail, and container options.",
    icon: <Truck className="h-8 w-8 text-white" />,
  },
  {
    title: "Customs & Compliance",
    description: "Data routed to StreamGlobe customs agents.",
    icon: <Shield className="h-8 w-8 text-white" />,
  },
  {
    title: "Packaging & Warehousing",
    description: "Routed to StreamPak for labeling, repackaging, or inventory management if needed.",
    icon: <Package className="h-8 w-8 text-white" />,
  },
  {
    title: "Shipment Tracking",
    description: "All stakeholders receive status updates through real-time Track & Trace.",
    icon: <Globe className="h-8 w-8 text-white" />,
  },
  {
    title: "Invoicing & Payment",
    description: "Managed by StreamLnk's treasury tools with milestone-based or escrow options.",
    icon: <BarChart3 className="h-8 w-8 text-white" />,
  },
]

export function SupplyChainSection() {
  return (
    <StandardizedTimeline
      title="Supply Chain Automation, Built In"
      description="Once a buyer places an order or wins an auction, E-Stream triggers the following automated flow:"
      steps={steps}
      bgColor="bg-[#F2F2F2]"
    />
  )
}
