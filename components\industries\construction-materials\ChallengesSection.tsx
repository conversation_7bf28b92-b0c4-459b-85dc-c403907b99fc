"use client";

import { CheckCircle } from "lucide-react";

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Facing Delays and Cost Overruns Due to Material Sourcing & Logistics?
          </h2>
          <p className="text-lg text-gray-700 mb-4 text-center">
            Key Supply Chain Challenges in the Construction Industry
          </p>
          <p className="text-lg text-gray-700 mb-10 text-center">
            The construction industry relies on a complex and often fragmented supply chain for a vast array of materials, leading to challenges such as:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Fragmented Supplier Base</h3>
                  <p className="text-gray-600">Sourcing diverse materials (cement, steel, lumber, aggregates, fixtures, etc.) from numerous, often local or regional, suppliers.</p>
                </div>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Price Volatility & Quoting Delays</h3>
                  <p className="text-gray-600">Difficulty obtaining timely and competitive quotes for bulk materials, often impacting project bids.</p>
                </div>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Logistics for Bulk & Heavy Materials</h3>
                  <p className="text-gray-600">Coordinating the transport of heavy, oversized, or bulk materials to often remote or congested construction sites.</p>
                </div>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Just-in-Time Delivery for Project Schedules</h3>
                  <p className="text-gray-600">Ensuring materials arrive on site exactly when needed to avoid costly project delays or storage issues.</p>
                </div>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Quality Assurance & Material Certification</h3>
                  <p className="text-gray-600">Verifying that materials meet specific engineering standards and project requirements.</p>
                </div>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Inventory Management on Site</h3>
                  <p className="text-gray-600">Managing material laydown areas and preventing spoilage or theft.</p>
                </div>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Demand for Sustainable & Green Building Materials</h3>
                  <p className="text-gray-600">Increasing need to source and document the use of environmentally friendly construction products.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}