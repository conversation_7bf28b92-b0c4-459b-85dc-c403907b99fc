import type React from "react"

interface Step {
  icon: React.ReactNode
  title: string
  description: string
}

interface ProcessStepsProps {
  steps: Step[]
}

export function ProcessSteps({ steps }: ProcessStepsProps) {
  return (
    <div className="relative">
      {/* Connecting line */}
      <div className="absolute top-12 left-0 right-0 h-1 bg-[#18b793] hidden md:block"></div>

      <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
        {steps.map((step, index) => (
          <div key={index} className="flex flex-col items-center text-center relative">
            <div className="bg-[#18b793] rounded-full w-24 h-24 flex items-center justify-center mb-6 z-10">
              {step.icon}
            </div>
            <h3 className="text-xl font-bold mb-2 text-[#004235]">{step.title}</h3>
            <p className="text-gray-600">{step.description}</p>
            <div className="absolute top-12 text-[#18b793] font-bold text-xl hidden md:block">{index + 1}</div>
          </div>
        ))}
      </div>
    </div>
  )
}
