"use client"

import { CheckCircle } from "lucide-react";

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Navigating the Dynamic & Complex Energy Market
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            Facing Volatility and Complexity in Your Energy Supply Chain?
          </p>
          <p className="text-lg text-gray-700 mb-10 text-center">
            The global energy sector is characterized by high stakes, price volatility, intricate logistics, and stringent regulatory oversight, presenting unique challenges:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Price Volatility & Hedging</h3>
                  <p className="text-gray-600">Managing exposure to rapidly fluctuating prices for crude oil, natural gas, refined products, and renewable energy credits.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Complex Global Logistics</h3>
                  <p className="text-gray-600">Coordinating bulk shipments via tankers, pipelines, rail, and specialized transport for components like wind turbine blades or solar panels.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Stringent Regulatory & Environmental Compliance</h3>
                  <p className="text-gray-600">Adhering to international maritime laws, environmental regulations, safety standards, and carbon reporting.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Counterparty Risk & Due Diligence</h3>
                  <p className="text-gray-600">Verifying the credentials and financial stability of global energy suppliers, traders, and off-takers.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Supply Chain Visibility</h3>
                  <p className="text-gray-600">Tracking bulk energy flows and critical component deliveries across extensive global networks.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Transition to Renewables</h3>
                  <p className="text-gray-600">Sourcing materials and managing logistics for renewable energy projects (solar, wind, battery storage, hydrogen).</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}