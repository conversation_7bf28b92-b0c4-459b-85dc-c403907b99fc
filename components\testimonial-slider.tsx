"use client"

import { useState } from "react"
import Image from "next/image"
import { ChevronLeft, ChevronRight, Quote } from "lucide-react"

interface Testimonial {
  quote: string
  author: string
  company: string
  image: string
}

interface TestimonialSliderProps {
  testimonials: Testimonial[]
}

export function TestimonialSlider({ testimonials }: TestimonialSliderProps) {
  const [currentIndex, setCurrentIndex] = useState(0)

  const goToPrevious = () => {
    const isFirstSlide = currentIndex === 0
    const newIndex = isFirstSlide ? testimonials.length - 1 : currentIndex - 1
    setCurrentIndex(newIndex)
  }

  const goToNext = () => {
    const isLastSlide = currentIndex === testimonials.length - 1
    const newIndex = isLastSlide ? 0 : currentIndex + 1
    setCurrentIndex(newIndex)
  }

  const goToSlide = (slideIndex: number) => {
    setCurrentIndex(slideIndex)
  }

  return (
    <div className="relative max-w-4xl mx-auto">
      <div className="bg-[#003025] rounded-xl p-8 md:p-12 relative">
        <Quote className="h-16 w-16 text-[#18b793]/20 absolute top-6 left-6" />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
          <div className="md:col-span-1 flex justify-center">
            <div className="relative w-32 h-32 rounded-full overflow-hidden border-4 border-[#18b793]">
              <Image
                src={testimonials[currentIndex].image || "/images/streamfreight/testimonial-placeholder.webp"}
                alt={testimonials[currentIndex].author}
                fill
                className="object-cover"
              />
            </div>
          </div>

          <div className="md:col-span-2">
            <p className="text-lg md:text-xl mb-6 italic">"{testimonials[currentIndex].quote}"</p>
            <div>
              <p className="font-bold text-lg">{testimonials[currentIndex].author}</p>
              <p className="text-[#18b793]">{testimonials[currentIndex].company}</p>
            </div>
          </div>
        </div>

        <div className="flex justify-center mt-8 gap-2">
          {testimonials.map((_, slideIndex) => (
            <div
              key={slideIndex}
              onClick={() => goToSlide(slideIndex)}
              className={`w-3 h-3 rounded-full cursor-pointer transition-all ${
                slideIndex === currentIndex ? "bg-[#18b793] w-6" : "bg-white/30"
              }`}
            ></div>
          ))}
        </div>
      </div>

      <button
        onClick={goToPrevious}
        className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 bg-[#18b793] rounded-full p-3 text-white shadow-lg hover:bg-[#004235] transition-colors"
        aria-label="Previous testimonial"
      >
        <ChevronLeft className="h-6 w-6" />
      </button>

      <button
        onClick={goToNext}
        className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 bg-[#18b793] rounded-full p-3 text-white shadow-lg hover:bg-[#004235] transition-colors"
        aria-label="Next testimonial"
      >
        <ChevronRight className="h-6 w-6" />
      </button>
    </div>
  )
}
