import type React from "react"
import Image from "next/image"
import {
  ArrowRight,
  Brain,
  Database,
  Shield,
  BarChart3,
  LineChart,
  FileText,
  Truck,
  Globe,
  Lock,
  Zap,
  Layers,
  LinkIcon,
  Users,
  Cpu,
  Wifi,
  Blocks,
  Leaf,
  Code,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import InnovationPillar from "./components/innovation-pillar"
import AiFeature from "./components/ai-feature"
import DataFeature from "./components/data-feature"
import RiskFeature from "./components/risk-feature"
import RoadmapItem from "./components/roadmap-item"
import CollaborationCard from "./components/collaboration-card"

export default function InnovationPage() {
  return (
    <main className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-[#F2F2F2] py-16 md:py-24 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="/placeholder.svg?height=800&width=1600"
            alt="Abstract neural network"
            fill
            className="object-cover"
          />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <div className="inline-block rounded-lg bg-[#004235]/10 px-3 py-1 text-sm text-[#004235] mb-4">
              Innovation at StreamLnk
            </div>
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235] mb-4">
              The Future of Industrial Trade is Intelligent. We're Building It.
            </h1>
            <p className="text-gray-600 md:text-xl mb-8">
              StreamLnk is pioneering the next generation of B2B commerce by embedding Artificial Intelligence, advanced
              data analytics, and sophisticated risk management into every transaction and workflow.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button className="bg-[#004235] hover:bg-[#004235]/90">
                Explore Our AI-Powered Features <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button variant="outline" className="border-[#028475] text-[#028475]">
                Learn About Our Data Engine
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Section 1: Our Innovation Mandate */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Beyond Digitization: Engineering Intelligence into Global Supply Chains
            </h2>
            <p className="text-gray-600">
              At StreamLnk, innovation isn't just a buzzword—it's the core of our DNA. We believe that simply moving
              industrial trade online isn't enough. True transformation requires a fundamental rethinking of how
              information flows, decisions are made, and risks are managed.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-3">
            <InnovationPillar
              icon={<Brain className="h-10 w-10 text-[#028475]" />}
              title="Artificial Intelligence & Machine Learning"
              description="Enhancing decision-making, automating complex processes, and delivering superior user experiences."
            />
            <InnovationPillar
              icon={<Database className="h-10 w-10 text-[#028475]" />}
              title="Big Data Analytics"
              description="Transforming data into strategic assets with comprehensive capture, analysis, and actionable insights."
            />
            <InnovationPillar
              icon={<Shield className="h-10 w-10 text-[#028475]" />}
              title="Proactive Risk Management"
              description="Building trust and resilience through multi-layered vetting, fraud detection, and secure infrastructure."
            />
          </div>

          <div className="mt-12 bg-[#f3f4f6] p-6 rounded-lg">
            <h3 className="text-xl font-semibold text-[#004235] mb-4">
              Our goal is to create a self-optimizing ecosystem where:
            </h3>
            <ul className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
              <li className="flex items-start gap-2">
                <Zap className="h-5 w-5 text-[#028475] mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">Sourcing is predictive, not reactive.</span>
              </li>
              <li className="flex items-start gap-2">
                <Truck className="h-5 w-5 text-[#028475] mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">Logistics are resilient and adaptable.</span>
              </li>
              <li className="flex items-start gap-2">
                <Lock className="h-5 w-5 text-[#028475] mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">Transactions are secure and transparent.</span>
              </li>
              <li className="flex items-start gap-2">
                <BarChart3 className="h-5 w-5 text-[#028475] mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">Market insights drive strategic advantage.</span>
              </li>
            </ul>
            <p className="mt-4 text-gray-600">
              This commitment to innovation ensures that the StreamLnk platform not only solves today's challenges but
              is also built to anticipate and address the complexities of tomorrow's global trade landscape.
            </p>
          </div>
        </div>
      </section>

      {/* Section 2: Artificial Intelligence & Machine Learning */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <div className="inline-block rounded-full bg-[#004235]/10 p-2 mb-4">
              <Brain className="h-6 w-6 text-[#028475]" />
            </div>
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              AI at the Helm: Driving Intelligent Automation and Optimization
            </h2>
            <p className="text-gray-600">
              StreamLnk leverages AI and ML across its entire platform to enhance decision-making, automate complex
              processes, and deliver superior user experiences.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <AiFeature
              icon={<Users className="h-8 w-8 text-[#028475]" />}
              title="Smart Product & Service Matching"
              description="Our AI algorithms intelligently connect buyers with the right suppliers and service providers based on detailed product specifications, compliance requirements, regional availability, past performance, and even predicted needs."
            />
            <AiFeature
              icon={<BarChart3 className="h-8 w-8 text-[#028475]" />}
              title="Dynamic Quoting & Auction Engine"
              description="AI powers our real-time quoting capabilities and optimizes our Auction Engine by suggesting optimal bid times, floor pricing (for suppliers), and predicting market responses to drive competitive and fair outcomes."
            />
            <AiFeature
              icon={<Truck className="h-8 w-8 text-[#028475]" />}
              title="Predictive Logistics & ETA Forecasting"
              description="We use ML models to analyze historical shipping data, port congestion, carrier performance, and weather patterns to provide more accurate Estimated Times of Arrival (ETAs) and proactively flag potential delays."
            />
            <AiFeature
              icon={<FileText className="h-8 w-8 text-[#028475]" />}
              title="Automated Document Processing & Compliance"
              description="AI assists in verifying compliance documents, extracting key information, and flagging discrepancies, significantly reducing manual effort and error rates."
            />
            <AiFeature
              icon={<Layers className="h-8 w-8 text-[#028475]" />}
              title="Personalized Portal Experiences"
              description="ML algorithms learn user preferences and behaviors to tailor dashboard views, recommend relevant products or services, and provide contextual alerts."
            />
            <AiFeature
              icon={<Shield className="h-8 w-8 text-[#028475]" />}
              title="Intelligent Risk Scoring"
              description="AI is a core component of our risk management system, continuously evaluating transaction and counterparty risk."
            />
          </div>

          <div className="mt-12 flex justify-center">
            <div className="relative w-full max-w-4xl h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-lg">
              <Image
                src="/placeholder.svg?height=400&width=800"
                alt="AI decision points in supply chain flow"
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end">
                <div className="p-6 text-white">
                  <h3 className="text-lg font-semibold mb-2">AI Decision Points in Supply Chain Flow</h3>
                  <p className="text-sm opacity-90">
                    Visualization of how AI optimizes matching, routing, pricing, and more throughout the supply chain.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 3: Data Intelligence */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <div className="inline-block rounded-full bg-[#004235]/10 p-2 mb-4">
              <Database className="h-6 w-6 text-[#028475]" />
            </div>
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Transforming Data into Your Strategic Asset with StreamResources+
            </h2>
            <p className="text-gray-600">
              Every interaction on the StreamLnk platform generates a wealth of data. Our dedicated data intelligence
              division, StreamResources+, captures, refines, and analyzes this information to create unparalleled market
              visibility and actionable insights.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2">
            <div className="space-y-6">
              <DataFeature
                icon={<Database className="h-8 w-8 text-[#028475]" />}
                title="Comprehensive Data Capture"
                description="We gather anonymized and aggregated data on product pricing, inventory levels, shipping costs, delivery times, customs clearance durations, supplier performance, buyer demand patterns, and regional risk factors."
              />
              <DataFeature
                icon={<LineChart className="h-8 w-8 text-[#028475]" />}
                title="StreamIndex™ – Proprietary Market Benchmarks"
                description="Our flagship data product, StreamIndex™, provides real-time and historical benchmarks for industrial material pricing, logistics efficiency, and supply chain risk, tailored by region, product, and Incoterm."
              />
              <DataFeature
                icon={<BarChart3 className="h-8 w-8 text-[#028475]" />}
                title="Predictive Analytics & Forecasting"
                description="Using advanced modeling, StreamResources+ forecasts market trends, demand spikes, potential supply disruptions, and optimal sourcing/selling windows."
              />
            </div>
            <div className="space-y-6">
              <DataFeature
                icon={<Layers className="h-8 w-8 text-[#028475]" />}
                title="Customizable Dashboards & APIs"
                description="We provide both internal teams and external subscribers (suppliers, buyers, financial institutions, governments) with role-specific dashboards and API access to this rich data, enabling data-driven decision-making."
              />
              <DataFeature
                icon={<Zap className="h-8 w-8 text-[#028475]" />}
                title="Monetizing Intelligence"
                description="StreamResources+ is not just an internal tool but a strategic business unit, offering premium data products and DaaS (Data-as-a-Service) subscriptions, turning platform activity into a high-value revenue stream."
              />
              <div className="relative h-[200px] rounded-lg overflow-hidden shadow-lg">
                <Image
                  src="/placeholder.svg?height=200&width=400"
                  alt="StreamResources+ dashboard"
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end">
                  <div className="p-4 text-white">
                    <p className="text-sm font-medium">StreamResources+ Dashboard Preview</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 4: Advanced Risk Management */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <div className="inline-block rounded-full bg-[#004235]/10 p-2 mb-4">
              <Shield className="h-6 w-6 text-[#028475]" />
            </div>
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Building Trust & Resilience Through Proactive Risk Mitigation
            </h2>
            <p className="text-gray-600">
              Global industrial trade is inherently complex and carries various risks. StreamLnk's innovative approach
              to risk management is designed to protect all participants and ensure the integrity of our ecosystem.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <RiskFeature
              icon={<Users className="h-8 w-8 text-[#028475]" />}
              title="Multi-Layered Counterparty Vetting"
              description="Our comprehensive KYC/AML and compliance onboarding processes, augmented by AI-driven background checks and continuous monitoring, ensure that all participants are verified and trustworthy."
            />
            <RiskFeature
              icon={<Shield className="h-8 w-8 text-[#028475]" />}
              title="AI-Powered Fraud Detection"
              description="Machine learning models analyze transactional patterns to identify and flag suspicious activities, preventing fraudulent orders and payments."
            />
            <RiskFeature
              icon={<BarChart3 className="h-8 w-8 text-[#028475]" />}
              title="Dynamic Risk Scoring"
              description="Every supplier, buyer, and even specific trade lanes are assigned dynamic risk scores based on historical performance, compliance status, geopolitical factors, and real-time data feeds from third-party risk intelligence providers."
            />
            <RiskFeature
              icon={<Lock className="h-8 w-8 text-[#028475]" />}
              title="Secure Payment Infrastructure"
              description="We integrate secure payment gateways, escrow services, milestone-based payments, and BNPL options to mitigate financial risk for both buyers and suppliers. Our system includes robust AR follow-up and overdue invoice enforcement."
            />
            <RiskFeature
              icon={<Bell className="h-8 w-8 text-[#028475]" />}
              title="Compliance Automation & Alerts"
              description="The platform automatically tracks document expirations, flags non-compliance, and provides alerts to ensure all regulatory requirements are met."
            />
            <RiskFeature
              icon={<Layers className="h-8 w-8 text-[#028475]" />}
              title="Traceability & Auditability"
              description="(Future focus) Exploring technologies like blockchain for enhanced traceability of materials and immutable audit trails for high-value or sensitive transactions."
            />
          </div>
        </div>
      </section>

      {/* Section 5: Our Innovation Roadmap */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Continuously Pushing the Frontiers of Trade Technology
            </h2>
            <p className="text-gray-600">
              Innovation at StreamLnk is a continuous journey. We are actively researching and developing the next wave
              of features to further enhance our platform.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <RoadmapItem
              icon={<Cpu className="h-8 w-8 text-[#028475]" />}
              title="Enhanced AI for Autonomous Negotiations"
              description="Developing AI agents that can conduct initial negotiations for standard contract terms."
              timeframe="2023-2024"
            />
            <RoadmapItem
              icon={<Wifi className="h-8 w-8 text-[#028475]" />}
              title="Deeper IoT Integration"
              description="For real-time tracking of cargo conditions (temperature, humidity, shock) in transit."
              timeframe="2023-2024"
            />
            <RoadmapItem
              icon={<Blocks className="h-8 w-8 text-[#028475]" />}
              title="Blockchain for Supply Chain Provenance"
              description="Enhancing traceability and transparency for high-value and sustainable materials."
              timeframe="2024-2025"
            />
            <RoadmapItem
              icon={<Leaf className="h-8 w-8 text-[#028475]" />}
              title="Advanced ESG & Carbon Footprint Tracking"
              description="Providing tools for businesses to monitor and report on the environmental impact of their supply chains."
              timeframe="2024-2025"
            />
            <RoadmapItem
              icon={<Database className="h-8 w-8 text-[#028475]" />}
              title="Expansion of StreamResources+ Offerings"
              description="Launching new data products and predictive models for more industries and use cases."
              timeframe="Ongoing"
            />
            <RoadmapItem
              icon={<Globe className="h-8 w-8 text-[#028475]" />}
              title="Global Trade Intelligence Network"
              description="Creating an interconnected ecosystem of trade data, insights, and predictive capabilities."
              timeframe="2025+"
            />
          </div>

          <div className="mt-12 relative h-[200px] rounded-lg overflow-hidden shadow-lg">
            <Image
              src="/placeholder.svg?height=200&width=1200"
              alt="Innovation timeline"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end">
              <div className="p-6 text-white">
                <h3 className="text-lg font-semibold mb-2">StreamLnk Innovation Timeline</h3>
                <p className="text-sm opacity-90">
                  Key past innovations and future R&D focuses on our journey to transform global trade.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 6: Collaborate with StreamLnk Innovation */}
      <section className="py-16 md:py-24 bg-[#F2F2F2]">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Partner with Us to Shape the Future
            </h2>
            <p className="text-gray-600">
              We believe in collaborative innovation. If you are a technology provider, data scientist, AI researcher,
              or an industry expert with ideas on how to further enhance the global trade ecosystem, we invite you to
              connect with our innovation team.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <CollaborationCard
              icon={<Code className="h-8 w-8 text-[#028475]" />}
              title="Explore API Partnership Opportunities"
              description="Access our Developer Portal to integrate with StreamLnk's powerful platform capabilities."
              buttonText="Visit Developer Portal"
              buttonLink="#"
            />
            <CollaborationCard
              icon={<Zap className="h-8 w-8 text-[#028475]" />}
              title="Join Our Beta Program"
              description="Be among the first to test and provide feedback on our newest features and innovations."
              buttonText="Apply for Beta Access"
              buttonLink="#"
            />
            <CollaborationCard
              icon={<LinkIcon className="h-8 w-8 text-[#028475]" />}
              title="Contact Our Innovation Team"
              description="Have ideas or questions? Reach out directly to our dedicated innovation specialists."
              buttonText="Get in Touch"
              buttonLink="mailto:<EMAIL>"
            />
          </div>
        </div>
      </section>
    </main>
  )
}

function Bell(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9" />
      <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0" />
    </svg>
  )
}
