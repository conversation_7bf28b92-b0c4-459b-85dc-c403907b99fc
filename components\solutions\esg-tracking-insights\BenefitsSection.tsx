import { Leaf, Building, ShieldCheck, Recycle, Users, Globe } from "lucide-react";

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Build a Greener, More Ethical, and Resilient Supply Chain
          </h2>

          <div className="grid md:grid-cols-2 gap-8 mt-10">
            {/* Meet Corporate Sustainability Goals */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start mb-2">
                <Leaf className="h-6 w-6 text-[#028475] mr-3 mt-1" />
                <h3 className="text-xl font-semibold text-[#004235]">Meet Corporate Sustainability Goals</h3>
              </div>
              <p className="text-gray-700 ml-9">
                Actively procure materials that align with your company's ESG targets.
              </p>
            </div>

            {/* Enhance Brand Reputation */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start mb-2">
                <Building className="h-6 w-6 text-[#028475] mr-3 mt-1" />
                <h3 className="text-xl font-semibold text-[#004235]">Enhance Brand Reputation</h3>
              </div>
              <p className="text-gray-700 ml-9">
                Demonstrate a commitment to responsible sourcing to customers, investors, and stakeholders.
              </p>
            </div>

            {/* Mitigate Regulatory & Reputational Risk */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start mb-2">
                <ShieldCheck className="h-6 w-6 text-[#028475] mr-3 mt-1" />
                <h3 className="text-xl font-semibold text-[#004235]">Mitigate Regulatory & Reputational Risk</h3>
              </div>
              <p className="text-gray-700 ml-9">
                Improve visibility into the ESG performance of your supply chain.
              </p>
            </div>

            {/* Drive Innovation */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start mb-2">
                <Recycle className="h-6 w-6 text-[#028475] mr-3 mt-1" />
                <h3 className="text-xl font-semibold text-[#004235]">Drive Innovation</h3>
              </div>
              <p className="text-gray-700 ml-9">
                Encourage suppliers to offer more sustainable products and processes.
              </p>
            </div>

            {/* Improve Stakeholder Engagement */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start mb-2">
                <Users className="h-6 w-6 text-[#028475] mr-3 mt-1" />
                <h3 className="text-xl font-semibold text-[#004235]">Improve Stakeholder Engagement</h3>
              </div>
              <p className="text-gray-700 ml-9">
                Provide transparent data for ESG reporting and communication.
              </p>
            </div>

            {/* Attract ESG-Conscious Customers & Talent */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start mb-2">
                <Globe className="h-6 w-6 text-[#028475] mr-3 mt-1" />
                <h3 className="text-xl font-semibold text-[#004235]">Attract ESG-Conscious Customers & Talent</h3>
              </div>
              <p className="text-gray-700 ml-9">
                Align your operations with growing market and societal expectations.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}