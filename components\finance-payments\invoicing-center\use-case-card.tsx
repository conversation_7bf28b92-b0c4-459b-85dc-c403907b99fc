import type { ReactNode } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { CheckCircle2 } from "lucide-react"

interface UseCaseCardProps {
  icon: ReactNode
  title: string
  description: string
  features: string[]
}

export default function UseCaseCard({ icon, title, description, features }: UseCaseCardProps) {
  return (
    <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors h-full">
      <CardContent className="pt-6 flex flex-col h-full">
        <div className="flex flex-col items-center text-center mb-4">
          <div className="p-3 rounded-full bg-[#004235]/10 mb-3">{icon}</div>
          <h3 className="text-lg font-semibold text-[#004235]">{title}</h3>
          <p className="text-gray-600 mt-2">{description}</p>
        </div>
        <ul className="space-y-2 mt-4">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start gap-2">
              <CheckCircle2 className="h-4 w-4 text-[#028475] mt-0.5 flex-shrink-0" />
              <span className="text-sm text-gray-600">{feature}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  )
}
