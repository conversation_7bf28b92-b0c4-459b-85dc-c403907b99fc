import { Globe, Database, LineChart, Layers } from "lucide-react"

export function WhyPartnerSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <div className="mb-12 text-center">
            <h2 className="text-3xl font-bold text-[#004235] mb-4">
              Why Partner With StreamLnk on Data & API Integration?
            </h2>
            <div className="w-20 h-1 bg-[#028475] mx-auto mb-6"></div>
          </div>

          <div className="prose prose-lg max-w-none text-gray-700">
            <p>
              StreamLnk is more than just a transaction platform; it's a vibrant, data-rich ecosystem continuously
              generating valuable insights from global industrial trade. With APIs embedded across our entire suite of
              portals (E-Stream, StreamFreight, StreamPak, StreamGlobe, MyStreamLnk, MyStreamLnk+), we are ushering in a
              new era of unprecedented connectivity. We link suppliers, logistics providers, buyers, financial
              institutions, and regulatory bodies.
            </p>
            <p>
              If your organization manages risk engines, develops freight management systems, provides compliance tools,
              offers financial services, or specializes in data analytics, StreamLnk's open-access developer tools and
              premium data feeds provide a powerful opportunity. Integrate with us to unlock mutual value, expand your
              service offerings, and reach a targeted industrial audience.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <Database className="h-6 w-6 text-[#028475] mr-3" />
                <h3 className="text-xl font-bold text-[#004235]">Data-Rich Ecosystem</h3>
              </div>
              <p className="text-gray-700">
                Access valuable insights generated from global industrial trade transactions and logistics operations.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <Globe className="h-6 w-6 text-[#028475] mr-3" />
                <h3 className="text-xl font-bold text-[#004235]">Global Reach</h3>
              </div>
              <p className="text-gray-700">
                Connect with a worldwide network of suppliers, buyers, and logistics providers across the industrial
                sector.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <LineChart className="h-6 w-6 text-[#028475] mr-3" />
                <h3 className="text-xl font-bold text-[#004235]">Real-Time Analytics</h3>
              </div>
              <p className="text-gray-700">
                Leverage live data streams for pricing, logistics, compliance, and market trends to enhance your
                offerings.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <Layers className="h-6 w-6 text-[#028475] mr-3" />
                <h3 className="text-xl font-bold text-[#004235]">Integrated Solutions</h3>
              </div>
              <p className="text-gray-700">
                Create seamless experiences by connecting your systems directly with StreamLnk's comprehensive platform.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
