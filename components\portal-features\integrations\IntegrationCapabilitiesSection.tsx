import { Server, Globe, FileText, Briefcase, BarChart, Code, Database, Zap, CheckCircle } from "lucide-react";

export default function IntegrationCapabilitiesSection() {
  return (
    <section className="py-16 md:py-24 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Unify Your Operations: How StreamLnk Connects with Your Key Systems
          </h2>
          <div className="w-20 h-1 bg-[#028475] mx-auto mb-10"></div>
          <p className="text-lg text-gray-700 mb-12 text-center max-w-3xl mx-auto">
            StreamLnk understands the importance of a connected enterprise. We offer robust integration capabilities, primarily through our StreamResources+ DaaS APIs and portal-specific API endpoints, to enable seamless data exchange with your existing business systems:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
            {/* ERP Integration */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <Server className="h-8 w-8 text-[#028475] mr-3" />
                <h3 className="text-xl font-bold text-[#004235]">ERP Integration</h3>
              </div>
              <p className="text-gray-500 text-sm mb-4">(e.g., SAP, Oracle NetSuite, Microsoft Dynamics)</p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
                  <p className="text-gray-700">Order Synchronization: Automatically sync sales orders from StreamLnk to your ERP for fulfillment, inventory management, and financial recording.</p>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
                  <p className="text-gray-700">Invoice & Payment Data Transfer: Push/pull invoice data and payment statuses between StreamLnk and your accounting modules.</p>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
                  <p className="text-gray-700">Product Catalog & Inventory Sync (for Suppliers): Potentially sync product master data and inventory levels from your ERP to E-Stream to ensure consistency (requires careful mapping).</p>
                </li>
              </ul>
            </div>

            {/* SCM Integration */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <Globe className="h-8 w-8 text-[#028475] mr-3" />
                <h3 className="text-xl font-bold text-[#004235]">SCM Integration</h3>
              </div>
              <p className="text-gray-500 text-sm mb-4">(Supply Chain Management)</p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
                  <p className="text-gray-700">Shipment & Logistics Data: Feed real-time shipment tracking data from StreamLnk (StreamFreight, StreamGlobe) into your SCM for a consolidated view of your entire supply chain.</p>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
                  <p className="text-gray-700">Demand & Supply Signals: Integrate StreamLnk's demand forecasts or supplier availability data into your SCM planning tools.</p>
                </li>
              </ul>
            </div>

            {/* Accounting Software Integration */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <FileText className="h-8 w-8 text-[#028475] mr-3" />
                <h3 className="text-xl font-bold text-[#004235]">Accounting Software Integration</h3>
              </div>
              <p className="text-gray-500 text-sm mb-4">(e.g., QuickBooks, Xero)</p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
                  <p className="text-gray-700">Automate the transfer of invoice and payment data from StreamLnk for easier reconciliation and financial reporting.</p>
                </li>
              </ul>
            </div>

            {/* CRM Integration */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <Briefcase className="h-8 w-8 text-[#028475] mr-3" />
                <h3 className="text-xl font-bold text-[#004235]">CRM Integration</h3>
              </div>
              <p className="text-gray-500 text-sm mb-4">(Customer Relationship Management)</p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
                  <p className="text-gray-700">Sync customer data (from MyStreamLnk/MyStreamLnk+) and order history with your CRM for a unified customer view.</p>
                </li>
              </ul>
            </div>

            {/* Logistics & Freight Partner API Integrations */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <Globe className="h-8 w-8 text-[#028475] mr-3" />
                <h3 className="text-xl font-bold text-[#004235]">Logistics & Freight Partner API Integrations</h3>
              </div>
              <p className="text-gray-500 text-sm mb-4">(Outbound from StreamLnk)</p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
                  <p className="text-gray-700">StreamLnk itself integrates deeply with carriers (StreamGlobe APIs), freight visibility platforms (e.g., Project44 via StreamFreight), and customs systems to power its own logistics services. This ensures the data within StreamLnk is rich and real-time.</p>
                </li>
              </ul>
            </div>

            {/* BI Tool Integration */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <BarChart className="h-8 w-8 text-[#028475] mr-3" />
                <h3 className="text-xl font-bold text-[#004235]">BI Tool Integration</h3>
              </div>
              <p className="text-gray-500 text-sm mb-4">(Business Intelligence)</p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
                  <p className="text-gray-700">Export aggregated data from StreamResources+ or connect directly via API to feed your internal BI platforms (Tableau, Power BI, Snowflake) for custom analytics.</p>
                </li>
              </ul>
            </div>
          </div>

          {/* Integration Methods */}
          <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-200 mb-12">
            <h3 className="text-2xl font-bold text-[#004235] mb-6">Integration Methods</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-start">
                <Code className="h-6 w-6 text-[#028475] mt-1 mr-4 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-2">RESTful APIs</h4>
                  <p className="text-gray-700">Secure, well-documented APIs for pulling and pushing data (Primarily via StreamResources+ DaaS Tiers).</p>
                </div>
              </div>
              <div className="flex items-start">
                <Database className="h-6 w-6 text-[#028475] mt-1 mr-4 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-2">Standardized Data Formats</h4>
                  <p className="text-gray-700">Support for common formats like JSON, XML, CSV for data exchange.</p>
                </div>
              </div>
              <div className="flex items-start">
                <Zap className="h-6 w-6 text-[#028475] mt-1 mr-4 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-2">Webhook Notifications</h4>
                  <p className="text-gray-700">Real-time event notifications from StreamLnk to trigger actions in your connected systems.</p>
                </div>
              </div>
              <div className="flex items-start">
                <Server className="h-6 w-6 text-[#028475] mt-1 mr-4 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-2">Developer Portal & Support</h4>
                  <p className="text-gray-700">Comprehensive API documentation, sandbox environments, and dedicated technical support for integration partners and enterprise clients.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}