import { But<PERSON> } from "@/components/ui/button"
import { ArrowR<PERSON> } from "lucide-react"
import Link from "next/link"

export function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl">
          {/* Optional: Re-add if a specific badge/pre-title is desired, styled consistently */}
          {/* <div className=\"flex items-center mb-4\">
            <div className=\"w-10 h-1 bg-[#028475] mr-3\"></div>
            <span className=\"text-[#028475] font-medium\">PARTNER WITH US</span>
          </div> */}
          <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
            Your Expertise, Our Network: Partner with StreamPak
          </h1>
          <p className="text-xl md:text-2xl text-[#028475] mb-8">
            If you provide professional packaging, repackaging, labeling, or third-party warehousing services,
            StreamLnk connects your capabilities to upstream and downstream supply chain needs across the globe.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button size="lg" className="bg-[#004235] hover:bg-[#028475] text-white transition-colors" asChild>
              <Link href="#apply-streampak"> {/* Assuming a link target */} 
                Apply to Join StreamPak <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white transition-colors"
              asChild
            >
              <Link href="#learn-more">Learn More</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
