import { Clock, CheckCircle2, AlertCircle } from "lucide-react"

export default function WhyUploadSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Why Upload Proof of Payment?</h2>
          <p className="text-gray-600 max-w-3xl">
            Uploading your proof of payment provides several benefits to ensure smooth order processing.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-[#f3f4f6] hover:border-[#028475] transition-colors">
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="p-3 rounded-full bg-[#004235]/10">
                <Clock className="h-8 w-8 text-[#028475]" />
              </div>
              <h3 className="text-xl font-semibold text-[#004235]">Accelerates Shipment Release</h3>
              <p className="text-gray-600">Orders are flagged for fulfillment upon POP upload.</p>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-[#f3f4f6] hover:border-[#028475] transition-colors">
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="p-3 rounded-full bg-[#004235]/10">
                <CheckCircle2 className="h-8 w-8 text-[#028475]" />
              </div>
              <h3 className="text-xl font-semibold text-[#004235]">Improves Payment Visibility</h3>
              <p className="text-gray-600">
                Buyers, suppliers, and StreamLnk support teams get real-time confirmation.
              </p>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-[#f3f4f6] hover:border-[#028475] transition-colors">
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="p-3 rounded-full bg-[#004235]/10">
                <AlertCircle className="h-8 w-8 text-[#028475]" />
              </div>
              <h3 className="text-xl font-semibold text-[#004235]">Avoids Delays</h3>
              <p className="text-gray-600">
                Missing POPs can delay customs filing, freight bookings, or warehouse dispatch.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}