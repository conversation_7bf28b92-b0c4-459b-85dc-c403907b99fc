"use client";

import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight, Download } from 'lucide-react'; // Changed ChevronRight to ArrowRight, Download might be removed or replaced

export default function AccessSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            Get Clarity in Volatile Markets.
          </h2>
          <p className="text-xl text-gray-700 mb-8">
            StreamIndex™: Access premium data and analytics. High-level insights may also be in your StreamLnk portal.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              asChild
              size="lg"
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
            >
              <Link href="/contact?subject=StreamIndex%20Demo%20Request">
                REQUEST DEMO <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              asChild
              variant="outline"
              size="lg"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
            >
              <Link href="/resources/streamresources-plus#subscription-plans"> {/* Assuming a section ID for plans */}
                COMPARE PLANS
              </Link>
            </Button>
          </div>
          <div className="mt-6">
            <Button
              asChild
              variant="link"
              className="text-[#028475] hover:text-[#004235]"
            >
              <Link href="/resources/sample-streamindex-brief.pdf" target="_blank" rel="noopener noreferrer">
                DOWNLOAD BRIEF <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}