"use client";

import { Target, FileText, ShieldCheck, TrendingUp, Users, CheckCircle, Zap } from "lucide-react"; // Added relevant icons

export default function BenefitsSection() {
  const benefits = [
    {
      icon: <Target className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Achieve Sustainability Targets",
      description: "Actively manage and improve the ESG performance of your supply chain."
    },
    {
      icon: <FileText className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Simplify ESG Reporting",
      description: "Access readily available data and downloadable reports for your internal and external disclosures (e.g., CSRD, sustainability reports)."
    },
    {
      icon: <Users className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Enhance Brand Reputation",
      description: "Demonstrate a credible commitment to responsible and sustainable business practices, building stakeholder trust."
    },
    {
      icon: <ShieldCheck className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Mitigate ESG-Related Risks",
      description: "Improve visibility into environmental and social risks within your supply chain."
    },
    {
      icon: <TrendingUp className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Attract Investors & Customers",
      description: "Align your operations with the growing demand for sustainable businesses from ESG-focused stakeholders."
    },
    {
      icon: <Zap className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Drive Positive Impact",
      description: "Contribute to a more environmentally and socially responsible global industrial ecosystem."
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Drive Sustainability, Enhance Transparency, Meet Reporting Needs
          </h2>
          <p className="text-xl text-gray-700 mb-12 text-center">
            Benefits of Using StreamLnk's ESG Reporting Tools
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="bg-[#F2F2F2] p-6 rounded-lg text-center flex flex-col items-center shadow-md">
                {benefit.icon}
                <h3 className="font-semibold text-[#004235] text-lg mb-2">{benefit.title}</h3>
                <p className="text-gray-600 text-sm">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}