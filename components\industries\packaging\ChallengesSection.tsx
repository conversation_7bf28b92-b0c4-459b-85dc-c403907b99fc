"use client";

import { CheckCircle } from "lucide-react";

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Facing Pressure in a Fast-Moving Packaging World?
          </h2>
          <p className="text-lg text-gray-700 mb-4 text-center">
            Key Challenges in the Packaging Material Supply Chain
          </p>
          <p className="text-lg text-gray-700 mb-10 text-center">
            The packaging industry is critical for almost every other sector, demanding agility, cost-efficiency, and increasing sustainability, while navigating:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Diverse Material Sourcing</h3>
                  <p className="text-gray-600">Managing procurement for a wide array of materials (polymers like PET, PE, PP; films; adhesives; paper; recycled inputs).</p>
                </div>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Price Volatility of Raw Materials</h3>
                  <p className="text-gray-600">Impact of commodity fluctuations on packaging costs.</p>
                </div>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Just-in-Time Demands</h3>
                  <p className="text-gray-600">Converters and CPGs require timely delivery of packaging materials to meet production schedules.</p>
                </div>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Sustainability & Regulatory Pressures</h3>
                  <p className="text-gray-600">Growing demand for recycled content, compostable materials, and compliance with extended producer responsibility (EPR) schemes.</p>
                </div>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Quality Control & Consistency</h3>
                  <p className="text-gray-600">Ensuring materials meet specific performance and safety standards (e.g., food-grade packaging).</p>
                </div>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Fragmented Supplier Base</h3>
                  <p className="text-gray-600">Fragmented Supplier Base for Niche or Sustainable Materials.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}