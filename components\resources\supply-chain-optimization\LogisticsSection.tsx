"use client";

import { Truck, Globe, PackageCheck, Route } from 'lucide-react';

export default function LogisticsSection() {
  return (
    <section className="py-12 md:py-20 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-4 text-center">
            Intelligent Logistics & Fulfillment Orchestration
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center leading-relaxed">
            Complex global logistics often suffer from a lack of real-time visibility, leading to inefficient handoffs between partners and unpredictable, often inflated, costs. Streamlining this critical phase is key to overall supply chain performance.
          </p>

          <div className="bg-white p-8 rounded-xl shadow-lg">
            <h3 className="text-2xl font-semibold text-[#004235] mb-3">
              StreamLnk Solution: Unified Logistics Command
            </h3>
            <p className="text-gray-700 mb-6 leading-relaxed">
              StreamLnk's integrated logistics portals (StreamFreight, StreamGlobe, StreamGlobe+, StreamPak) provide a unified command center for all your shipping and fulfillment needs. Our powerful AI engine optimizes routes, suggests the most efficient carriers, and automates critical document flow. Real-time tracking offers complete end-to-end visibility, simplifying customs clearance and proactive issue resolution.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="flex items-start">
                <Truck className="h-8 w-8 text-[#028475] mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Reduced Freight Costs</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">Optimize routes and carriers for better rates.</p>
                </div>
              </div>
              <div className="flex items-start">
                <Route className="h-8 w-8 text-[#028475] mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Shorter Transit Times</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">Minimize delays with efficient planning and tracking.</p>
                </div>
              </div>
              <div className="flex items-start">
                <PackageCheck className="h-8 w-8 text-[#028475] mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Improved JIT Delivery</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">Enhance reliability for just-in-time manufacturing.</p>
                </div>
              </div>
              <div className="flex items-start">
                <Globe className="h-8 w-8 text-[#028475] mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Streamlined Compliance</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">Automate documentation for smoother customs clearance.</p>
                </div>
              </div>
            </div>
            <p className="text-md text-gray-800 font-medium leading-relaxed">
              The outcome is significantly reduced freight costs, shorter and more predictable transit times, minimized delays, improved Just-In-Time (JIT) delivery performance, and streamlined global trade compliance.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}