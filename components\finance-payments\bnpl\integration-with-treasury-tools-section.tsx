import Image from "next/image";
import { BarChart3, Calendar, FileCheck } from "lucide-react";
import IntegrationCard from "@/components/finance-payments/bnpl/integration-card";

export default function IntegrationWithTreasuryToolsSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="grid gap-12 lg:grid-cols-2 items-center">
          <div>
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-6">Integration with Treasury Tools</h2>
            <p className="text-gray-600 mb-8">
              Manage all your BNPL activities seamlessly through our comprehensive Treasury Dashboard.
            </p>

            <div className="space-y-4">
              <IntegrationCard
                icon={<BarChart3 className="h-6 w-6 text-[#028475]" />}
                title="Comprehensive Dashboard"
                description="View all BNPL activity in the Treasury Dashboard"
              />
              <IntegrationCard
                icon={<Calendar className="h-6 w-6 text-[#028475]" />}
                title="Payment Monitoring"
                description="Monitor upcoming due dates, outstanding balances, and payment reminders"
              />
              <IntegrationCard
                icon={<FileCheck className="h-6 w-6 text-[#028475]" />}
                title="Export Capabilities"
                description="Export BNPL invoices and summaries for your ERP or accounting system"
              />
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/finance-payments/Graphic of comprehensive treasury dashboard monitoring and managing BNPL activity.png"
              alt="Treasury dashboard interface"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
}