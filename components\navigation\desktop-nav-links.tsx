"use client";

import { usePathname } from "next/navigation";
import clsx from "clsx";
import Link from "next/link";
import * as Icons from "lucide-react";
import { MegaMenu, MegaMenuTrigger } from "../ui/mega-menu";
import { RenderMegaMenuContent } from "./mega-menu-content";
import { MegaMenuNavItem } from "./types";

interface DesktopNavLinksProps {
  variant: 'header' | 'subnav';
  navigationItems: MegaMenuNavItem[];
  openMegaMenu: string | null;
  setOpenMegaMenu: (id: string | null) => void;
}

export function DesktopNavLinks({ 
  variant, 
  navigationItems, 
  openMegaMenu, 
  setOpenMegaMenu 
}: DesktopNavLinksProps) {
  const pathname = usePathname();
  // Create a unique identifier for each navigation instance
  const navId = variant === 'header' ? 'header-nav' : 'sticky-nav';

  return (
    <div className="hidden md:flex items-center gap-8 h-full" data-nav-id={navId}>
      {navigationItems.map((item) => {
        const isItemActive = openMegaMenu === `${navId}-${item.id}`;
        const isPageActive = pathname.startsWith(`/${item.id}`) ||
                          (item.content.type === 'custom' && item.content.sidebarLinks.some(link => link.href === pathname)) ||
                          (item.content.type === 'custom' && item.content.mainColumns.some(col => col.items.some(i => i.href === pathname))) ||
                          (item.content.type === 'custom' && item.content.bottomGrid?.sections.some(sec => sec.links.some(link => link.href === pathname)));

        return (
          <MegaMenu
            key={`${navId}-${item.id}`}
            open={isItemActive}
            onOpenChange={(open) => {
              // Close any other open menus before opening this one
              if (open) {
                setOpenMegaMenu(`${navId}-${item.id}`);
              } else if (openMegaMenu === `${navId}-${item.id}`) {
                setOpenMegaMenu(null);
              }
            }}
          >
            <MegaMenuTrigger asChild>
              <button
                type="button"
                className={clsx(
                  "relative flex items-center gap-1 text-sm font-medium transition-colors py-5 outline-none focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-[#004235] h-full",
                  "after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[3px] after:w-full after:bg-[#004235] after:transition-transform after:duration-300",
                  (isItemActive || (isPageActive && !isItemActive && !openMegaMenu)) ? "after:scale-x-100" : "after:scale-x-0 hover:after:scale-x-100",
                  variant === 'header' ?
                    (isItemActive || isPageActive ? "text-white after:bg-white" : "text-white hover:text-opacity-80 after:bg-white") :
                    (isItemActive || isPageActive ? "text-[#004235]" : "text-gray-800 hover:text-[#004235]")
                )}
              >
                <span>{item.trigger}</span>
                <Icons.ChevronDown className={clsx("h-4 w-4 transition-transform duration-200", isItemActive && "rotate-180")} />
              </button>
            </MegaMenuTrigger>
            <RenderMegaMenuContent content={item.content} />
          </MegaMenu>
        );
      })}
    </div>
  );
}