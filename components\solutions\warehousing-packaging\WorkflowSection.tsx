"use client"

export default function WorkflowSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Ensuring Your Products Are Market-Ready, Every Time
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            From an "Unfinished" Product to Final Delivery:
          </p>
          
          <div className="space-y-6">
            <div className="flex items-start">
              <div className="bg-[#004235] text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 flex-shrink-0 mt-1">1</div>
              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">Order Placed/Product Flagged</h3>
                <p className="text-gray-700">Buyer orders a product that AI identifies as needing packaging (e.g., bulk polymer requiring 25kg bags).</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="bg-[#004235] text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 flex-shrink-0 mt-1">2</div>
              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">StreamPak Assignment</h3>
                <p className="text-gray-700">Job is routed to a qualified packaging partner near the optimal point in the supply chain.</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="bg-[#004235] text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 flex-shrink-0 mt-1">3</div>
              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">Goods Transferred</h3>
                <p className="text-gray-700">StreamFreight coordinates movement of bulk material to the StreamPak partner.</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="bg-[#004235] text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 flex-shrink-0 mt-1">4</div>
              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">Packaging Executed</h3>
                <p className="text-gray-700">Partner receives goods (verifies quantity, reports discrepancies), performs packaging per specifications, updates status in StreamPak.</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="bg-[#004235] text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 flex-shrink-0 mt-1">5</div>
              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">Quality Assured</h3>
                <p className="text-gray-700">QC reports and completion evidence uploaded.</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="bg-[#004235] text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 flex-shrink-0 mt-1">6</div>
              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">Ready for Final Dispatch</h3>
                <p className="text-gray-700">Packaged goods are now "Finished" and ready for onward shipment to the customer via StreamFreight/StreamGlobe.</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="bg-[#004235] text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 flex-shrink-0 mt-1">7</div>
              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">Full Visibility</h3>
                <p className="text-gray-700">Supplier and Buyer see updated product status and ETAs in their respective portals.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}