"use client";

import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Building Sustainable Futures: ESG Reporting & Responsible Sourcing, Powered by StreamLnk
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Meet growing stakeholder demands and contribute to a more sustainable world. StreamLnk provides the tools and data insights to help you track, manage, and report on the Environmental, Social, and Governance (ESG) impact of your industrial supply chain.
            </p>
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white px-6"
              size="lg"
              asChild
            >
              <Link href="/request-demo?solution=esg-reporting&source=resource-hero">
                DISCOVER ESG SOLUTIONS
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/resource-esg-reporting-hero.webp" // Placeholder - suggest user to replace
              alt="ESG Reporting and Sustainable Sourcing with StreamLnk"
              fill
              className="object-cover"
              priority
            />
            {/* TODO: Consider adding a more relevant image or illustration */}
          </div>
        </div>
      </div>
    </section>
  );
}