import React from 'react';

interface TimelineItemProps {
  number: number;
  title: string;
  description: string;
}

export function TimelineItem({ number, title, description }: TimelineItemProps) {
  return (
    <div className="flex flex-col md:flex-row items-center gap-6 bg-[#F2F2F2] p-6 rounded-lg">
      <div className="bg-[#004235] text-white w-12 h-12 rounded-full flex items-center justify-center font-bold text-xl flex-shrink-0">
        {number}
      </div>
      <div>
        <h3 className="text-xl font-bold text-[#004235] mb-2">{title}</h3>
        <p className="text-gray-700">{description}</p>
      </div>
    </div>
  );
}