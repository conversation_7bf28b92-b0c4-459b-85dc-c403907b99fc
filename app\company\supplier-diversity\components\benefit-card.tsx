import type { ReactNode } from "react"
import { Card, CardContent } from "@/components/ui/card"

interface BenefitCardProps {
  icon: ReactNode
  title: string
  description: string
}

export default function BenefitCard({ icon, title, description }: BenefitCardProps) {
  return (
    <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors h-full">
      <CardContent className="pt-6 flex flex-col items-center text-center space-y-4">
        <div className="p-3 rounded-full bg-[#004235]/10">{icon}</div>
        <h3 className="text-lg font-semibold text-[#004235]">{title}</h3>
        <p className="text-gray-600">{description}</p>
      </CardContent>
    </Card>
  )
}
