"use client";

import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

const faqs = [
  {
    question: "Can I change my plan later?",
    answer: "Yes, you can typically upgrade your plan at any time. Downgrades are usually processed at the end of your current billing cycle."
  },
  {
    question: "Is there a free trial available?",
    answer: "We may offer limited-time free trials for certain tiers. Please check our current promotions or contact sales."
  },
  {
    question: "How is the data anonymized and secured?",
    answer: "All data used in StreamResources+ is rigorously anonymized and aggregated to protect individual user and company confidentiality, adhering to strict data privacy and security protocols."
  },
  {
    question: "What kind of support is offered for subscribers?",
    answer: "All subscribers receive access to our standard support channels. Professional Analyst and Enterprise tiers include more dedicated support options."
  }
];

export default function FaqSection() {
  return (
    <section className="py-16 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-[#004235] mb-4">
              Your Questions Answered
            </h2>
            <p className="text-xl text-[#028475] font-semibold">
              Frequently Asked Questions about Subscriptions
            </p>
          </div>
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => (
              <AccordionItem value={`item-${index}`} key={index} className="border-b border-gray-300">
                <AccordionTrigger className="text-left hover:no-underline py-4 text-lg font-medium text-[#004235] hover:text-[#028475]">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="pt-2 pb-4 text-gray-700">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  );
}