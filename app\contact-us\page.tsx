// app/contact-us/page.tsx
import { ContactForm } from "@/components/contact/contact-form";
import { ContactInfoCard, ContactInfoItem } from "@/components/contact/contact-info-card";
import { Mail, Phone, Building, Users, Newspaper, BarChart2, ShieldCheck, Briefcase, FileText, CreditCard, HelpCircle } from 'lucide-react';
import { MainNav } from "@/components/main-nav";
import { MainFooter } from "@/components/main-footer";

const contactInfoItems: ContactInfoItem[] = [
  {
    title: "General Inquiries:",
    email: "<EMAIL>",
    description: "For general inquiries and initial contact",
    icon: <Mail className="h-6 w-6 text-primary" />
  },
  {
    title: "Customer Support:",
    email: "<EMAIL>",
    description: "For customers seeking help with orders, tracking, or technical assistance.",
    icon: <HelpCircle className="h-6 w-6 text-primary" />
  },
  {
    title: "Sales & Partnerships:",
    email: "<EMAIL>",
    description: "For inquiries regarding new business opportunities, partnerships, or product offerings",
    icon: <Users className="h-6 w-6 text-primary" />
  },
  {
    title: "Supplier Support:",
    email: "<EMAIL>",
    description: "For suppliers needing assistance with registration, product listings, or technical support.",
    icon: <Building className="h-6 w-6 text-primary" />
  },
  {
    title: "Media & Press:",
    email: "<EMAIL>",
    description: "For media inquiries, press releases, and interviews.",
    icon: <Newspaper className="h-6 w-6 text-primary" />
  },
  {
    title: "Marketing & Advertising:",
    email: "<EMAIL>",
    description: "For marketing-related inquiries, including advertisements, collaborations, or events",
    icon: <BarChart2 className="h-6 w-6 text-primary" />
  },
  {
    title: "Compliance & Legal:",
    email: "<EMAIL>",
    description: "For compliance-related questions, legal matters, and regulatory information.",
    icon: <ShieldCheck className="h-6 w-6 text-primary" />
  },
  {
    title: "Technical Support:",
    email: "<EMAIL>",
    description: "For issues related to the platform, website, or system.",
    icon: <Phone className="h-6 w-6 text-primary" /> // Placeholder, consider specific tech icon
  },
  {
    title: "Billing & Payments:",
    email: "<EMAIL>",
    description: "For payment-related inquiries, invoices, and billing issues.",
    icon: <CreditCard className="h-6 w-6 text-primary" />
  },
  {
    title: "Careers:",
    email: "<EMAIL>",
    description: "For job applications, internships, and career-related inquiries.",
    icon: <Briefcase className="h-6 w-6 text-primary" />
  },
];

export default function ContactUsPage() {
  return (
    <div className="bg-white min-h-screen">
      <MainNav />
      {/* Header Section */}
      <section className="py-20 md:py-28 text-center bg-[#f3f4f6]">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl md:text-5xl font-extrabold text-[#004235] mb-4">
            Contact StreamLnk
          </h1>
          <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto">
            We’re here to assist with any inquiries, partnership opportunities, or customer support needs. Get in touch with us through the following channels.
          </p>
        </div>
      </section>

      {/* Main Content Section */}
      <section className="py-16 md:py-24">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-3 gap-12 lg:gap-16 items-start">
            {/* Contact Form Section (Left - 2 columns wide on lg) */}
            <div className="lg:col-span-2 bg-white p-8 md:p-12 rounded-xl shadow-lg">
              <ContactForm />
            </div>

            {/* Contact Info Section (Right - 1 column wide on lg) */}
            <div className="space-y-8">
              {contactInfoItems.map((item) => (
                <ContactInfoCard key={item.title} item={item} />
              ))}
            </div>
          </div>
        </div>
      </section>
      <MainFooter />
    </div>
  );
}