// Placeholder for fetching future plans content based on slug
// In a real app, this data would come from a CMS or database
const getFuturePlansContent = async (slug: string) => {
  // Simulate fetching data
  await new Promise(resolve => setTimeout(resolve, 100));
  // Example content structure - adapt as needed
  if (slug === "sample-case-study-1") {
    return {
      title: "Looking Ahead: Deepening the Partnership with StreamLnk",
      paragraphs: [
        "Building on the success achieved, Acme Corp plans to further leverage StreamLnk's capabilities. They are currently exploring the integration of StreamLnk's Supplier Portal to enhance collaboration with their key vendors and gain deeper insights into supplier performance.",
        "Additionally, Acme Corp is interested in piloting StreamLnk's advanced AI-powered demand forecasting module to improve inventory management and reduce stockouts. They see StreamLnk not just as a solution provider, but as a long-term strategic partner in their journey towards supply chain excellence and digital transformation."
      ]
    };
  }
  // Fallback or default content
  return {
    title: "Looking Ahead / Future with StreamLnk",
    paragraphs: [
      "This section outlines how the client plans to continue using or expand their use of StreamLnk. It could involve adopting new modules, integrating with other systems, or rolling out the platform to new departments or regions.",
      "Highlighting future plans demonstrates the client's ongoing commitment and satisfaction with StreamLnk, reinforcing the platform's value and adaptability."
    ]
  };
};

interface FuturePlansSectionProps {
  caseStudySlug: string;
}

const FuturePlansSection: React.FC<FuturePlansSectionProps> = async ({ caseStudySlug }) => {
  const content = await getFuturePlansContent(caseStudySlug);

  return (
    <section className="py-12 md:py-16 lg:py-20 bg-gray-50">
      <div className="container mx-auto px-4 md:px-6">
        <h2 className="text-3xl md:text-4xl font-semibold text-[#004235] mb-8 text-center">
          {content.title}
        </h2>
        <div className="max-w-3xl mx-auto space-y-6 text-gray-700 leading-relaxed">
          {content.paragraphs.map((paragraph, index) => (
            <p key={index} className="text-lg">
              {paragraph}
            </p>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FuturePlansSection;