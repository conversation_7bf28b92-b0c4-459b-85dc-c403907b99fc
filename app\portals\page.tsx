import Image from "next/image";
import Link from "next/link";
import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import { Button } from "@/components/ui/button";
import { Accordion } from "@/components/ui/accordion";
import PortalItem from "@/components/portals/portal-item";
import {
  ShoppingCart,
  Network,
  Factory,
  Package,
  Truck,
  Ship,
  Stamp,
  BarChart,
  Warehouse,
} from "lucide-react";
import { redirect } from 'next/navigation';

export default function PortalsPage() {
  redirect('/business-account');
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      {/* Hero Section */}
      <section className="bg-[#F2F2F2] text-[#004235] py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">StreamLnk Portals – Your Dedicated Gateway to Global Industrial Trade</h1>
            <p className="text-xl mb-8">
              The StreamLnk Ecosystem: Specialized Portals, Unified Power.
              Access the tools and information you need, exactly when you need them.
              StreamLnk's suite of interconnected portals provides a tailored experience for every participant in the global industrial supply chain, all powered by a single, intelligent backend.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-[#004235] hover:bg-[#023025] text-white px-6 py-5">
                DISCOVER WHICH PORTAL IS RIGHT FOR YOU
              </Button>
              <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#F2F2F2] hover:text-[#023025] px-6 py-5">
                REQUEST A GENERAL PLATFORM DEMO
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Specialization Benefits Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center mb-12">
            <h2 className="text-3xl font-bold text-[#004235] mb-4">One Platform, Many Gateways – The Power of Specialization</h2>
            <h3 className="text-xl font-semibold text-[#028475] mb-6">Why Specialized Portals? Tailored Experiences for Optimal Efficiency.</h3>
            <p className="text-lg text-gray-700 mb-8">
              The global industrial supply chain involves diverse stakeholders with unique needs, workflows, and priorities. A one-size-fits-all approach doesn't work. StreamLnk addresses this by offering:
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <h4 className="text-xl font-semibold text-[#004235] mb-3">Role-Specific Functionality</h4>
              <p className="text-gray-700">
                Each portal is designed with features and tools specifically relevant to the tasks and goals of its primary users.
              </p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <h4 className="text-xl font-semibold text-[#004235] mb-3">Intuitive User Experience (UX)</h4>
              <p className="text-gray-700">
                Interfaces are optimized for the workflows of buyers, suppliers, logistics providers, etc., minimizing clutter and maximizing ease of use.
              </p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <h4 className="text-xl font-semibold text-[#004235] mb-3">Targeted Information Delivery</h4>
              <p className="text-gray-700">
                Users see the data and alerts most pertinent to their role, ensuring focus and actionable insights.
              </p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <h4 className="text-xl font-semibold text-[#004235] mb-3">Seamless Interconnectivity</h4>
              <p className="text-gray-700">
                While specialized, all portals are deeply integrated, ensuring smooth data flow and automated handoffs across the entire ecosystem.
              </p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <h4 className="text-xl font-semibold text-[#004235] mb-3">Secure, Permission-Based Access</h4>
              <p className="text-gray-700">
                Users only access the data and functionalities authorized for their role, ensuring security and data privacy.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Portal Ecosystem Section */}
      <section className="py-16 bg-[#F2F2F2]">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center mb-12">
            <h2 className="text-3xl font-bold text-[#004235] mb-4">Explore the StreamLnk Portal Ecosystem</h2>
            <p className="text-xl text-[#028475]">Find Your Dedicated Gateway to Smarter Industrial Trade</p>
          </div>

          {/* Buyers Section */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-[#004235] mb-6 border-b-2 border-[#028475] pb-2 inline-block">For Buyers & Their Representatives</h3>
            
            <Accordion type="single" collapsible className="w-full space-y-6">
              <PortalItem
                id="mystreamlnk"
                title="MyStreamLnk (Customer Portal)"
                subtitle="For: Manufacturers, Procurement Teams, End-Users"
                icon={<ShoppingCart className="h-8 w-8" />}
                color="#004235"
                imageSrc="/images/portals/my-stream-lnk.webp"
                imageAlt="MyStreamLnk Customer Portal"
              >
                <div className="space-y-4">
                  <p className="text-gray-700">
                    <span className="font-semibold">Key Functions:</span> Discover products, request quotes, place orders, track shipments, manage documents & payments, access ESG insights & iScore™ summaries.
                  </p>
                  <div className="flex flex-wrap gap-4">
                    <Link href="/our-divisions/ecommerce" className="text-[#028475] hover:underline font-medium">Learn More about MyStreamLnk</Link>
                    <Link href="/login" className="text-[#028475] hover:underline font-medium">Login</Link>
                  </div>
                </div>
              </PortalItem>

              <PortalItem
                id="mystreamlnkplus"
                title="MyStreamLnk+ (Agent & Distributor Portal)"
                subtitle="For: Independent Sales Agents, Regional Distributors"
                icon={<Network className="h-8 w-8" />}
                color="#004235"
                imageSrc="/images/portals/my-stream-lnk-plus.webp"
                imageAlt="MyStreamLnk+ Agent Portal"
              >
                <div className="space-y-4">
                  <p className="text-gray-700">
                    <span className="font-semibold">Key Functions:</span> Manage customer portfolios, facilitate quotes & orders, track commissions & performance, access team tools & resources.
                  </p>
                  <div className="flex flex-wrap gap-4">
                    <Link href="/partner/partner-with-us" className="text-[#028475] hover:underline font-medium">Learn More about MyStreamLnk+</Link>
                    <Link href="/login" className="text-[#028475] hover:underline font-medium">Login</Link>
                  </div>
                </div>
              </PortalItem>
            </Accordion>
          </div>

          {/* Suppliers Section */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-[#004235] mb-6 border-b-2 border-[#028475] pb-2 inline-block">For Suppliers & Production Partners</h3>
            
            <Accordion type="single" collapsible className="w-full space-y-6">
              <PortalItem
                id="estream"
                title="E-Stream (Supplier Portal)"
                subtitle="For: Polymer Producers, Chemical Manufacturers, Industrial Material Suppliers"
                icon={<Factory className="h-8 w-8" />}
                color="#004235"
                imageSrc="/images/portals/e-stream.webp"
                imageAlt="E-Stream Supplier Portal"
              >
                <div className="space-y-4">
                  <p className="text-gray-700">
                    <span className="font-semibold">Key Functions:</span> List products, manage inventory & pricing (with StreamIndex™ assist), receive RFQs/orders, participate in auctions, coordinate fulfillment.
                  </p>
                  <div className="flex flex-wrap gap-4">
                    <Link href="/partner/e-stream-supplier" className="text-[#028475] hover:underline font-medium">Learn More about E-Stream</Link>
                    <Link href="/login" className="text-[#028475] hover:underline font-medium">Login</Link>
                  </div>
                </div>
              </PortalItem>

              <PortalItem
                id="streampak"
                title="StreamPak (Packaging & Warehousing Portal)"
                subtitle="For: Third-Party Packaging Companies, Warehouse Operators"
                icon={<Warehouse className="h-8 w-8" />}
                color="#004235"
                imageSrc="/images/portals/stream-pak.webp"
                imageAlt="StreamPak Packaging Portal"
              >
                <div className="space-y-4">
                  <p className="text-gray-700">
                    <span className="font-semibold">Key Functions:</span> Manage packaging/storage assignments, update inventory & job status, ensure compliance, report shortages.
                  </p>
                  <div className="flex flex-wrap gap-4">
                    <Link href="/partner/streampak-partner" className="text-[#028475] hover:underline font-medium">Learn More about StreamPak</Link>
                    <Link href="/login" className="text-[#028475] hover:underline font-medium">Login</Link>
                  </div>
                </div>
              </PortalItem>
            </Accordion>
          </div>

          {/* Logistics Section */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-[#004235] mb-6 border-b-2 border-[#028475] pb-2 inline-block">For Logistics & Compliance Facilitators</h3>
            
            <Accordion type="single" collapsible className="w-full space-y-6">
              <PortalItem
                id="streamfreight"
                title="StreamFreight (Land Freight Portal)"
                subtitle="For: Trucking Companies, Rail Operators, Independent Owner-Operators"
                icon={<Truck className="h-8 w-8" />}
                color="#004235"
                imageSrc="/images/portals/stream-freight.webp"
                imageAlt="StreamFreight Land Portal"
              >
                <div className="space-y-4">
                  <p className="text-gray-700">
                    <span className="font-semibold">Key Functions:</span> Bid on land freight jobs, manage assignments, update shipment status, digital PODs, track payments.
                  </p>
                  <div className="flex flex-wrap gap-4">
                    <Link href="/partner/stream-freight" className="text-[#028475] hover:underline font-medium">Learn More about StreamFreight</Link>
                    <Link href="/login" className="text-[#028475] hover:underline font-medium">Login</Link>
                  </div>
                </div>
              </PortalItem>

              <PortalItem
                id="streamglobe"
                title="StreamGlobe (Sea Freight Carrier Hub)"
                subtitle="For: Ocean Freight Carriers, NVOCCs (Primarily API Integration)"
                icon={<Ship className="h-8 w-8" />}
                color="#004235"
                imageSrc="/images/portals/stream-globe.webp"
                imageAlt="StreamGlobe Sea Freight Portal"
              >
                <div className="space-y-4">
                  <p className="text-gray-700">
                    <span className="font-semibold">Key Functions:</span> Exchange vessel schedules, booking data, container tracking, eB/L information. Light portal for API management.
                  </p>
                  <div className="flex flex-wrap gap-4">
                    <Link href="/partner/api-integration" className="text-[#028475] hover:underline font-medium">Learn More about StreamGlobe APIs</Link>
                  </div>
                </div>
              </PortalItem>

              <PortalItem
                id="streamglobeplus"
                title="StreamGlobe+ (Customs Clearance Agent Portal)"
                subtitle="For: Licensed Customs Clearance Agents & Brokers"
                icon={<Stamp className="h-8 w-8" />}
                color="#004235"
                imageSrc="/images/portals/stream-globe-plus.webp"
                imageAlt="StreamGlobe+ Customs Portal"
              >
                <div className="space-y-4">
                  <p className="text-gray-700">
                    <span className="font-semibold">Key Functions:</span> Manage customs assignments, automated document delivery, real-time clearance status updates, POA management.
                  </p>
                  <div className="flex flex-wrap gap-4">
                    <Link href="/partner/streamglobe-agent" className="text-[#028475] hover:underline font-medium">Learn More about StreamGlobe+</Link>
                    <Link href="/login" className="text-[#028475] hover:underline font-medium">Login</Link>
                  </div>
                </div>
              </PortalItem>
            </Accordion>
          </div>

          {/* Intelligence Section */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-[#004235] mb-6 border-b-2 border-[#028475] pb-2 inline-block">For Intelligence & Strategic Insights</h3>
            
            <Accordion type="single" collapsible className="w-full space-y-6">
              <PortalItem
                id="streamresourcesplus"
                title="StreamResources+ (Data & Analytics Portal)"
                subtitle="For: Subscribers (Suppliers, Buyers, Financial Institutions, Analysts, Governments), Internal StreamLnk Teams"
                icon={<BarChart className="h-8 w-8" />}
                color="#004235"
                imageSrc="/images/portals/stream-resources-plus.webp"
                imageAlt="StreamResources+ Analytics Portal"
              >
                <div className="space-y-4">
                  <p className="text-gray-700">
                    <span className="font-semibold">Key Functions:</span> Access StreamIndex™ benchmarks, iScore™ detailed reports, market intelligence, predictive analytics, DaaS API.
                  </p>
                  <div className="flex flex-wrap gap-4">
                    <Link href="/solutions/data-analytics" className="text-[#028475] hover:underline font-medium">Learn More about StreamResources+</Link>
                    <Link href="/login" className="text-[#028475] hover:underline font-medium">Subscriber Login</Link>
                  </div>
                </div>
              </PortalItem>
            </Accordion>
          </div>
        </div>
      </section>

      {/* Integration Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">Specialized Access, Unified Ecosystem: How Our Portals Work Together</h2>
            <p className="text-lg text-gray-700 mb-8">
              The true power of StreamLnk lies in the seamless integration between our portals. An order placed in MyStreamLnk can automatically trigger actions in E-Stream, assign tasks in StreamFreight, StreamGlobe+, and StreamPak, and feed data into StreamResources+ for analysis. This ensures:
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
              <div className="bg-[#F2F2F2] p-6 rounded-lg">
                <h4 className="text-xl font-semibold text-[#004235] mb-3">End-to-End Process Automation</h4>
              </div>
              <div className="bg-[#F2F2F2] p-6 rounded-lg">
                <h4 className="text-xl font-semibold text-[#004235] mb-3">Real-Time Data Consistency Across All Stakeholders</h4>
              </div>
              <div className="bg-[#F2F2F2] p-6 rounded-lg">
                <h4 className="text-xl font-semibold text-[#004235] mb-3">Reduced Manual Handoffs and Errors</h4>
              </div>
              <div className="bg-[#F2F2F2] p-6 rounded-lg">
                <h4 className="text-xl font-semibold text-[#004235] mb-3">A Single Source of Truth for Every Transaction</h4>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-[#F2F2F2]">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">Ready to Experience a Tailored Solution for Your Role in Global Trade?</h2>
          <p className="text-xl text-gray-700 mb-8 max-w-3xl mx-auto">
            No matter your function in the industrial supply chain, StreamLnk has a dedicated portal designed to empower your operations and connect you to a world of opportunity.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button className="bg-[#004235] hover:bg-[#023025] text-white px-6 py-5">
              REQUEST A GENERAL PLATFORM DEMO
            </Button>
            <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#F2F2F2] hover:text-[#023025] px-6 py-5">
              HELP ME CHOOSE THE RIGHT PORTAL
            </Button>
            <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#F2F2F2] hover:text-[#023025] px-6 py-5">
              REGISTER FOR A BUSINESS ACCOUNT
            </Button>
          </div>
        </div>
      </section>

      <BottomFooter />
    </div>
  );
}