import type { ReactNode } from "react"
import { Card, CardContent } from "@/components/ui/card"

interface BenefitCardProps {
  icon: ReactNode
  title: string
  description: string
}

export default function BenefitCard({ icon, title, description }: BenefitCardProps) {
  return (
    <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
      <CardContent className="p-4 flex items-start gap-4">
        <div className="p-2 rounded-full bg-[#004235]/10 flex-shrink-0">{icon}</div>
        <div>
          <h4 className="font-medium text-[#004235] mb-1">{title}</h4>
          <p className="text-sm text-gray-600">{description}</p>
        </div>
      </CardContent>
    </Card>
  )
}
