import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"
import HeroSection from "@/components/finance-payments/treasury-dashboard/hero-section"
import DashboardPreviewSection from "@/components/finance-payments/treasury-dashboard/dashboard-preview-section"
import KeyDashboardCapabilitiesSection from "@/components/finance-payments/treasury-dashboard/key-dashboard-capabilities-section"
import SecurityRoleBasedAccessSection from "@/components/finance-payments/treasury-dashboard/security-role-based-access-section"
import MultiCurrencySupportSection from "@/components/finance-payments/treasury-dashboard/multi-currency-support-section"
import TreasuryToolsIntegrationSection from "@/components/finance-payments/treasury-dashboard/treasury-tools-integration-section"
import CommonUseCasesSection from "@/components/finance-payments/treasury-dashboard/common-use-cases-section"
import CtaSection from "@/components/finance-payments/treasury-dashboard/cta-section"

export default function TreasuryDashboardPage() {
  return (
    <main className="flex flex-col min-h-screen">
      <MainNav />
      <HeroSection />
      <DashboardPreviewSection />
      <KeyDashboardCapabilitiesSection />
      <SecurityRoleBasedAccessSection />
      <MultiCurrencySupportSection />
      <TreasuryToolsIntegrationSection />
      <CommonUseCasesSection />
      <CtaSection />
      <MainFooter />
    </main>
  )
}
