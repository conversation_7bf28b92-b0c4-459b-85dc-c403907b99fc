"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { <PERSON>R<PERSON>, <PERSON><PERSON>, BarChartBig, LandmarkIcon } from 'lucide-react';

const implementations = [
  {
    icon: <Cpu className="h-8 w-8 text-[#028475] mb-3" />,
    description: "Our AI-powered quoting engine streamlines procurement...",
    link: "/solutions/sourcing-procurement",
    linkText: "Explore Sourcing Solutions"
  },
  {
    icon: <BarChartBig className="h-8 w-8 text-[#028475] mb-3" />,
    description: "StreamIndex™ leverages big data for market benchmarks...",
    link: "/solutions/streamindex-benchmarks",
    linkText: "Discover StreamIndex™"
  },
  {
    icon: <LandmarkIcon className="h-8 w-8 text-[#028475] mb-3" />,
    description: "Secure, integrated payments are core to our fintech approach...",
    link: "/solutions/trade-finance",
    linkText: "Learn About Trade Finance"
  }
];

export default function InnovationInActionSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Innovation in Action: Technology Powering the StreamLnk Platform
          </h2>
          <p className="text-xl text-gray-700">
            How StreamLnk Implements These Trends
          </p>
        </div>
        <p className="text-lg text-gray-600 mb-12 max-w-4xl mx-auto text-center">
          StreamLnk is at the forefront of applying AI, data analytics, and seamless platform integration to address real-world challenges in industrial trade. We are committed to delivering innovative solutions that drive efficiency and growth for your business.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {implementations.map((item, index) => (
            <div key={index} className="bg-gray-50 p-6 rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 flex flex-col">
              <div className="flex-shrink-0">{item.icon}</div>
              <p className="text-gray-700 mb-4 flex-grow">{item.description}</p>
              <Button 
                variant="link"
                className="text-[#028475] hover:text-[#004235] p-0 justify-start mt-auto"
                asChild
              >
                <Link href={item.link}>
                  {item.linkText}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}