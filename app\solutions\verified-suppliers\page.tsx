"use client"

import { MainNav } from "@/components/main-nav"
import { BottomFooter } from "@/components/bottom-footer"

import { HeroSection } from "@/components/solutions/verified-suppliers/HeroSection"
import { RisksUncertaintiesSection } from "@/components/solutions/verified-suppliers/RisksUncertaintiesSection"
import { HowStreamLnkEnsuresQualitySection } from "@/components/solutions/verified-suppliers/HowStreamLnkEnsuresQualitySection"
import { BusinessBenefitsSection } from "@/components/solutions/verified-suppliers/BusinessBenefitsSection"
import { JourneySection } from "@/components/solutions/verified-suppliers/JourneySection"
import { ContinuousEnhancementSection } from "@/components/solutions/verified-suppliers/ContinuousEnhancementSection"

export default function VerifiedSuppliersPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      {/* Hero Section */}
      <HeroSection />

      {/* Risks & Uncertainties Section */}
      <RisksUncertaintiesSection />

      {/* How StreamLnk Ensures Supplier Quality Section */}
      <HowStreamLnkEnsuresQualitySection />

      {/* Business Benefits Section */}
      <BusinessBenefitsSection />

      {/* Journey Section */}
      <JourneySection />

      {/* Continuous Enhancement Section */}
      <ContinuousEnhancementSection />

      <BottomFooter />
    </div>
  )
}