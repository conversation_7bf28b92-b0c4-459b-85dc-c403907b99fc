import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Navigate Global Markets with Confidence: In-Depth Analysis & Reports from StreamLnk
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Make informed strategic decisions with our comprehensive market analysis, powered by the real-time data and AI capabilities of StreamResources+. Explore pricing trends, demand forecasts, supply chain risks, and sector-specific outlooks.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
                size="lg"
                asChild
              >
                <Link href="/resources/market-analysis/latest-reports"> {/* Placeholder link */}
                  EXPLORE LATEST REPORTS
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button
                variant="outline"
                className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
                size="lg"
                asChild
              >
                <Link href="/solutions/streamresources-plus"> {/* Placeholder link */}
                  LEARN STREAMRESOURCES+
                  {/* <ArrowRight className="ml-2 h-5 w-5" /> */}
                </Link>
              </Button>
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/resources/market-analysis-hero.webp" // Placeholder - suggest user to replace
              alt="Market Analysis Hero Image"
              fill
              className="object-cover"
              priority
            />
            {/* TODO: Consider adding a more relevant image or illustration for market analysis */}
          </div>
        </div>
      </div>
    </section>
  );
}