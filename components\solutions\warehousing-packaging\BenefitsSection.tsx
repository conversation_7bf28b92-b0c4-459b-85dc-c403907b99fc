"use client"

import { CheckCircle } from "lucide-react"

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Optimize Product Handling, Reduce Lead Times, Ensure Quality
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Access to Specialized Services</h3>
                  <p className="text-gray-600">Easily find and engage vetted partners for specific packaging or storage needs without extensive individual searches.</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Improved Product Readiness</h3>
                  <p className="text-gray-600">Ensure materials are correctly packaged, labeled, and prepared for final markets or specific customer requirements.</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Enhanced Inventory Visibility</h3>
                  <p className="text-gray-600">Gain real-time insight into goods stored or being processed at third-party facilities.</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Streamlined Coordination</h3>
                  <p className="text-gray-600">Reduce the complexity of managing handoffs between suppliers, packagers, warehouses, and freight carriers.</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Quality Assurance</h3>
                  <p className="text-gray-600">Work with partners who adhere to StreamLnk's quality and compliance standards, with performance monitoring.</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Cost Efficiency</h3>
                  <p className="text-gray-600">Benefit from a network of potentially competitive service providers and optimized logistics flows.</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Scalability</h3>
                  <p className="text-gray-600">Easily scale your packaging or warehousing needs up or down based on demand, leveraging our partner network.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}