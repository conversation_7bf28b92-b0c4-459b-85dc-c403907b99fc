"use client"

import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, Bell } from "lucide-react";
import { FilterSection } from "./FilterSection";

export function CareersFilterSidebar() {
  return (
    <aside className="w-full md:w-1/3 lg:w-1/4">
      <h2 className="text-xl font-semibold text-gray-800 mb-4">Refine your search</h2>

      {/* Filter Sections */}
      <FilterSection title="Function" defaultOpen={true}>
        <div className="relative">
          <Input type="text" placeholder="Function" className="pr-10" />
          <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
        </div>
        <div className="space-y-2 mt-3 max-h-60 overflow-y-auto">
          {/* Checkbox items */}
          <div className="flex items-center space-x-2">
            <Checkbox id="func-logistics" />
            <label htmlFor="func-logistics" className="text-sm font-medium text-gray-700">Logistics (4)</label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="func-manufacturing" />
            <label htmlFor="func-manufacturing" className="text-sm font-medium text-gray-700">Manufacturing (107)</label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="func-marketing" />
            <label htmlFor="func-marketing" className="text-sm font-medium text-gray-700">Marketing (2)</label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="func-oag" />
            <label htmlFor="func-oag" className="text-sm font-medium text-gray-700">O&G Field (18)</label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="func-other" />
            <label htmlFor="func-other" className="text-sm font-medium text-gray-700">Other (19)</label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="func-prodmgmt" />
            <label htmlFor="func-prodmgmt" className="text-sm font-medium text-gray-700">Product Management (4)</label>
          </div>
        </div>
      </FilterSection>

      <FilterSection title="Country">
        <p className="text-sm text-gray-500">Country filters go here</p>
      </FilterSection>
      <FilterSection title="State">
        <p className="text-sm text-gray-500">State filters go here</p>
      </FilterSection>
      <FilterSection title="City">
        <p className="text-sm text-gray-500">City filters go here</p>
      </FilterSection>
      <FilterSection title="Employment Type">
        <p className="text-sm text-gray-500">Employment Type filters go here</p>
      </FilterSection>

      {/* Create Job Alert Box */}
      <div className="mt-8 rounded-lg border border-gray-200 p-4">
        <div className="flex items-center gap-2 mb-3">
          <Bell className="h-6 w-6 text-[#00A991]" />
          <h3 className="text-lg font-semibold text-gray-800">Create Job Alert</h3>
        </div>
        <p className="text-xs text-gray-500 mb-4">
          NOTE: Use refine search filters above to get better job alerts
        </p>
        <div className="space-y-3">
          <div>
            <label htmlFor="email-alert" className="sr-only">Email Address</label>
            <Input id="email-alert" type="email" placeholder="Enter mail" />
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">You'll get emails</span>
            <Select defaultValue="weekly">
              <SelectTrigger className="w-[120px] h-9">
                <SelectValue placeholder="Frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button className="w-full bg-[#00A991] hover:bg-[#008a75] text-white">
            Create Job Alert
          </Button>
        </div>
      </div>
    </aside>
  );
}