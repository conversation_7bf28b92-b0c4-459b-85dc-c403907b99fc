import { CheckCircle } from "lucide-react"

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Reduce Delays, Minimize Risk, Ensure Compliance
          </h2>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Benefit 1 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <CheckCircle className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-[#004235] mb-1">Faster Clearance Times</h3>
                <p className="text-gray-700">
                  Automated document flow and proactive agent coordination reduce processing delays.
                </p>
              </div>
            </div>

            {/* Benefit 2 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <CheckCircle className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-[#004235] mb-1">Reduced Errors & Penalties</h3>
                <p className="text-gray-700">
                  Standardized processes and access to expert agents minimize costly documentation errors.
                </p>
              </div>
            </div>

            {/* Benefit 3 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <CheckCircle className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-[#004235] mb-1">Enhanced Compliance</h3>
                <p className="text-gray-700">
                  Stay up-to-date with changing regulations and ensure all shipments meet customs requirements.
                </p>
              </div>
            </div>

            {/* Benefit 4 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <CheckCircle className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-[#004235] mb-1">Increased Transparency</h3>
                <p className="text-gray-700">
                  Real-time visibility into the clearance status of all your international shipments.
                </p>
              </div>
            </div>

            {/* Benefit 5 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <CheckCircle className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-[#004235] mb-1">Lower Administrative Burden</h3>
                <p className="text-gray-700">
                  Significantly reduce the time and effort spent on manual customs paperwork and follow-ups.
                </p>
              </div>
            </div>

            {/* Benefit 6 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <CheckCircle className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-[#004235] mb-1">Improved Predictability</h3>
                <p className="text-gray-700">
                  More reliable supply chain planning with better estimates of clearance times.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}