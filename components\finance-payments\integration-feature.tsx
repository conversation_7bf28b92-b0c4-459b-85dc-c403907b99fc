import React from "react";

interface IntegrationFeatureProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
}

export default function IntegrationFeature({ title, description, icon }: IntegrationFeatureProps) {
  return (
    <li className="flex items-start">
      {icon ? (
        <div className="mr-3">{icon}</div>
      ) : (
        <div className="mr-3 mt-1 bg-[#028475] rounded-full p-1">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-white"
          >
            <polyline points="20 6 9 17 4 12" />
          </svg>
        </div>
      )}
      <div>
        <h3 className="font-medium text-[#004235]">{title}</h3>
        <p className="text-gray-600">{description}</p>
      </div>
    </li>
  );
}