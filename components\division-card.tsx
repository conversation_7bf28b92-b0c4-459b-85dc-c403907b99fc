import Link from "next/link"
import Image from "next/image"
import { ChevronRight } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

interface DivisionCardProps {
  title: string
  description: string
  imageSrc: string
  href: string
  iconSrc: string
}

export function DivisionCard({ title, description, imageSrc, href, iconSrc }: DivisionCardProps) {
  return (
    <Card className="overflow-hidden transition-all duration-300 hover:shadow-lg group border-0 shadow-md">
      <div className="relative h-48 overflow-hidden">
        <Image
          src={imageSrc || "/placeholder.svg"}
          alt={title}
          fill
          className="object-cover transition-transform duration-500 group-hover:scale-105"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
        <div className="absolute bottom-0 left-0 p-4 flex items-center">
          <div className="bg-white rounded-full p-2 mr-3 shadow-md">
            <Image src={iconSrc || "/placeholder.svg"} alt={`${title} icon`} width={24} height={24} />
          </div>
          <h3 className="text-xl font-bold text-white">{title}</h3>
        </div>
      </div>
      <CardContent className="p-6">
        <p className="text-gray-700 mb-4">{description}</p>
        <Link href={href} className="inline-flex items-center text-[#028475] font-medium group-hover:underline">
          <span className="mr-1">Learn More</span>
          <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
        </Link>
      </CardContent>
    </Card>
  )
}
