import type React from "react"
import { CheckCircle } from "lucide-react"
import { Badge } from "@/components/ui/badge"

interface FeatureCardProps {
  icon: React.ReactNode
  title: string
  children: React.ReactNode
}

export function FeatureCard({ icon, title, children }: FeatureCardProps) {
  return (
    <div className="bg-white border border-gray-100 rounded-lg p-4 hover:shadow-sm transition-shadow">
      <div className="flex items-start gap-3">
        <div className="text-[#004235] mt-1">{icon}</div>
        <div>
          <h5 className="font-medium text-[#023025] mb-1">{title}</h5>
          <p className="text-sm text-[#404040]">{children}</p>
        </div>
      </div>
    </div>
  )
}

interface PlatformBadgeProps {
  icon: React.ReactNode
  platform: string
}

export function PlatformBadge({ icon, platform }: PlatformBadgeProps) {
  return (
    <Badge variant="outline" className="py-1.5 px-3 bg-white border-gray-200 text-[#404040] flex items-center gap-1.5">
      {icon}
      {platform}
    </Badge>
  )
}

interface BenefitItemProps {
  children: React.ReactNode
}

export function BenefitItem({ children }: BenefitItemProps) {
  return (
    <li className="flex items-start gap-2">
      <CheckCircle className="h-5 w-5 text-[#004235] mt-0.5 flex-shrink-0" />
      <span className="text-[#404040]">{children}</span>
    </li>
  )
}
