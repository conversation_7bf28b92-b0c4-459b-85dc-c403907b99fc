"use client"

import { useEffect, useRef } from "react"

export default function StockChart() {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    canvas.width = canvas.offsetWidth
    canvas.height = canvas.offsetHeight

    // Sample data - this would be replaced with actual stock data
    const data = [
      { year: "2023", projected: 0 },
      { year: "2024", projected: 15 },
      { year: "2025", projected: 35 },
      { year: "2026", projected: 65 },
      { year: "2027", projected: 100 },
      { year: "2028", projected: 150 },
    ]

    // Chart dimensions
    const padding = 40
    const chartWidth = canvas.width - padding * 2
    const chartHeight = canvas.height - padding * 2

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Draw axes
    ctx.beginPath()
    ctx.moveTo(padding, padding)
    ctx.lineTo(padding, canvas.height - padding)
    ctx.lineTo(canvas.width - padding, canvas.height - padding)
    ctx.strokeStyle = "#d1d5db"
    ctx.stroke()

    // Draw y-axis labels
    ctx.font = "12px Arial"
    ctx.fillStyle = "#6b7280"
    ctx.textAlign = "right"
    ctx.textBaseline = "middle"

    const yLabels = ["$0", "$50M", "$100M", "$150M"]
    const yStep = chartHeight / (yLabels.length - 1)

    yLabels.forEach((label, i) => {
      const y = canvas.height - padding - i * yStep
      ctx.fillText(label, padding - 10, y)

      // Draw horizontal grid lines
      ctx.beginPath()
      ctx.moveTo(padding, y)
      ctx.lineTo(canvas.width - padding, y)
      ctx.strokeStyle = "#e5e7eb"
      ctx.stroke()
    })

    // Draw x-axis labels
    ctx.textAlign = "center"
    ctx.textBaseline = "top"

    const xStep = chartWidth / (data.length - 1)

    data.forEach((point, i) => {
      const x = padding + i * xStep
      ctx.fillText(point.year, x, canvas.height - padding + 10)
    })

    // Draw line chart
    ctx.beginPath()
    data.forEach((point, i) => {
      const x = padding + i * xStep
      const y = canvas.height - padding - (point.projected / 150) * chartHeight
      if (i === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })
    ctx.strokeStyle = "#028475"
    ctx.lineWidth = 3
    ctx.stroke()

    // Draw data points
    data.forEach((point, i) => {
      const x = padding + i * xStep
      const y = canvas.height - padding - (point.projected / 150) * chartHeight
      ctx.beginPath()
      ctx.arc(x, y, 5, 0, Math.PI * 2)
      ctx.fillStyle = "#004235"
      ctx.fill()
    })

    // Draw area under the line
    ctx.beginPath()
    data.forEach((point, i) => {
      const x = padding + i * xStep
      const y = canvas.height - padding - (point.projected / 150) * chartHeight
      if (i === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })
    ctx.lineTo(padding + (data.length - 1) * xStep, canvas.height - padding)
    ctx.lineTo(padding, canvas.height - padding)
    ctx.closePath()
    ctx.fillStyle = "rgba(2, 132, 117, 0.1)"
    ctx.fill()

    // Draw chart title
    ctx.font = "14px Arial"
    ctx.fillStyle = "#004235"
    ctx.textAlign = "center"
    ctx.textBaseline = "top"
    ctx.fillText("Projected Revenue Growth", canvas.width / 2, 10)
  }, [])

  return <canvas ref={canvasRef} className="w-full h-[300px]" />
}
