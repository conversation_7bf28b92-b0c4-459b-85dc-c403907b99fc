import type { Metada<PERSON> } from "next"
import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"
import HeroSection from "@/components/solutions/esg-tracking-insights/HeroSection"
import ChallengesSection from "@/components/solutions/esg-tracking-insights/ChallengesSection"
import CapabilitiesSection from "@/components/solutions/esg-tracking-insights/CapabilitiesSection"
import StrategySection from "@/components/solutions/esg-tracking-insights/StrategySection"
import BenefitsSection from "@/components/solutions/esg-tracking-insights/BenefitsSection"
import CTASection from "@/components/solutions/esg-tracking-insights/CTASection"

export const metadata: Metadata = {
  title: "ESG Tracking & Insights | StreamLnk",
  description:
    "Meet your corporate responsibility goals and build more resilient, ethical supply chains. StreamLnk provides the tools and data to track Environmental, Social, and Governance (ESG) factors in your industrial material procurement.",
}

export default function ESGTrackingInsightsPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <ChallengesSection />

      <CapabilitiesSection />

      <StrategySection />

      <BenefitsSection />

      <CTASection />

      <MainFooter />
    </div>
  );
}