import { CheckCircle } from "lucide-react"

export function WhyPromoteSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Why Promote Your Services on StreamLnk?
          </h2>
          <p className="text-lg text-gray-700 mb-8 leading-relaxed">
            In today's complex global industrial marketplace, traditional directories and cold outreach are no longer
            enough. Visibility where decisions are made is paramount. StreamLnk is not just a platform; it's an active,
            AI-powered ecosystem where procurement, logistics, and service sourcing decisions happen in real-time. Our
            platform is built on integrated tools for quoting, shipping, customs clearance, packaging, and payment.
          </p>
          <p className="text-lg text-gray-700 mb-8 leading-relaxed">
            When you list and promote your services on StreamLnk, you plug directly into this live trade environment.
            This gives your business unparalleled exposure to:
          </p>

          <div className="space-y-4 mb-8">
            {[
              "Verified Global Suppliers & Manufacturers: Companies selling industrial products to over 80 countries.",
              "Active Industrial Buyers: Businesses placing global RFQs and orders across a multitude of product categories.",
              "Integrated Logistics Workflows: Opportunities arising from automated connections with packaging, freight, and customs operations.",
              "Influential Agents & Distributors: Professionals managing regional accounts and complex fulfillment strategies who require reliable service partners.",
              "Strategic Sourcing Teams: Decision-makers using the StreamLnk platform to build and complete their end-to-end shipment and supply chain requirements.",
            ].map((item, index) => (
              <div key={index} className="flex items-start">
                <CheckCircle className="h-6 w-6 text-[#028475] flex-shrink-0 mt-1 mr-3" />
                <p className="text-gray-700">{item}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
