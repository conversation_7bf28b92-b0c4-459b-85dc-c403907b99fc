import { Accordion } from "@/components/ui/accordion"
import PortalItem from "@/components/portals/portal-item"
import { Database, BookOpen, FileText, Users, Shield, Smartphone, Laptop, BarChart, TrendingUp, AlertTriangle, Truck, LineChart } from "lucide-react"
import { FeatureCard, PlatformBadge, BenefitItem } from "@/components/portals/portal-components"
import Link from "next/link"

export default function ResourcePortals() {
  return (
    <Accordion type="single" collapsible className="w-full space-y-6">
      <PortalItem
        id="streamresourcesplus"
        title="STREAMRESOURCES+"
        subtitle="Comprehensive Resources & Support"
        icon={<Database className="h-8 w-8" />}
        color="#004235"
        imageSrc="/images/portals/stream-resources-plus.webp"
        imageAlt="Resources Portal"
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">Portal Overview</h4>
            <p className="text-[#404040] mb-6">
              STREAMRESOURCES+™ provides access to comprehensive resources, documentation, training materials, and
              support tools to maximize your efficiency within the StreamLnk ecosystem. Our platform is designed to help
              you get the most out of our services and tools.
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Key Features</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <FeatureCard icon={<BookOpen />} title="Documentation Access">
                Access comprehensive documentation and guides for all StreamLnk services
              </FeatureCard>
              <FeatureCard icon={<FileText />} title="Templates & Forms">
                Download templates and forms to streamline your operations
              </FeatureCard>
              <FeatureCard icon={<Users />} title="Training Programs">
                Participate in training and certification programs to enhance your skills
              </FeatureCard>
              <FeatureCard icon={<Shield />} title="Compliance Support">
                Get support for compliance and regulatory issues related to your operations
              </FeatureCard>
            </div>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Available Platforms</h4>
            <div className="flex flex-wrap gap-4 mb-6">
              <PlatformBadge icon={<Laptop />} platform="Web Portal" />
              <PlatformBadge icon={<Smartphone />} platform="Mobile App" />
            </div>
          </div>

          <div className="bg-gray-50 p-6 rounded-lg">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">Who is this for?</h4>
            <p className="text-[#404040] mb-6">
              STREAMRESOURCES+™ is designed for all users of the StreamLnk ecosystem who want to maximize their
              efficiency and effectiveness. Whether you're a customer, agent, supplier, or logistics partner, our
              resource portal provides the tools and information you need.
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Benefits</h4>
            <ul className="space-y-3 mb-6">
              <BenefitItem>Comprehensive knowledge base</BenefitItem>
              <BenefitItem>Standardized templates and forms</BenefitItem>
              <BenefitItem>Professional development opportunities</BenefitItem>
              <BenefitItem>Expert compliance guidance</BenefitItem>
              <BenefitItem>Streamlined support access</BenefitItem>
            </ul>

            <Link href="/signup?portal=streamresources-plus" className="block w-full">
              <button className="w-full bg-[#004235] hover:bg-[#004235]/90 text-white py-2 px-4 rounded-md font-medium mt-4">
                Request Access
              </button>
            </Link>
          </div>
        </div>
      </PortalItem>

      <PortalItem
        id="streamindex"
        title="STREAMINDEX"
        subtitle="AI-Driven Market Intelligence Tool"
        icon={<LineChart className="h-8 w-8" />}
        color="#004235"
        imageSrc="/images/portals/stream-Index.webp"
        imageAlt="Market Intelligence Portal"
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">Portal Overview</h4>
            <p className="text-[#404040] mb-6">
              StreamIndex™ is StreamLnk's proprietary industrial market intelligence tool designed to provide real-time, AI-driven benchmarks and insights across global supply chains. It functions as a dynamic pricing, logistics, and risk index—specifically tailored to polymer and raw material trade.
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Key Features</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <FeatureCard icon={<BarChart />} title="Product Pricing Index">
                Aggregates and normalizes pricing data across suppliers, geographies, and incoterms to give an average market price (benchmark) per SKU
              </FeatureCard>
              <FeatureCard icon={<Truck />} title="Freight Efficiency Score">
                Tracks delivery timelines, route reliability, carrier performance, and adds weight for customs delays and packaging cycle times
              </FeatureCard>
              <FeatureCard icon={<TrendingUp />} title="Supply Health Monitor">
                Measures real-time supplier inventory velocity, stock aging, and order fulfillment rate to assess supply chain robustness
              </FeatureCard>
              <FeatureCard icon={<LineChart />} title="Buyer Demand Trends">
                Analyzes RFQ patterns, auction participation, reorder cycles, and seasonality to forecast demand intensity by region or product
              </FeatureCard>
            </div>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Available Platforms</h4>
            <div className="flex flex-wrap gap-4 mb-6">
              <PlatformBadge icon={<Laptop />} platform="Web Portal" />
              <PlatformBadge icon={<Smartphone />} platform="Mobile App" />
            </div>
          </div>

          <div className="bg-gray-50 p-6 rounded-lg">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">How StreamIndex™ is Used</h4>
            <p className="text-[#404040] mb-6">
              StreamIndex™ provides valuable market intelligence for various stakeholders in the supply chain ecosystem:
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Benefits</h4>
            <ul className="space-y-3 mb-6">
              <BenefitItem>By Buyers: Compare pricing and reliability across suppliers before placing large-volume orders</BenefitItem>
              <BenefitItem>By Suppliers: Benchmark pricing, performance, and compliance metrics against market average</BenefitItem>
              <BenefitItem>By Freight & Customs Agents: Evaluate and optimize operations for better scoring</BenefitItem>
              <BenefitItem>By StreamLnk's Own AI: Route shipments efficiently and flag underperforming lanes</BenefitItem>
            </ul>



            <Link href="/signup?portal=streamindex" className="block w-full">
              <button className="w-full bg-[#004235] hover:bg-[#004235]/90 text-white py-2 px-4 rounded-md font-medium mt-4">
                Request Access
              </button>
            </Link>
          </div>
        </div>
      </PortalItem>
    </Accordion>
  )
}
