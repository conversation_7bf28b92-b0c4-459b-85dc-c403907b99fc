import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Your Global Trade Hub, In Your Pocket: StreamLnk Mobile Access
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Stay connected to your critical industrial trade operations wherever you are. StreamLnk's portals are designed for mobile responsiveness, with dedicated native apps planned for an even richer on-the-go experience.
            </p>
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white px-6"
              size="lg"
              asChild
            >
              <Link href="/portal-features/mobile">
                Explore Mobile Access
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/portal-features/mobile/hero-placeholder.jpg" // Placeholder image - replace with actual image
              alt="StreamLnk Mobile Access"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
}