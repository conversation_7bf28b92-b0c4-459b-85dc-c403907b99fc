"use client"

import { MainNav } from "@/components/main-nav"
import { BottomFooter } from "@/components/bottom-footer"
import { HeroSection } from "@/components/api-integration/hero-section"
import { WhyPartnerSection } from "@/components/api-integration/why-partner-section"
import { PartnershipTypesSection } from "@/components/api-integration/partnership-types-section"
import { DeveloperToolsSection } from "@/components/api-integration/developer-tools-section"
import { MonetizationSection } from "@/components/api-integration/monetization-section"
import { UseCasesSection } from "@/components/api-integration/use-cases-section"
import { ResourcesSection } from "@/components/api-integration/resources-section"
import { GetStartedSection } from "@/components/api-integration/get-started-section"
import { CtaSection } from "@/components/api-integration/cta-section"

export default function ApiIntegrationPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <MainNav />
      <main>
        <HeroSection />
        <WhyPartnerSection />
        <PartnershipTypesSection />
        <DeveloperToolsSection />
        <MonetizationSection />
        <UseCasesSection />
        <ResourcesSection />
        <GetStartedSection />
        <CtaSection />
      </main>
      <BottomFooter />
    </div>
  )
}
