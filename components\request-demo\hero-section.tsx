import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export function RequestDemoHero() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
          <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
            Experience the Future of Industrial Trade: Request Your Personalized StreamLnk Demo
          </h1>
          <p className="text-xl md:text-2xl text-gray-700 mb-8">
            See firsthand how our integrated platform can revolutionize your supply chain, optimize costs, and unlock new global opportunities.
          </p>
          <Button 
            className="bg-[#004235] hover:bg-[#028475] text-white px-6"
            size="lg"
            onClick={() => {
              const formElement = document.getElementById('demo-form')
              if (formElement) {
                formElement.scrollIntoView({ behavior: 'smooth' })
              }
            }}
          >
            Request Demo Now
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl mt-8 lg:mt-0">
            <Image
              src="/images/request-demo/request-demo-hero.webp" // Placeholder - suggest user to replace
              alt="StreamLnk Request Demo Hero Image"
              fill
              className="object-cover"
              priority
            />
            {/* TODO: Consider adding a more relevant image or illustration for requesting a demo */}
          </div>
        </div>
      </div>
    </section>
  )
}