"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/resources/whitepapers/HeroSection";
import IntroSection from "@/components/resources/whitepapers/IntroSection";
import FeaturedWhitepapersSection from "@/components/resources/whitepapers/FeaturedWhitepapersSection";
import CategoriesSection from "@/components/resources/whitepapers/CategoriesSection";
import AllWhitepapersSection from "@/components/resources/whitepapers/AllWhitepapersSection";
import SuggestTopicSection from "@/components/resources/whitepapers/SuggestTopicSection";

export default function WhitepapersPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <IntroSection />
      <FeaturedWhitepapersSection />
      <CategoriesSection />
      <AllWhitepapersSection />
      <SuggestTopicSection />

      <BottomFooter />
    </div>
  );
}