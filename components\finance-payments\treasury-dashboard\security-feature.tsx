import type { ReactNode } from "react"
import { Card, CardContent } from "@/components/ui/card"

interface SecurityFeatureProps {
  icon: ReactNode
  title: string
  description: string
}

export default function SecurityFeature({ icon, title, description }: SecurityFeatureProps) {
  return (
    <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
      <CardContent className="pt-6 flex items-start gap-4">
        <div className="p-3 rounded-full bg-[#004235]/10 flex-shrink-0">{icon}</div>
        <div>
          <h3 className="text-lg font-semibold text-[#004235] mb-2">{title}</h3>
          <p className="text-gray-600">{description}</p>
        </div>
      </CardContent>
    </Card>
  )
}
