"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/industries/packaging/HeroSection";
import ChallengesSection from "@/components/industries/packaging/ChallengesSection";
import SolutionsSection from "@/components/industries/packaging/SolutionsSection";
import SustainablePackagingSection from "@/components/industries/packaging/SustainablePackagingSection";
import BenefitsSection from "@/components/industries/packaging/BenefitsSection";
import { PackagingCTASection } from "@/components/industries/packaging/CTASection";

export default function PackagingIndustryPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <ChallengesSection />
      <SolutionsSection />
      <SustainablePackagingSection />
      <BenefitsSection />
      <PackagingCTASection />

      <BottomFooter />
    </div>
  );
}