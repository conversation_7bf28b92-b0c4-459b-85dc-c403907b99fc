"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/resources/iscore/HeroSection";
import ChallengeSection from "@/components/resources/iscore/ChallengeSection";
import WhatIsIscoreSection from "@/components/resources/iscore/WhatIsIscoreSection";
import HowIscoreEmpowersSection from "@/components/resources/iscore/HowIscoreEmpowersSection";
import AccessingIscoreSection from "@/components/resources/iscore/AccessingIscoreSection";
import CtaSection from "@/components/resources/iscore/CtaSection";

export default function IscorePage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <ChallengeSection />
      <WhatIsIscoreSection />
      <HowIscoreEmpowersSection />
      <AccessingIscoreSection />
      <CtaSection />

      <BottomFooter />
    </div>
  );
}