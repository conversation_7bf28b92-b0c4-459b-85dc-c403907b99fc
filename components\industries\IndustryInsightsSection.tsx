import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"

export default function IndustryInsightsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Industry Insights & Market Intelligence
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            Access data-driven reports and analyses specific to your sector via StreamResources+. Understand market trends, pricing dynamics, and supply chain risks.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white px-6"
              asChild
            >
              <Link href="/solutions/data-analytics">
                Explore Industry Insights
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button 
              variant="outline" 
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white px-6"
              asChild
            >
              <Link href="/solutions/streamindex-benchmarks">
                Learn About StreamResources+
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}