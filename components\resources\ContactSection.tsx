"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { Mail } from "lucide-react";

export default function ContactSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto text-center bg-[#F2F2F2] p-8 md:p-12 rounded-lg shadow-lg">
          <h2 className="text-3xl font-bold text-[#004235] mb-4">
            Can't Find What You're Looking For?
          </h2>
          <p className="text-xl text-gray-700 mb-6">
            Let Us Know
          </p>
          <p className="text-lg text-gray-600 mb-8">
            If you're looking for information on a specific topic not covered here, or if you have suggestions for future resources, please contact our content team.
          </p>
          <Button 
            className="bg-[#004235] hover:bg-[#028475] text-white px-8 py-3 text-lg"
            asChild
            size="lg"
          >
            <Link href="/contact?subject=Resource Hub Suggestion">
              <Mail className="mr-2 h-5 w-5" />
              CONTACT CONTENT TEAM
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}