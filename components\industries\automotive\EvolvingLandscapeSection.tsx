"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Zap, Leaf, Recycle, ArrowRight } from "lucide-react"

const topics = [
  {
    icon: <Zap className="h-8 w-8 text-[#028475]" />,
    title: "Electric Vehicles (EVs)",
    description: "Facilitating the sourcing of specialized materials for batteries, lightweight structures, and EV components.",
  },
  {
    icon: <Leaf className="h-8 w-8 text-[#028475]" />,
    title: "Sustainability",
    description: "Enabling the procurement of recycled plastics, bio-based materials, and low-carbon footprint components.",
  },
  {
    icon: <Recycle className="h-8 w-8 text-[#028475]" />,
    title: "Circular Economy",
    description: "Supporting the traceability and trade of recyclable automotive materials.",
  },
]

export default function EvolvingLandscapeSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Powering the Next Generation of Automotive Innovation
          </h2>
          <p className="text-xl text-[#028475] font-semibold mb-6">
            Focus on the Evolving Automotive Landscape (EVs, Sustainability)
          </p>
          <p className="text-lg text-gray-700 mb-8">
            StreamLnk is equipped to support the automotive industry's transition towards:
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {topics.map((topic, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="flex justify-center mb-4">
                <div className="bg-[#004235]/10 p-3 rounded-full">
                  {topic.icon}
                </div>
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">{topic.title}</h3>
              <p className="text-gray-600">{topic.description}</p>
            </div>
          ))}
        </div>
        <div className="text-center">
          <Button 
            variant="outline"
            className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white px-6"
            size="lg"
            asChild
          >
            <Link href="/solutions/sustainable-materials">
              Discover Our Solutions for Sustainable Materials
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}