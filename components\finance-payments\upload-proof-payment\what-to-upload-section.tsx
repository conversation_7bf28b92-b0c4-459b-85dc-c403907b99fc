import UploadRequirement from "@/components/finance-payments/upload-proof-payment/upload-requirement"
import { FileCheck, FileText } from "lucide-react"

export default function WhatToUploadSection() {
  return (
    <section className="py-16 md:py-24 bg-[#f3f4f6]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">What to Upload</h2>
          <p className="text-gray-600 max-w-3xl">Ensure your uploaded documents include the following information:</p>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <UploadRequirement icon={<FileText />} text="Bank name and logo" />
          <UploadRequirement icon={<FileText />} text="Sender and recipient account details" />
          <UploadRequirement icon={<FileText />} text="Amount transferred and currency" />
          <UploadRequirement icon={<FileText />} text="Date of transfer" />
          <UploadRequirement icon={<FileText />} text="Payment reference or invoice number" />
          <UploadRequirement icon={<FileText />} text="Confirmation stamp or tracking ID (if applicable)" />
        </div>

        <div className="mt-8 p-4 bg-white rounded-lg border border-[#f3f4f6] flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="flex items-center gap-3">
            <FileCheck className="h-6 w-6 text-[#028475]" />
            <span className="text-gray-700">Accepted formats: PDF, PNG, JPG</span>
          </div>
          <div className="flex items-center gap-3">
            <FileCheck className="h-6 w-6 text-[#028475]" />
            <span className="text-gray-700">Maximum file size: 10MB</span>
          </div>
        </div>
      </div>
    </section>
  )
}