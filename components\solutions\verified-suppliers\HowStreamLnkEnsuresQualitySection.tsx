"use client"

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, FileCheck, Award } from "lucide-react"

export function HowStreamLnkEnsuresQualitySection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Source with Confidence: How StreamLnk Ensures Supplier Quality
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamLnk is committed to building the most trusted B2B ecosystem for industrial materials. Our E-Stream portal and rigorous onboarding process ensure that every supplier on our platform meets stringent criteria:
          </p>
          
          <div className="space-y-8">
            {/* Comprehensive Onboarding & KYC/AML */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Users className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Comprehensive Onboarding & KYC/AML</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>All suppliers undergo a detailed registration process, submitting business licenses, tax IDs, key contact information, and operational details.</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>We conduct Know Your Customer (KYC) and Anti-Money Laundering (AML) checks to verify identity and legitimacy.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            
            {/* Document Verification */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <FileCheck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Document Verification</h3>
                  <p className="text-gray-700 mb-4">
                    Mandatory upload and verification of critical compliance documents:
                  </p>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>Quality Certifications (ISO, industry-specific standards)</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>Product Certifications (REACH, RoHS, FDA as applicable)</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>Material Safety Data Sheets (MSDS/SDS) & Technical Data Sheets (TDS)</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>Certificates of Origin (COO)</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>Insurance Coverage</span>
                    </li>
                  </ul>
                  <p className="text-gray-700 mt-4">
                    Our system tracks document expiry dates with automated alerts to suppliers and internal flags for non-compliance.
                  </p>
                </div>
              </div>
            </div>
            
            {/* iScore™ Performance & Reliability Ratings */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Award className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">iScore™ Performance & Reliability Ratings</h3>
                  <p className="text-gray-700 mb-4">
                    Our proprietary iScore™ system continuously evaluates suppliers based on:
                  </p>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>Operational Performance: On-time readiness, order fulfillment accuracy, inventory availability.</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>Compliance History: Document validity, adherence to platform terms.</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>Customer Feedback: Ratings on product quality, service, and communication from verified buyers on StreamLnk.</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>Financial Trustworthiness Indicators (where applicable and permissible).</span>
                    </li>
                  </ul>
                  <p className="text-gray-700 mt-4">
                    Buyers see a simplified iScore™ badge (e.g., "Gold Rated," "iScore: 85/100") providing an at-a-glance trust signal.
                  </p>
                </div>
              </div>
            </div>
            
            {/* Transparent Supplier Profiles */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Users className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Transparent Supplier Profiles</h3>
                  <p className="text-gray-700 mb-4">
                    Detailed profiles on E-Stream (visible to buyers on MyStreamLnk) showcase:
                  </p>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>Company information and history.</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>Full product catalogs with detailed specifications.</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>Visible certifications and compliance documents (non-sensitive parts).</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>Regions served and logistics capabilities.</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#028475] mr-2 mt-0.5 flex-shrink-0" />
                      <span>Anonymized performance highlights and iScore™ summaries.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}