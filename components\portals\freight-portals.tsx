import { Accordion } from "@/components/ui/accordion"
import PortalItem from "@/components/portals/portal-item"
import {
  Truck,
  FileCheck,
  FileText,
  BarChart,
  TrendingUp,
  Smartphone,
  Laptop,
  Server,
  Package,
  Layers,
  Shield,
} from "lucide-react"
import { FeatureCard, PlatformBadge, BenefitItem } from "@/components/portals/portal-components"
import Link from "next/link"

export default function FreightPortals() {
  return (
    <Accordion type="single" collapsible className="w-full space-y-6">
      <PortalItem
        id="streamfreight"
        title="STREAMFREIGHT"
        subtitle="For Truckers and Freight Forwarders"
        icon={<Truck className="h-8 w-8" />}
        color="#004235"
        imageSrc="/images/portals/stream-freight.webp"
        imageAlt="Freight Forwarding Portal"
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">Portal Overview</h4>
            <p className="text-[#404040] mb-6">
              STREAMFREIGHT connects truckers and freight forwarders with available shipments in the StreamLnk network.
              Our platform helps you find and bid on available shipments, manage compliance documents, upload invoices,
              and track your earnings seamlessly within the network.
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Key Features</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <FeatureCard icon={<TrendingUp />} title="Shipment Bidding">
                Find and bid on available shipments that match your capabilities and routes
              </FeatureCard>
              <FeatureCard icon={<FileCheck />} title="Compliance Management">
                Efficiently manage your compliance documents and certifications
              </FeatureCard>
              <FeatureCard icon={<FileText />} title="Invoice Uploads">
                Upload invoices for completed deliveries and track payment status
              </FeatureCard>
              <FeatureCard icon={<BarChart />} title="Earnings Tracking">
                Track your earnings and performance metrics within the network
              </FeatureCard>
            </div>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Available Platforms</h4>
            <div className="flex flex-wrap gap-4 mb-6">
              <PlatformBadge icon={<Smartphone />} platform="Mobile App" />
              <PlatformBadge icon={<Laptop />} platform="Web Portal" />
            </div>
          </div>

          <div className="bg-gray-50 p-6 rounded-lg">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">Who is this for?</h4>
            <p className="text-[#404040] mb-6">
              STREAMFREIGHT is designed for truckers and freight forwarders who want to find new business opportunities
              and streamline their operations. Our platform connects you with available shipments and provides the tools
              you need to manage your business efficiently.
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Benefits</h4>
            <ul className="space-y-3 mb-6">
              <BenefitItem>Access to new business opportunities</BenefitItem>
              <BenefitItem>Streamlined compliance management</BenefitItem>
              <BenefitItem>Efficient invoice processing</BenefitItem>
              <BenefitItem>Transparent earnings tracking</BenefitItem>
              <BenefitItem>Integrated communication tools</BenefitItem>
            </ul>

            <Link href="/signup?portal=streamfreight" className="block w-full">
              <button className="w-full  bg-[#004235] hover:bg-[#004235]/90 text-white py-2 px-4 rounded-md font-medium mt-4">
                Request Access
              </button>
            </Link>
          </div>
        </div>
      </PortalItem>
      <PortalItem
        id="stream-pak"
        title="STREAM-PAK"
        subtitle="For Packaging & 3PL Warehouse Partners"
        icon={<Package className="h-8 w-8" />}
        color="#023025"
        imageSrc="/images/portals/stream-pak.webp"
        imageAlt="Packaging Portal"
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">Portal Overview</h4>
            <p className="text-[#404040] mb-6">
              STREAM-PAK centralizes your finishing operations within the StreamLnk supply chain, providing tools for
              packaging and warehouse management. Our platform helps you manage packaging and storage job assignments,
              update inventory status, ensure document compliance, and submit invoices for services rendered.
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Key Features</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <FeatureCard icon={<FileCheck />} title="Job Management">
                Efficiently manage packaging and storage job assignments in one central location
              </FeatureCard>
              <FeatureCard icon={<Layers />} title="Inventory Tracking">
                Update and track inventory status in real-time across your warehouse operations
              </FeatureCard>
              <FeatureCard icon={<Shield />} title="Compliance Assurance">
                Ensure document compliance for all shipments with automated verification
              </FeatureCard>
              <FeatureCard icon={<FileText />} title="Invoice Submission">
                Submit and track invoices for services rendered within the StreamLnk network
              </FeatureCard>
            </div>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Available Platforms</h4>
            <div className="flex flex-wrap gap-4 mb-6">
              <PlatformBadge icon={<Smartphone />} platform="Mobile App" />
              <PlatformBadge icon={<Laptop />} platform="Web Portal" />
              <PlatformBadge icon={<Server />} platform="API Access" />
            </div>
          </div>

          <div className="bg-gray-50 p-6 rounded-lg">
            <h4 className="text-lg font-semibold text-[#023025] mb-4">Who is this for?</h4>
            <p className="text-[#404040] mb-6">
              STREAM-PAK is designed for packaging providers and 3PL warehouse partners who want to integrate
              seamlessly with the StreamLnk supply chain. Our platform provides the tools you need to manage your
              operations efficiently and provide excellent service to your customers.
            </p>

            <h4 className="text-lg font-semibold text-[#023025] mb-4">Benefits</h4>
            <ul className="space-y-3 mb-6">
              <BenefitItem>Centralized job management</BenefitItem>
              <BenefitItem>Real-time inventory tracking</BenefitItem>
              <BenefitItem>Streamlined compliance verification</BenefitItem>
              <BenefitItem>Efficient invoice processing</BenefitItem>
              <BenefitItem>Integrated communication tools</BenefitItem>
            </ul>

            <Link href="/signup?portal=streampak" className="block w-full">
              <button className="w-full  bg-[#004235] hover:bg-[#004235]/90 text-white py-2 px-4 rounded-md font-medium mt-4">
                Request Access
              </button>
            </Link>
          </div>
        </div>
      </PortalItem>
    </Accordion>
  )
}