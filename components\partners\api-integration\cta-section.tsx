import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { ArrowR<PERSON> } from "lucide-react"

export function CtaSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-6">
            Let's Build Smarter, More Connected Supply Chains—Together
          </h2>

          <p className="text-lg text-gray-700 mb-10 max-w-3xl mx-auto">
            Join us in pioneering the next generation of industrial trade intelligence. By integrating your solutions
            with StreamLnk, you contribute to a more transparent, efficient, and data-driven global marketplace.
          </p>

          <div className="flex flex-wrap justify-center gap-6">
            <Button size="lg" className="bg-[#004235] text-white hover:bg-[#028475]" asChild>
              <Link href="#" className="flex items-center">
                Apply to Become a Partner
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button variant="outline" size="lg" className="border-[#004235] text-[#004235] hover:bg-[#004235]/10" asChild>
              <Link href="#">Request API Documentation</Link>
            </Button>
          </div>

          <p className="text-sm text-gray-600 mt-8">Portal Access: StreamResources+ & Developer Portal</p>
        </div>
      </div>
    </section>
  )
}
