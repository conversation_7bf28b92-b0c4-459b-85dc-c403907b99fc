import { Search, Truck, CreditCard, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

export function SupportSection() {
  const supportItems = [
    {
      icon: <Search className="h-10 w-10 text-[#028475]" />,
      title: "Supplier Matching & Vetting",
      description: "We connect your clients with verified suppliers that meet their specific requirements.",
    },
    {
      icon: <Truck className="h-10 w-10 text-[#028475]" />,
      title: "Logistics & Customs Coordination",
      description: "Seamless handling of shipping and customs clearance via StreamFreight & StreamGlobe.",
    },
    {
      icon: <CreditCard className="h-10 w-10 text-[#028475]" />,
      title: "Secure Invoicing & Payment Processing",
      description: "Reliable financial transactions with multiple payment options and security measures.",
    },
    {
      icon: <ShieldCheck className="h-10 w-10 text-[#028475]" />,
      title: "Risk Management & Compliance Oversight",
      description: "Comprehensive risk assessment and compliance monitoring for all transactions.",
    },
    {
      icon: <BarChart className="h-10 w-10 text-[#028475]" />,
      title: "Transparent Pricing & Market Data",
      description: "Access to real-time market pricing and trends to inform client decisions.",
    },
  ]

  return (
    <section className="py-20 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Support From StreamLnk: You Focus on Sales, We Handle the Rest
          </h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            StreamLnk provides robust backend support so you can concentrate on client relationships and sales growth:
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {supportItems.map((item, index) => (
            <Card key={index} className="border border-gray-200 hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="mb-4">{item.icon}</div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">{item.title}</h3>
                <p className="text-gray-700">{item.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-12 text-center">
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            Your clients get full system access and the benefits of a global platform, with you as their dedicated,
            trusted liaison.
          </p>
        </div>
      </div>
    </section>
  )
}
