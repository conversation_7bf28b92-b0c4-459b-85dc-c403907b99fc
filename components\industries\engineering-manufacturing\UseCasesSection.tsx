"use client";

import { Setting<PERSON>, Wrench, Layers } from "lucide-react";

const useCases = [
  {
    title: "Component Sourcing for an Equipment Manufacturer",
    description: "An industrial machinery OEM uses MyStreamLnk to source 200+ unique components from 15 different global suppliers, managing all RFQs, orders, and inbound logistics through a single dashboard, reducing procurement lead time by 20%.",
    icon: <Settings className="h-10 w-10 text-[#028475] mb-4" />
  },
  {
    title: "MRO Procurement for a Large Plant",
    description: "A manufacturing plant streamlines its MRO supplies procurement by connecting with verified local and international suppliers on StreamLnk, ensuring availability of critical spare parts and reducing emergency purchase costs.",
    icon: <Wrench className="h-10 w-10 text-[#028475] mb-4" />
  },
  {
    title: "Raw Material Sourcing for a Specialty Alloy Producer",
    description: "A producer of high-performance alloys uses E-Stream to reach new niche buyers in the aerospace and medical device industries, showcasing their unique material properties and certifications.",
    icon: <Layers className="h-10 w-10 text-[#028475] mb-4" />
  }
];

export default function UseCasesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-[#004235] mb-4 text-center">
          Use Cases – StreamLnk in Engineering & Manufacturing
        </h2>
        <p className="text-lg text-gray-700 mb-10 text-center">
          Optimizing Operations from Design to Delivery
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {useCases.map((useCase, index) => (
            <div key={index} className="bg-[#F2F2F2] p-6 rounded-lg shadow-md">
              {useCase.icon}
              <h3 className="text-xl font-semibold text-[#004235] mb-2">{useCase.title}</h3>
              <p className="text-gray-600">{useCase.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}