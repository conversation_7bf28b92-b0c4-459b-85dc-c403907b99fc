import type { ReactNode } from "react"
import { <PERSON>, Card<PERSON>ontent, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"

interface CollaborationCardProps {
  icon: ReactNode
  title: string
  description: string
  buttonText: string
  buttonLink: string
}

export default function CollaborationCard({
  icon,
  title,
  description,
  buttonText,
  buttonLink,
}: CollaborationCardProps) {
  return (
    <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors h-full flex flex-col">
      <CardContent className="pt-6 flex-grow">
        <div className="flex flex-col items-center text-center space-y-4">
          <div className="p-3 rounded-full bg-[#004235]/10">{icon}</div>
          <h3 className="text-lg font-semibold text-[#004235]">{title}</h3>
          <p className="text-gray-600">{description}</p>
        </div>
      </CardContent>
      <CardFooter className="pt-0 flex justify-center">
        <Button className="bg-[#004235] hover:bg-[#004235]/90 w-full" asChild>
          <a href={buttonLink}>{buttonText}</a>
        </Button>
      </CardFooter>
    </Card>
  )
}
