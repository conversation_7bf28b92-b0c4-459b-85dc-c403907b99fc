"use client"

import { AlertTriangle } from "lucide-react"

export default function ChallengesSection() {
  const challenges = [
    "Reliance on lagging indicators or historical data that doesn't reflect current market sentiment.",
    "Limited visibility into real-time buyer intent or supplier capacity across different regions.",
    "Difficulty in factoring in external disruptors (geopolitical events, logistics bottlenecks, raw material shortages) into forecasts.",
    "Risk of overstocking during downturns or understocking during demand surges, leading to financial losses or missed opportunities.",
    "Inability to make agile pricing or production adjustments based on forward-looking intelligence."
  ]

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Caught Off Guard by Market Swings? The Perils of Poor Forecasting.
          </h2>
          <p className="text-lg text-gray-700 mb-8 text-center">
            In the dynamic global market for industrial materials, anticipating demand and supply fluctuations is critical, yet often incredibly difficult due to:
          </p>
          
          <div className="space-y-4 mt-8">
            {challenges.map((challenge, index) => (
              <div key={index} className="flex items-start gap-4 p-4 bg-[#f3f4f6] rounded-lg">
                <AlertTriangle className="h-6 w-6 text-amber-500 flex-shrink-0 mt-1" />
                <p className="text-gray-700">{challenge}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}