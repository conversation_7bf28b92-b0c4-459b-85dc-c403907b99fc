import HeroSection from "@/components/solutions/warehousing-packaging/HeroSection"
import ChallengesSection from "@/components/solutions/warehousing-packaging/ChallengesSection"
import PlatformOverviewSection from "@/components/solutions/warehousing-packaging/PlatformOverviewSection"
import WorkflowSection from "@/components/solutions/warehousing-packaging/WorkflowSection"
import BenefitsSection from "@/components/solutions/warehousing-packaging/BenefitsSection"
import CallToActionSection from "@/components/solutions/warehousing-packaging/CallToActionSection"
import { MainNav } from "@/components/main-nav"
import { BottomFooter } from "@/components/bottom-footer"
import { Button } from "@/components/ui/button"
import { ArrowRight, CheckCircle, Package, Truck, BarChart3, FileText, ShieldCheck, Warehouse } from "lucide-react"

export default function WarehousePackagingPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <ChallengesSection />

      <PlatformOverviewSection />

      <WorkflowSection />

      <BenefitsSection />

      <CallToActionSection />

      <BottomFooter />
    </div>
  )
}