import { StandardizedTimeline } from "@/components/ui/standardized-timeline";
import { Folder, Upload, FileText, CheckCircle, Send } from "lucide-react";

export default function HowToUploadSection() {
  const steps = [
    {
      number: 1,
      icon: <Folder className="h-8 w-8 text-white" />,
      title: "Navigate to Payments",
      description: "Go to the Payments or Order Details section of your portal (MyStreamLnk or E-Stream)"
    },
    {
      number: 2,
      icon: <Upload className="h-8 w-8 text-white" />,
      title: "Click Upload",
      description: "Click on Upload Proof of Payment"
    },
    {
      number: 3,
      icon: <FileText className="h-8 w-8 text-white" />,
      title: "Attach Receipt",
      description: "Attach your bank receipt or wire confirmation"
    },
    {
      number: 4,
      icon: <CheckCircle className="h-8 w-8 text-white" />,
      title: "Match to Order",
      description: "Match it to the correct invoice or order ID"
    },
    {
      number: 5,
      icon: <Send className="h-8 w-8 text-white" />,
      title: "Submit",
      description: "Submit. Confirmation email will follow"
    }
  ];

  return (
    <StandardizedTimeline
      title="How to Upload POP on StreamLnk"
      description="Follow these simple steps to upload your proof of payment."
      steps={steps}
      bgColor="bg-[#F2F2F2]"
    />
  );
}