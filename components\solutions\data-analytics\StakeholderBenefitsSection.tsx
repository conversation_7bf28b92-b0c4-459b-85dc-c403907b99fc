import { Building, Users, Truck, UserCheck, Landmark } from "lucide-react"

export default function StakeholderBenefitsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]" id="stakeholders">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Intelligence Tailored for Every Stakeholder
          </h2>
        </div>

        <div className="grid md:grid-cols-3 gap-6 max-w-5xl mx-auto">
          {[{
              icon: <Building className="h-8 w-8 text-[#004235]" />,
              title: "Suppliers",
              description: "Understand true market demand, optimize pricing, benchmark performance, identify new market opportunities."
            },
            {
              icon: <Users className="h-8 w-8 text-[#004235]" />,
              title: "Buyers/Manufacturers",
              description: "Make informed sourcing decisions, reduce procurement costs, ensure supply chain resilience, track supplier reliability."
            },
            {
              icon: <Truck className="h-8 w-8 text-[#004235]" />,
              title: "Logistics Providers",
              description: "Optimize routes, benchmark service levels, identify high-demand lanes."
            },
            {
              icon: <UserCheck className="h-8 w-8 text-[#004235]" />,
              title: "Agents/Distributors",
              description: "Understand client needs better, identify cross-selling opportunities, provide data-backed advice."
            },
            {
              icon: <Landmark className="h-8 w-8 text-[#004235]" />,
              title: "Financial Institutions & Investors",
              description: "Assess industry risk, identify investment opportunities, monitor commodity trends."
            },
            {
              icon: <Building className="h-8 w-8 text-[#004235]" />,
              title: "Governments & Trade Bodies",
              description: "Track trade flows, monitor compliance, inform policy decisions."
            }
          ].map((item, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="bg-[#F2F2F2] p-3 rounded-full mb-4">
                {item.icon}
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">{item.title}</h3>
              <p className="text-gray-700">{item.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}