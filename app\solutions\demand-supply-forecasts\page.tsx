"use client"

import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"
import HeroSection from "@/components/solutions/demand-supply-forecasts/HeroSection"
import ChallengesSection from "@/components/solutions/demand-supply-forecasts/ChallengesSection"
import ForecastingApproachSection from "@/components/solutions/demand-supply-forecasts/ForecastingApproachSection"
import StrategicBenefitsSection from "@/components/solutions/demand-supply-forecasts/StrategicBenefitsSection"
import StreamResourcesSection from "@/components/solutions/demand-supply-forecasts/StreamResourcesSection"
import CTASection from "@/components/solutions/demand-supply-forecasts/CTASection"

export default function DemandSupplyForecastsPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <ChallengesSection />

      <ForecastingApproachSection />

      <StrategicBenefitsSection />

      <StreamResourcesSection />

      <CTASection />

      <MainFooter />
    </div>
  )
}