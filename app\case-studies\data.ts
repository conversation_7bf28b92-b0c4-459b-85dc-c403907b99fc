import { notFound } from 'next/navigation';

// Define a type for the case study data
interface CaseStudyData {
  slug: string;
  clientName: string;
  caseStudyHeadline: string;
  heroImageUrl: string;
  // Add other fields as per your data structure
}

// Placeholder function to fetch case study data by slug
// In a real application, this would fetch data from a CMS, database, or API
export async function getCaseStudyData(slug: string): Promise<CaseStudyData | null> {
  // This is a placeholder. Replace with actual data fetching logic.
  const MOCK_CASE_STUDIES: CaseStudyData[] = [
    {
      slug: "sample-case-study-1",
      clientName: "Acme Corp",
      caseStudyHeadline: "Revolutionizing Supply Chains with StreamLnk",
      heroImageUrl: "/images/case-studies/sample-hero-1.jpg", // Example path
    },
    {
      slug: "another-success-story",
      clientName: "Beta Solutions",
      caseStudyHeadline: "StreamLnk Drives Efficiency for Beta Solutions",
      heroImageUrl: "/images/case-studies/sample-hero-2.jpg", // Example path
    },
  ];

  const study = MOCK_CASE_STUDIES.find((cs) => cs.slug === slug);
  return study || null;
}

export async function generateMetadata({ params }: { params: { slug: string } }) {
  const caseStudy = await getCaseStudyData(params.slug);
  if (!caseStudy) {
    return {
      title: "Case Study Not Found | StreamLnk",
    };
  }
  return {
    title: `${caseStudy.clientName}: ${caseStudy.caseStudyHeadline} | StreamLnk Success Stories`,
  };
}