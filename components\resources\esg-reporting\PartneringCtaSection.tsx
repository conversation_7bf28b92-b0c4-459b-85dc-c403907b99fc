"use client";

import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight, Users, BarChart2, FileText, MessageSquare } from 'lucide-react';

export default function PartneringCtaSection() {
  return (
    <section className="py-16 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-4">
            StreamLnk: Your Technology Partner for ESG Excellence in Industrial Trade
          </h2>
          <p className="text-xl text-[#028475] font-semibold mb-8">
            Partnering for a Sustainable Future
          </p>
          <p className="text-lg text-gray-700 mb-10">
            We are continuously evolving our platform to provide more robust ESG tracking, reporting, and sustainable sourcing capabilities. We collaborate with industry initiatives, certification bodies, and our users to advance sustainability across the industrial materials ecosystem.
          </p>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white w-full"
              size="lg"
              asChild
            >
              <Link href="/request-demo?solution=esg-reporting&source=resource-cta">
                REQUEST ESG DEMO
                <MessageSquare className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full"
              size="lg"
              asChild
            >
              <Link href="/e-stream/supplier-sustainability">
                SUPPLIER SUSTAINABILITY
                <Users className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Button
              variant="link"
              className="text-[#028475] hover:text-[#004235] w-full justify-start sm:justify-center text-left sm:text-center"
              size="lg"
              asChild
            >
              <Link href="/streamresources/esg-insights">
                EXPLORE ESG INSIGHTS
                <BarChart2 className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              variant="link"
              className="text-[#028475] hover:text-[#004235] w-full justify-start sm:justify-center text-left sm:text-center"
              size="lg"
              asChild
            >
              <Link href="/resources/whitepapers/sustainable-supply-chains">
                READ WHITEPAPER
                <FileText className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>

        </div>
      </div>
    </section>
  );
}