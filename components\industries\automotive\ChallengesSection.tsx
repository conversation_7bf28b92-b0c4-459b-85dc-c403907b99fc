"use client"

import { <PERSON>ert<PERSON><PERSON>gle, Clock, Layers, ShieldCheck, TrendingUp, Globe, Zap } from "lucide-react"

const challenges = [
  {
    icon: <Clock className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Just-in-Time (JIT) Delivery Imperatives",
    description: "Disruptions in raw material or component supply can halt production lines, incurring massive costs.",
  },
  {
    icon: <Layers className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Complex Tiered Supplier Networks",
    description: "Managing visibility and coordination across multiple tiers of suppliers (Tier 1, Tier 2, Tier N).",
  },
  {
    icon: <ShieldCheck className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Stringent Quality & Compliance Standards",
    description: "Adherence to IATF 16949, OEM-specific requirements, and safety regulations for materials and components.",
  },
  {
    icon: <TrendingUp className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Material Price Volatility",
    description: "Fluctuations in prices for polymers, specialty chemicals, metals, and electronic components impacting cost structures.",
  },
  {
    icon: <Globe className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Global Sourcing & Logistics",
    description: "Managing international freight, customs, and warehousing for a diverse range of parts and materials.",
  },
  {
    icon: <Zap className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Push for Sustainability & EV Materials",
    description: "Increasing demand for lightweight materials, recycled content, battery components, and transparent ESG reporting.",
  },
]

export default function ChallengesSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            The High-Stakes Demands of the Automotive Supply Chain
          </h2>
          <p className="text-lg text-gray-700">
            Navigating the Pressures of Automotive Manufacturing & Sourcing?
          </p>
          <p className="text-lg text-gray-700 mt-2">
            The automotive industry operates on tight margins, complex global supply networks, and exacting quality standards, leading to significant challenges:
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {challenges.map((challenge, index) => (
            <div key={index} className="bg-[#F2F2F2] p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
              <div className="flex justify-center md:justify-start">
                {challenge.icon}
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2 text-center md:text-left">{challenge.title}</h3>
              <p className="text-gray-600 text-center md:text-left">{challenge.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}