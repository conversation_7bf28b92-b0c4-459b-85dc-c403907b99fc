import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";

export default function CommonReportingFunctionalitiesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">Common Reporting Functionalities</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"/></svg>
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Filtering & Sorting</h4>
              </div>
              <p className="text-gray-700">Easily refine report data.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"/><line x1="16" y1="2" x2="16" y2="6"/><line x1="8" y1="2" x2="8" y2="6"/><line x1="3" y1="10" x2="21" y2="10"/></svg>
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Date Range Selection</h4>
              </div>
              <p className="text-gray-700">Analyze performance over specific periods.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 3v18h18"/><path d="m19 9-5 5-4-4-3 3"/></svg>
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Visualizations</h4>
              </div>
              <p className="text-gray-700">Integrated charts and graphs within reports for easier understanding.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7 10 12 15 17 10"/><line x1="12" y1="15" x2="12" y2="3"/></svg>
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Downloadable Formats</h4>
              </div>
              <p className="text-gray-700">Export reports to PDF, CSV, or Excel for offline analysis or sharing.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/></svg>
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Scheduled Reports</h4>
              </div>
              <p className="text-gray-700">Option to have key reports automatically generated and emailed (Future Enhancement).</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}