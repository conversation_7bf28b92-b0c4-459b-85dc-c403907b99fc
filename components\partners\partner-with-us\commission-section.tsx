import { DollarSign, TrendingUp, LinkIcon, Shield } from "lucide-react"

export function CommissionSection() {
  const commissionFeatures = [
    {
      icon: <DollarSign className="h-12 w-12 text-[#004235]" />,
      title: "Earn Per Order",
      description:
        "Receive commissions on every successful transaction completed by the clients under your management.",
    },
    {
      icon: <TrendingUp className="h-12 w-12 text-[#004235]" />,
      title: "Performance Bonuses",
      description:
        "Potential for additional bonuses based on achieving targets for sales volume, client retention, and portfolio growth.",
    },
    {
      icon: <LinkIcon className="h-12 w-12 text-[#004235]" />,
      title: "Permanent Client Linkage",
      description:
        "All clients you onboard remain linked to your agent account for commission purposes throughout your tenure.",
    },
    {
      icon: <Shield className="h-12 w-12 text-[#004235]" />,
      title: "Protected Client Base",
      description: "We ensure the clients you bring to the platform are respected as part of your portfolio.",
    },
  ]

  return (
    <section className="py-20 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Commission Structure & Client Ownership
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {commissionFeatures.map((feature, index) => (
            <div
              key={index}
              className="bg-white rounded-lg shadow-md p-6 text-center hover:shadow-xl transition-shadow duration-300"
            >
              <div className="flex justify-center mb-4">{feature.icon}</div>
              <h3 className="text-xl font-semibold text-[#004235] mb-3">{feature.title}</h3>
              <p className="text-gray-700">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
