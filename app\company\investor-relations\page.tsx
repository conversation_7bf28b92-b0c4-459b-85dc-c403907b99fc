import type React from "react"
import Image from "next/image"
import {
  ArrowRight,
  Globe,
  TrendingUp,
  Users,
  LineChart,
  DollarSign,
  FileText,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Target,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Input } from "@/components/ui/input"

import InvestmentThesis from "./components/investment-thesis"
import FinancialReport from "./components/financial-report"
import LeadershipProfile from "./components/leadership-profile"
import NewsItem from "./components/news-item"
import StockChart from "./components/stock-chart"

export default function InvestorRelationsPage() {
  return (
    <main className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-[#F2F2F2] py-16 md:py-24 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="/placeholder.svg?height=800&width=1600"
            alt="Global trade network with upward trend"
            fill
            className="object-cover"
          />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <div className="inline-block rounded-lg bg-[#004235]/10 px-3 py-1 text-sm text-[#004235] mb-4">
              Investor Relations
            </div>
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235] mb-4">
              Investing in the Transformation of Global Industrial Supply Chains
            </h1>
            <p className="text-gray-600 md:text-xl mb-8">
              StreamLnk Inc. is redefining how energy and industrial materials are sourced, moved, and financed
              worldwide. Discover the opportunity to partner with a leader in B2B digital innovation.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button className="bg-[#004235] hover:bg-[#004235]/90">
                View Latest Financial Report <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button variant="outline" className="border-[#028475] text-[#028475]">
                Contact Investor Relations
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Section 1: Welcome to StreamLnk Investor Relations */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
                Building a Global Leader, Delivering Sustainable Value
              </h2>
            </div>
            <div className="space-y-6 text-gray-600">
              <p>
                Welcome to the StreamLnk Inc. Investor Relations hub. As a company poised for significant growth and
                future market leadership, we are committed to transparency, disciplined execution, and creating
                long-term value for our shareholders.
              </p>
              <p>
                StreamLnk is at the forefront of digitizing the multi-trillion-dollar industrial raw materials market.
                Our integrated ecosystem of specialized portals, powered by AI and cutting-edge technology, addresses
                deep-seated inefficiencies in global supply chains. This page provides access to essential information
                about our financial performance, corporate governance, strategic initiatives, and the vast market
                opportunity we are addressing.
              </p>
              <p>
                We believe in a future where industrial trade is seamless, intelligent, and data-driven. We invite you
                to explore how StreamLnk is making this vision a reality and to learn more about becoming a part of our
                growth story.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Section 2: Our Investment Thesis – Why StreamLnk? */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
                The StreamLnk Opportunity: A Compelling Case for Investment
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Discover why StreamLnk represents a unique opportunity in the rapidly evolving landscape of global
                industrial trade.
              </p>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
              <InvestmentThesis
                icon={<Globe className="h-8 w-8 text-[#028475]" />}
                title="Massive & Underserved Market"
                description="Targeting the $700B+ global polymer market initially, with a clear path to expand into the multi-trillion-dollar industrial raw materials sector, which is still largely undigitized."
              />

              <InvestmentThesis
                icon={<NetworkIconComponent className="h-8 w-8 text-[#028475]" />}
                title="First-Mover Advantage in a Unified Ecosystem"
                description="We are building the first truly end-to-end digital platform combining marketplace, logistics, compliance, finance, and data analytics for these complex B2B supply chains."
              />

              <InvestmentThesis
                icon={<TrendingUp className="h-8 w-8 text-[#028475]" />}
                title="Scalable & High-Margin Business Model"
                description="Our platform architecture is designed for global scalability with strong network effects. Revenue streams (transaction fees, subscriptions, data monetization, financial services) offer high-margin potential."
              />

              <InvestmentThesis
                icon={<BrainCircuitIconComponent className="h-8 w-8 text-[#028475]" />}
                title="Proprietary Data & AI Capabilities"
                description="Our ecosystem generates unique, high-value data, powering our AI-driven StreamIndex™ and creating a defensible competitive moat through our StreamResources+ data intelligence division."
              />

              <InvestmentThesis
                icon={<Users className="h-8 w-8 text-[#028475]" />}
                title="Proven Leadership & Strategic Vision"
                description="Led by a team with deep expertise in supply chain, technology, global trade, and finance, with a clear roadmap for phased growth, international expansion, and vertical integration."
              />

              <InvestmentThesis
                icon={<Target className="h-8 w-8 text-[#028475]" />}
                title="Clear Path to Profitability & IPO"
                description="Our phased capital raise strategy and financial projections demonstrate a disciplined approach to growth, targeting profitability and a public market listing within a 5-6 year horizon."
              />
            </div>

            <div className="mt-8 p-6 bg-white rounded-lg border border-[#f3f4f6]">
              <div className="flex items-start gap-4">
                <div className="p-2 rounded-full bg-[#004235]/10 flex-shrink-0">
                  <ScaleIconComponent className="h-6 w-6 text-[#028475]" />
                </div>
                <div>
                  <h3 className="font-semibold text-[#004235] mb-2">Strategic Alignment with Global Trends</h3>
                  <p className="text-gray-600">
                    Benefiting from the megatrends of supply chain digitization, B2B e-commerce adoption, and the
                    increasing need for resilience and transparency in global trade.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 3: Financial Information & Reports */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Performance & Financials</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Access our latest financial disclosures, reports, and presentations.
              </p>
            </div>

            <Tabs defaultValue="quarterly" className="w-full">
              <TabsList className="grid w-full max-w-md mx-auto grid-cols-4 mb-8">
                <TabsTrigger value="quarterly">Quarterly</TabsTrigger>
                <TabsTrigger value="annual">Annual</TabsTrigger>
                <TabsTrigger value="presentations">Presentations</TabsTrigger>
                <TabsTrigger value="filings">Filings</TabsTrigger>
              </TabsList>
              <TabsContent value="quarterly" className="space-y-4">
                <FinancialReport
                  title="Q1 2023 Earnings Report"
                  date="May 15, 2023"
                  description="First quarter financial results and business highlights."
                  fileSize="2.4 MB"
                  downloadLink="#"
                />
                <FinancialReport
                  title="Q4 2022 Earnings Report"
                  date="February 28, 2023"
                  description="Fourth quarter and full year 2022 financial results."
                  fileSize="3.1 MB"
                  downloadLink="#"
                />
                <FinancialReport
                  title="Q3 2022 Earnings Report"
                  date="November 15, 2022"
                  description="Third quarter financial results and business highlights."
                  fileSize="2.8 MB"
                  downloadLink="#"
                />
              </TabsContent>
              <TabsContent value="annual" className="space-y-4">
                <FinancialReport
                  title="2022 Annual Report"
                  date="March 30, 2023"
                  description="Comprehensive overview of StreamLnk's performance and strategic initiatives for fiscal year 2022."
                  fileSize="5.7 MB"
                  downloadLink="#"
                />
                <FinancialReport
                  title="2021 Annual Report"
                  date="March 30, 2022"
                  description="Comprehensive overview of StreamLnk's performance and strategic initiatives for fiscal year 2021."
                  fileSize="5.2 MB"
                  downloadLink="#"
                />
              </TabsContent>
              <TabsContent value="presentations" className="space-y-4">
                <FinancialReport
                  title="Investor Day Presentation"
                  date="June 10, 2023"
                  description="Detailed presentation on StreamLnk's long-term strategy and market opportunity."
                  fileSize="8.3 MB"
                  downloadLink="#"
                />
                <FinancialReport
                  title="Series B Funding Deck"
                  date="January 15, 2023"
                  description="Presentation used during the Series B funding round."
                  fileSize="6.5 MB"
                  downloadLink="#"
                />
              </TabsContent>
              <TabsContent value="filings" className="space-y-4">
                <div className="p-6 bg-white rounded-lg border border-[#f3f4f6]">
                  <p className="text-center text-gray-600">
                    SEC filings will be available after StreamLnk's initial public offering.
                  </p>
                </div>
              </TabsContent>
            </Tabs>

            <div className="mt-12 grid gap-6 md:grid-cols-2">
              <Card className="border-[#f3f4f6]">
                <CardContent className="pt-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 rounded-full bg-[#004235]/10">
                      <LineChart className="h-6 w-6 text-[#028475]" />
                    </div>
                    <h3 className="text-lg font-semibold text-[#004235]">Financial Projections Overview</h3>
                  </div>
                  <p className="text-gray-600 mb-4">
                    StreamLnk is targeting significant revenue growth with a clear path to profitability over the next 5
                    years.
                  </p>
                  <Button variant="outline" className="w-full border-[#028475] text-[#028475]">
                    View 5-Year Financial Model
                  </Button>
                </CardContent>
              </Card>

              <Card className="border-[#f3f4f6]">
                <CardContent className="pt-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 rounded-full bg-[#004235]/10">
                      <DollarSign className="h-6 w-6 text-[#028475]" />
                    </div>
                    <h3 className="text-lg font-semibold text-[#004235]">Capital Raise History & Structure</h3>
                  </div>
                  <p className="text-gray-600 mb-4">
                    Information on past funding rounds – Seed, Series A, Series B, and current status.
                  </p>
                  <Button variant="outline" className="w-full border-[#028475] text-[#028475]">
                    View Funding History
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Section 4: Corporate Governance & Leadership */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
                Commitment to Excellence and Integrity
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                StreamLnk Inc. is dedicated to maintaining the highest standards of corporate governance, ethical
                conduct, and transparency.
              </p>
            </div>

            <div className="mb-12">
              <h3 className="text-xl font-semibold text-[#004235] mb-6 text-center">Board of Directors</h3>
              <div className="grid gap-6 md:grid-cols-3">
                <LeadershipProfile
                  name="Jane Smith"
                  title="Chairperson"
                  imageSrc="/placeholder.svg?height=200&width=200"
                  bio="Former CEO of Global Materials Inc. with 25+ years of experience in industrial supply chains."
                />
                <LeadershipProfile
                  name="John Doe"
                  title="CEO & Director"
                  imageSrc="/placeholder.svg?height=200&width=200"
                  bio="Founder of StreamLnk with extensive background in technology and global trade."
                />
                <LeadershipProfile
                  name="Sarah Johnson"
                  title="Independent Director"
                  imageSrc="/placeholder.svg?height=200&width=200"
                  bio="Former CTO of Tech Innovations with expertise in AI and data analytics."
                />
              </div>
              <div className="mt-6 text-center">
                <Button variant="outline" className="border-[#028475] text-[#028475]">
                  View All Board Members
                </Button>
              </div>
            </div>

            <div className="mb-12">
              <h3 className="text-xl font-semibold text-[#004235] mb-6 text-center">Executive Leadership Team</h3>
              <div className="text-center">
                <Button className="bg-[#004235] hover:bg-[#004235]/90">Meet Our Leadership Team</Button>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-[#004235] mb-6 text-center">Governance Documents</h3>
              <div className="grid gap-4 md:grid-cols-2">
                <Card className="border-[#f3f4f6]">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="p-2 rounded-full bg-[#004235]/10">
                        <FileText className="h-6 w-6 text-[#028475]" />
                      </div>
                      <h4 className="font-semibold text-[#004235]">Corporate Governance Guidelines</h4>
                    </div>
                    <Button variant="outline" className="w-full border-[#028475] text-[#028475]">
                      Download PDF
                    </Button>
                  </CardContent>
                </Card>
                <Card className="border-[#f3f4f6]">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="p-2 rounded-full bg-[#004235]/10">
                        <FileText className="h-6 w-6 text-[#028475]" />
                      </div>
                      <h4 className="font-semibold text-[#004235]">Code of Business Conduct & Ethics</h4>
                    </div>
                    <Button variant="outline" className="w-full border-[#028475] text-[#028475]">
                      Download PDF
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 5: Stock Information */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">StreamLnk on the Market</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Information about StreamLnk's stock performance and market presence.
              </p>
            </div>

            <div className="p-8 bg-white rounded-lg border border-[#f3f4f6] text-center mb-8">
              <p className="text-gray-600">
                StreamLnk is currently a privately-held company. Stock information will be available following our
                initial public offering.
              </p>
            </div>

            <div className="mb-12">
              <h3 className="text-xl font-semibold text-[#004235] mb-6">Projected Stock Performance</h3>
              <div className="bg-white p-4 rounded-lg border border-[#f3f4f6]">
                <StockChart />
                <p className="text-xs text-gray-500 text-center mt-2">
                  *Illustrative purposes only. Not a guarantee of future performance.
                </p>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-[#004235] mb-6">Future Market Information</h3>
              <div className="grid gap-6 md:grid-cols-3">
                <Card className="border-[#f3f4f6]">
                  <CardContent className="pt-6 text-center">
                    <h4 className="font-semibold text-[#004235] mb-2">Stock Ticker Symbol</h4>
                    <p className="text-gray-600">Projected: SLNK</p>
                  </CardContent>
                </Card>
                <Card className="border-[#f3f4f6]">
                  <CardContent className="pt-6 text-center">
                    <h4 className="font-semibold text-[#004235] mb-2">Target Exchange</h4>
                    <p className="text-gray-600">NASDAQ</p>
                  </CardContent>
                </Card>
                <Card className="border-[#f3f4f6]">
                  <CardContent className="pt-6 text-center">
                    <h4 className="font-semibold text-[#004235] mb-2">IPO Timeline</h4>
                    <p className="text-gray-600">5-6 Year Horizon</p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 6: News, Events & Presentations */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
                Stay Informed: Latest Updates from StreamLnk
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Keep up with the latest news, events, and presentations from StreamLnk.
              </p>
            </div>

            <div className="mb-12">
              <h3 className="text-xl font-semibold text-[#004235] mb-6">Press Releases</h3>
              <div className="space-y-4">
                <NewsItem
                  title="StreamLnk Secures $50M in Series B Funding"
                  date="June 15, 2023"
                  description="Funding will accelerate platform development and international expansion."
                  link="#"
                />
                <NewsItem
                  title="StreamLnk Announces Strategic Partnership with Global Logistics Provider"
                  date="May 3, 2023"
                  description="Partnership enhances cross-border shipping capabilities for platform users."
                  link="#"
                />
                <NewsItem
                  title="StreamLnk Launches StreamResources+ Data Intelligence Division"
                  date="April 12, 2023"
                  description="New division will monetize platform data and provide market insights."
                  link="#"
                />
              </div>
              <div className="mt-6 text-center">
                <Button variant="outline" className="border-[#028475] text-[#028475]">
                  View All Press Releases
                </Button>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-[#004235] mb-6">Upcoming Investor Events</h3>
              <div className="grid gap-4 md:grid-cols-2">
                <Card className="border-[#f3f4f6]">
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-3">
                      <div className="p-2 rounded-full bg-[#004235]/10 flex-shrink-0 mt-1">
                        <Calendar className="h-5 w-5 text-[#028475]" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-[#004235] mb-1">Q2 2023 Earnings Call</h4>
                        <p className="text-gray-600 text-sm mb-2">August 15, 2023 | 2:00 PM ET</p>
                        <Button variant="outline" size="sm" className="border-[#028475] text-[#028475]">
                          Add to Calendar
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="border-[#f3f4f6]">
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-3">
                      <div className="p-2 rounded-full bg-[#004235]/10 flex-shrink-0 mt-1">
                        <Calendar className="h-5 w-5 text-[#028475]" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-[#004235] mb-1">Investor Day 2023</h4>
                        <p className="text-gray-600 text-sm mb-2">September 20, 2023 | New York City</p>
                        <Button variant="outline" size="sm" className="border-[#028475] text-[#028475]">
                          Register Now
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 7: Investor FAQs & Contact */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
                Frequently Asked Questions & Investor Contact
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Find answers to common investor questions and learn how to get in touch with our Investor Relations
                team.
              </p>
            </div>

            <div className="mb-12">
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="item-1">
                  <AccordionTrigger className="text-[#004235] hover:text-[#004235]/90">
                    What is StreamLnk's primary revenue model?
                  </AccordionTrigger>
                  <AccordionContent className="text-gray-600">
                    StreamLnk generates revenue through multiple streams: transaction fees on the marketplace,
                    subscription fees for premium platform features, monetization of market data through our
                    StreamResources+ division, and financial services fees for payment processing, escrow, and BNPL
                    options.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-2">
                  <AccordionTrigger className="text-[#004235] hover:text-[#004235]/90">
                    What are the key growth drivers for the company?
                  </AccordionTrigger>
                  <AccordionContent className="text-gray-600">
                    Our growth is driven by increasing platform adoption across our target markets, expansion into new
                    geographic regions, introduction of new product categories beyond our initial focus on polymers,
                    development of additional value-added services, and the scaling of our data monetization business.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-3">
                  <AccordionTrigger className="text-[#004235] hover:text-[#004235]/90">
                    How does StreamLnk differentiate itself from competitors?
                  </AccordionTrigger>
                  <AccordionContent className="text-gray-600">
                    StreamLnk differentiates through our comprehensive end-to-end approach that integrates marketplace,
                    logistics, compliance, finance, and data analytics in a single ecosystem. Unlike point solutions
                    that address only specific parts of the supply chain, we provide a unified platform that creates
                    network effects and generates valuable proprietary data. Our deep industry expertise and focus on
                    complex industrial materials also sets us apart from general B2B marketplaces.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-4">
                  <AccordionTrigger className="text-[#004235] hover:text-[#004235]/90">
                    What is your strategy for international expansion?
                  </AccordionTrigger>
                  <AccordionContent className="text-gray-600">
                    Our international expansion follows a phased approach, starting with our core markets in North
                    America, expanding to Europe and the Middle East, and then into Asia-Pacific. We establish regional
                    hubs with local teams who understand the specific needs and regulatory requirements of each market.
                    We also partner with established players in new regions to accelerate adoption and navigate local
                    complexities.
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-5">
                  <AccordionTrigger className="text-[#004235] hover:text-[#004235]/90">
                    How is AI integrated into your platform?
                  </AccordionTrigger>
                  <AccordionContent className="text-gray-600">
                    AI is core to our platform's functionality and competitive advantage. We use machine learning for
                    intelligent product and supplier matching, predictive pricing, logistics optimization, fraud
                    detection, and market trend analysis. Our StreamIndex™ uses AI to provide real-time and predictive
                    market benchmarks. As we gather more transaction data, our AI capabilities become increasingly
                    sophisticated, creating a virtuous cycle that enhances platform value.
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>

            <div className="grid gap-8 md:grid-cols-2">
              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-6">Investor Relations Contact</h3>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Mail className="h-5 w-5 text-[#028475]" />
                    <span className="text-gray-600"><EMAIL></span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="h-5 w-5 text-[#028475]" />
                    <span className="text-gray-600">+****************</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <MapPin className="h-5 w-5 text-[#028475] mt-1" />
                    <span className="text-gray-600">
                      StreamLnk Inc.
                      <br />
                      123 Innovation Way
                      <br />
                      San Francisco, CA 94105
                    </span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-semibold text-[#004235] mb-6">Sign Up for IR Alerts</h3>
                <Card className="border-[#f3f4f6]">
                  <CardContent className="pt-6">
                    <p className="text-gray-600 mb-4">
                      Stay up-to-date with the latest investor information from StreamLnk.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3">
                      <Input
                        type="email"
                        placeholder="Your Email Address"
                        className="border-[#f3f4f6] focus-visible:ring-[#028475]"
                      />
                      <Button className="bg-[#004235] hover:bg-[#004235]/90 whitespace-nowrap">
                        Subscribe to IR Updates
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500 mt-2">
                      By subscribing, you agree to receive investor relations communications from StreamLnk Inc.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}

function BrainCircuitIconComponent(props: React.ComponentProps<"svg">) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M12 4.5a2.5 2.5 0 0 0-4.96-.46 2.5 2.5 0 0 0-1.98 3 2.5 2.5 0 0 0-1.32 4.24 3 3 0 0 0 .34 5.58 2.5 2.5 0 0 0 2.96 3.08 2.5 2.5 0 0 0 4.91.05L12 20V4.5Z" />
      <path d="M16 8V5c0-1.1.9-2 2-2" />
      <path d="M12 13h4" />
      <path d="M12 18h6a2 2 0 0 1 2 2v1" />
      <path d="M12 8h8" />
      <path d="M20.5 8a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z" />
      <path d="M16.5 13a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z" />
      <path d="M20.5 21a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z" />
      <path d="M18.5 3a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0Z" />
    </svg>
  )
}

function NetworkIconComponent(props: React.ComponentProps<"svg">) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect x="16" y="16" width="6" height="6" rx="1" />
      <rect x="2" y="16" width="6" height="6" rx="1" />
      <rect x="9" y="2" width="6" height="6" rx="1" />
      <path d="M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3" />
      <path d="M12 12V8" />
    </svg>
  )
}

function ScaleIconComponent(props: React.ComponentProps<"svg">) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="m16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z" />
      <path d="m2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z" />
      <path d="M7 21h10" />
      <path d="M12 3v18" />
      <path d="M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2" />
    </svg>
  )
}
