import { CountrySelector } from "@/components/country-selector"
import { MainNav } from "@/components/main-nav"
import { MainFooter } from "@/components/main-footer"
import { HeroSection } from "@/components/partners/promote-services/hero-section"
import { WhyPromoteSection } from "@/components/partners/promote-services/why-promote-section"
import { WhoShouldListSection } from "@/components/partners/promote-services/who-should-list-section"
import { WhereSeenSection } from "@/components/partners/promote-services/where-seen-section"
import { ServiceListingSection } from "@/components/partners/promote-services/service-listing-section"
import { SmartMatchingSection } from "@/components/partners/promote-services/smart-matching-section"
import { PremiumPromotionSection } from "@/components/partners/promote-services/premium-promotion-section"
import { HowToJoinSection } from "@/components/partners/promote-services/how-to-join-section"
import { CtaSection } from "@/components/partners/promote-services/cta-section"

export default function PromoteServicesPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <CountrySelector />
      <MainNav />

      <main>
        {/* Hero Section */}
        <HeroSection />

        {/* Why Promote Section */}
        <WhyPromoteSection />

        {/* Who Should List Section */}
        <WhoShouldListSection />

        {/* Where Services Will Be Seen Section */}
        <WhereSeenSection />

        {/* Service Listing Section */}
        <ServiceListingSection />

        {/* Smart Matching Section */}
        <SmartMatchingSection />

        {/* Premium Promotion Section */}
        <PremiumPromotionSection />

        {/* How to Join Section */}
        <HowToJoinSection />

        {/* CTA Section */}
        <CtaSection />
      </main>

      <MainFooter />
    </div>
  )
}
