import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"

interface LeadershipProfileProps {
  name: string
  title: string
  imageSrc: string
  bio: string
}

export default function LeadershipProfile({ name, title, imageSrc, bio }: LeadershipProfileProps) {
  return (
    <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
      <CardContent className="pt-6 flex flex-col items-center text-center">
        <div className="relative w-24 h-24 rounded-full overflow-hidden mb-4">
          <Image src={imageSrc || "/placeholder.svg"} alt={name} fill className="object-cover" />
        </div>
        <h3 className="font-semibold text-[#004235] mb-1">{name}</h3>
        <p className="text-sm text-[#028475] mb-3">{title}</p>
        <p className="text-sm text-gray-600">{bio}</p>
      </CardContent>
    </Card>
  )
}
