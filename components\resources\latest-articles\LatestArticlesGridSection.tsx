"use client";

import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface ArticleCardProps {
  slug: string;
  imageUrl: string;
  category: string;
  title: string;
  excerpt: string;
  author: string;
  date: string;
}

const ArticleCard: React.FC<ArticleCardProps> = ({ slug, imageUrl, category, title, excerpt, author, date }) => {
  return (
    <Link href={`/resources/latest-articles/${slug}`} className="block group bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden">
      <div className="relative w-full h-48">
        <Image
          src={imageUrl}
          alt={title}
          fill
          className="object-cover group-hover:scale-105 transition-transform duration-300"
        />
      </div>
      <div className="p-5">
        <span className="inline-block bg-[#028475] text-white text-xs font-semibold px-2 py-1 rounded-full mb-2">
          {category}
        </span>
        <h3 className="text-lg font-semibold text-[#004235] mb-2 group-hover:text-[#028475] transition-colors duration-300">
          {title}
        </h3>
        <p className="text-gray-600 text-sm mb-3 line-clamp-3">
          {excerpt}
        </p>
        <p className="text-xs text-gray-500 mb-3">
          By {author} | {date}
        </p>
        <span className="text-sm font-medium text-[#004235] group-hover:text-[#028475] transition-colors duration-300">
          Read More &rarr;
        </span>
      </div>
    </Link>
  );
};

const sampleArticles: ArticleCardProps[] = [
  {
    slug: "navigating-port-congestion-q4",
    imageUrl: "/images/placeholder-article-1.jpg", // Placeholder
    category: "Logistics",
    title: "Navigating Port Congestion: Strategies for Q4",
    excerpt: "Key insights and proactive measures to mitigate the impact of global port congestion on your supply chain during the peak season.",
    author: "Jane Doe",
    date: "October 24, 2023",
  },
  {
    slug: "streamindex-price-transparency-chemicals",
    imageUrl: "/images/placeholder-article-2.jpg", // Placeholder
    category: "Technology",
    title: "How StreamIndex™ is Bringing Price Transparency to Industrial Chemicals",
    excerpt: "A deep dive into the innovative StreamIndex™ platform and its role in revolutionizing price discovery for industrial chemical procurement.",
    author: "John Smith",
    date: "October 22, 2023",
  },
  {
    slug: "rise-of-recycled-polymers-challenges-opportunities",
    imageUrl: "/images/placeholder-article-3.jpg", // Placeholder
    category: "Sustainability",
    title: "The Rise of Recycled Polymers: Sourcing Challenges & Opportunities",
    excerpt: "Exploring the growing demand for recycled polymers, the complexities in sourcing, and how businesses can navigate this evolving market.",
    author: "Alice Brown",
    date: "October 20, 2023",
  },
  {
    slug: "ai-in-supply-chain-future-trends",
    imageUrl: "/images/placeholder-article-4.jpg", // Placeholder
    category: "AI Innovation",
    title: "The Future of AI in Supply Chain Management",
    excerpt: "Examining the transformative potential of Artificial Intelligence in optimizing logistics, demand forecasting, and overall supply chain efficiency.",
    author: "Dr. Alex Chen",
    date: "October 18, 2023",
  },
  {
    slug: "esg-reporting-industrial-trade",
    imageUrl: "/images/placeholder-article-5.jpg", // Placeholder
    category: "Sustainability & ESG",
    title: "ESG Reporting: A New Imperative for Industrial Trade",
    excerpt: "Understanding the growing importance of Environmental, Social, and Governance (ESG) reporting and its implications for industrial businesses.",
    author: "Maria Garcia",
    date: "October 15, 2023",
  },
  {
    slug: "digital-transformation-logistics",
    imageUrl: "/images/placeholder-article-6.jpg", // Placeholder
    category: "Technology",
    title: "Embracing Digital Transformation in Logistics",
    excerpt: "How technology is reshaping the logistics landscape, from automated warehousing to data-driven route optimization.",
    author: "David Lee",
    date: "October 12, 2023",
  },
];

export default function LatestArticlesGridSection() {
  // Basic pagination logic (can be expanded with state management)
  const currentPage = 1;
  const totalPages = Math.ceil(sampleArticles.length / 6); // Assuming 6 articles per page

  return (
    <section className="py-12 md:py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
          Latest Articles
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {sampleArticles.slice(0, 6).map((article) => (
            <ArticleCard key={article.slug} {...article} />
          ))}
        </div>

        {/* Pagination */} 
        {totalPages > 1 && (
          <div className="mt-12 flex justify-center items-center space-x-2">
            <Button
              variant="outline"
              size="icon"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-5 w-5" />
              <span className="sr-only">Previous Page</span>
            </Button>
            {[...Array(totalPages)].map((_, i) => (
              <Button
                key={i + 1}
                variant={currentPage === i + 1 ? "default" : "outline"}
                className={
                  currentPage === i + 1
                    ? "bg-[#004235] text-white hover:bg-[#028475]"
                    : "border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
                }
              >
                {i + 1}
              </Button>
            ))}
            <Button
              variant="outline"
              size="icon"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-5 w-5" />
              <span className="sr-only">Next Page</span>
            </Button>
          </div>
        )}
      </div>
    </section>
  );
}