import { Card, CardContent } from "@/components/ui/card"
import { Award, Star, Layout, BarChart3, LinkIcon } from "lucide-react"

export function PremiumPromotionSection() {
  const premiumFeatures = [
    {
      icon: <Award className="h-10 w-10 text-[#028475]" />,
      title: "Sponsored Service Slots",
      description: "Feature your listing prominently on relevant search result pages and order configuration screens.",
    },
    {
      icon: <Star className="h-10 w-10 text-[#028475]" />,
      title: "Featured Listings",
      description: "Appear on country-specific, industry-specific, or service-category dashboards.",
    },
    {
      icon: <Layout className="h-10 w-10 text-[#028475]" />,
      title: "White-Labeled Dashboards",
      description: "Potential to offer StreamLnk-powered analytics and KPIs directly to your clients.",
    },
    {
      icon: <BarChart3 className="h-10 w-10 text-[#028475]" />,
      title: "Inclusion in StreamResources+ Reports",
      description: "Be cited or featured in global benchmarking reports and industry insights publications.",
    },
    {
      icon: <LinkIcon className="h-10 w-10 text-[#028475]" />,
      title: "Affiliate & Integration Tools",
      description:
        "Opportunities for deeper integration, direct quoting tools, co-branded BNPL solutions, or embedded supply chain finance offerings.",
    },
  ]

  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Add-On Premium Promotion Opportunities:
          </h2>
          <p className="text-lg text-gray-700 mb-12 max-w-4xl">
            StreamLnk offers premium service partners enhanced visibility options to further boost their presence:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {premiumFeatures.map((feature, index) => (
              <Card key={index} className="border-none shadow-md hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="rounded-full bg-[#004235]/10 p-3 mr-4">{feature.icon}</div>
                    <h3 className="text-xl font-semibold text-[#004235]">{feature.title}</h3>
                  </div>
                  <p className="text-gray-600">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
