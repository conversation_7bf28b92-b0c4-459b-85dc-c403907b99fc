import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"

interface PartnerSpotlightProps {
  logo: string
  name: string
  quote: string
}

export default function PartnerSpotlight({ logo, name, quote }: PartnerSpotlightProps) {
  return (
    <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
      <CardContent className="pt-6">
        <div className="flex flex-col items-center text-center space-y-4">
          <div className="relative w-[200px] h-[100px]">
            <Image src={logo || "/placeholder.svg"} alt={name} fill className="object-contain" />
          </div>
          <h3 className="text-lg font-semibold text-[#004235]">{name}</h3>
          <div className="relative">
            <svg
              className="absolute top-0 left-0 transform -translate-x-3 -translate-y-2 h-6 w-6 text-[#028475]/30"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M14.017 18L14.017 10.609C14.017 4.905 17.748 1.039 23 0L23.995 2.151C21.563 3.068 20 5.789 20 8H24V18H14.017ZM0 18V10.609C0 4.905 3.748 1.039 9 0L9.996 2.151C7.563 3.068 6 5.789 6 8H9.983L9.983 18L0 18Z" />
            </svg>
            <blockquote className="relative z-10 px-6 py-2 italic text-gray-600">{quote}</blockquote>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
