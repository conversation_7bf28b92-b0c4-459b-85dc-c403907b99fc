import type React from "react"
import { G<PERSON>l, FileText, ClipboardCheck, Bell, FileIcon as FileInvoice, BarChart2 } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

interface Feature {
  icon: React.ReactNode
  title: string
  description: string
}

export function FeaturesSection() {
  const features: Feature[] = [
    {
      icon: <Gavel className="h-8 w-8 text-[#028475]" />,
      title: "Bid on Available Jobs",
      description: "Browse and bid competitively on shipments that match your region, capability, and equipment.",
    },
    {
      icon: <FileText className="h-8 w-8 text-[#028475]" />,
      title: "View Full Shipment Details",
      description:
        "Access comprehensive information for each job, including route details, pickup/drop-off instructions, and cargo specifications.",
    },
    {
      icon: <ClipboardCheck className="h-8 w-8 text-[#028475]" />,
      title: "Manage Compliance Documents",
      description:
        "Upload, store, and update your insurance certificates, operating permits, driver certifications, and other essential compliance paperwork.",
    },
    {
      icon: <Bell className="h-8 w-8 text-[#028475]" />,
      title: "Receive Automated Notifications",
      description:
        "Get timely alerts for new job opportunities, bid status updates, document expirations, and important system messages.",
    },
    {
      icon: <FileInvoice className="h-8 w-8 text-[#028475]" />,
      title: "Submit Invoices & Track Payments",
      description:
        "Generate and submit invoices directly through the portal and monitor payment status via your AR Dashboard.",
    },
    {
      icon: <BarChart2 className="h-8 w-8 text-[#028475]" />,
      title: "Analyze Performance",
      description:
        "Review your historical earnings, job acceptance rates, on-time performance trends, and other key metrics.",
    },
  ]

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-8 text-center">
          What You Can Do in the StreamFreight Portal
        </h2>
        <p className="text-lg text-gray-700 max-w-4xl mx-auto mb-12 text-center">
          The StreamFreight portal is your command center for managing all aspects of your partnership:
        </p>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="border-t-4 border-t-[#028475]">
              <CardContent className="p-6">
                <div className="mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">{feature.title}</h3>
                <p className="text-gray-700">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-8 text-center">
          <p className="text-lg text-gray-700">
            Stay informed with alerts for job reassignments due to inactivity or significant delays, helping you
            maintain service levels.
          </p>
        </div>
      </div>
    </section>
  )
}
