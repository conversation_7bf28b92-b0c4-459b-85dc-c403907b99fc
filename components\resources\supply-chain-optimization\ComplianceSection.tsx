"use client";

import { Shield<PERSON>heck, FileText, Users, CreditCard } from 'lucide-react';

export default function ComplianceSection() {
  return (
    <section className="py-12 md:py-20 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-4 text-center">
            Embedding Compliance & Risk Mitigation
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center leading-relaxed">
            Global trade is inherently exposed to various risks, including fraud, geopolitical disruptions, and regulatory non-compliance. Verifying partners and managing extensive documentation can be a burdensome and error-prone process.
          </p>

          <div className="bg-white p-8 rounded-xl shadow-lg">
            <h3 className="text-2xl font-semibold text-[#004235] mb-3">
              StreamLnk Solution: Integrated Trust and Security
            </h3>
            <p className="text-gray-700 mb-6 leading-relaxed">
              StreamLnk integrates trust and security directly into the platform fabric. We conduct thorough KYC/AML (Know Your Customer/Anti-Money Laundering) verification for all platform participants. Automated document management and our proprietary iScore™ partner ratings ensure you are working with reliable and vetted entities. StreamIndex™ provides valuable risk indicators based on market data, and secure payment options, including Escrow and Buy Now Pay Later (BNPL) services, mitigate financial risks.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="flex items-start">
                <ShieldCheck className="h-8 w-8 text-[#028475] mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Reduced Fraud & Non-Compliance</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">Mitigate risks with verified partners and automated checks.</p>
                </div>
              </div>
              <div className="flex items-start">
                <Users className="h-8 w-8 text-[#028475] mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Enhanced Partner Reliability</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">Utilize iScore™ ratings for informed decisions.</p>
                </div>
              </div>
              <div className="flex items-start">
                <FileText className="h-8 w-8 text-[#028475] mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Automated Document Management</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">Streamline compliance paperwork and audit trails.</p>
                </div>
              </div>
              <div className="flex items-start">
                <CreditCard className="h-8 w-8 text-[#028475] mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-[#004235] mb-1">Improved Security</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">Leverage secure payment options and risk indicators.</p>
                </div>
              </div>
            </div>
            <p className="text-md text-gray-800 font-medium leading-relaxed">
              This comprehensive approach results in significantly reduced exposure to fraud and non-compliance, enhanced partner reliability through data-driven insights, and improved overall security for your global trade operations.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}