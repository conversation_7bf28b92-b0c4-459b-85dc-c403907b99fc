import Image from "next/image"
import {
  <PERSON><PERSON><PERSON>,
  Calendar,
  CheckCircle2,
  Clock,
  DollarSign,
  File<PERSON>heck,
  LineChart,
  Wallet,
  Building2,
  <PERSON><PERSON>hart3,
  Truck,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import BnplStep from "./components/bnpl-step"
import BenefitCard from "./components/benefit-card"
import EligibilityCard from "./components/eligibility-card"
import IntegrationCard from "./components/integration-card"
import UsageStep from "./components/usage-step"

export default function BnplPage() {
  return (
    <main className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-[#F2F2F2] py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
            <div className="flex flex-col justify-center space-y-4">
              <div className="inline-block rounded-lg bg-[#004235]/10 px-3 py-1 text-sm text-[#004235]">
                Flexible Payment Solutions
              </div>
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235]">
                Buy Now Pay Later (BNPL) Options
              </h1>
              <p className="text-gray-600 md:text-xl">
                Get the flexibility to secure critical materials immediately while deferring payment according to
                pre-approved terms, enabling faster decisions and stronger cash flow.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button className="bg-[#004235] hover:bg-[#004235]/90">
                  Check Eligibility <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                <Button variant="outline" className="border-[#028475] text-[#028475]">
                  Learn More
                </Button>
              </div>
            </div>
            <div className="flex justify-center">
              <div className="relative w-full max-w-[500px] h-[300px] md:h-[400px]">
                <Image
                  src="/placeholder.svg?height=400&width=500"
                  alt="Buy Now Pay Later illustration"
                  fill
                  className="object-contain"
                  priority
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How BNPL Works Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">How BNPL Works on StreamLnk</h2>
            <p className="text-gray-600 max-w-3xl">
              Our Buy Now Pay Later solution streamlines procurement while maintaining healthy cash flow for your
              business.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <BnplStep
              icon={<Calendar className="h-8 w-8 text-[#028475]" />}
              title="Request BNPL Terms at Checkout"
              description="Eligible buyers can opt to pay 30, 45, or 60 days after delivery."
            />
            <BnplStep
              icon={<LineChart className="h-8 w-8 text-[#028475]" />}
              title="Credit Assessment in Real-Time"
              description="Our platform performs automatic eligibility checks using trade history, StreamIndex score, and external credit data."
            />
            <BnplStep
              icon={<CheckCircle2 className="h-8 w-8 text-[#028475]" />}
              title="Instant Supplier Notification"
              description="Once approved, suppliers receive payment confirmation and dispatch instructions as if payment has already cleared."
            />
            <BnplStep
              icon={<DollarSign className="h-8 w-8 text-[#028475]" />}
              title="StreamLnk Settles the Invoice"
              description="The supplier is paid upfront; the buyer repays StreamLnk on the agreed timeline."
            />
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Benefits of BNPL</h2>
            <p className="text-gray-600 max-w-3xl">
              Our BNPL solution offers significant advantages for both buyers and suppliers.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2">
            <div>
              <div className="flex items-center gap-3 mb-6">
                <Building2 className="h-6 w-6 text-[#028475]" />
                <h3 className="text-xl font-semibold text-[#004235]">Benefits for Buyers</h3>
              </div>
              <div className="grid gap-4">
                <BenefitCard
                  icon={<Wallet className="h-6 w-6 text-[#028475]" />}
                  title="Improved Cash Flow"
                  description="Secure materials without immediate cash outflow"
                />
                <BenefitCard
                  icon={<FileCheck className="h-6 w-6 text-[#028475]" />}
                  title="Consolidated Billing"
                  description="Consolidate multiple shipments under one deferred billing cycle"
                />
                <BenefitCard
                  icon={<BarChart3 className="h-6 w-6 text-[#028475]" />}
                  title="Better Planning"
                  description="Improve working capital and budget planning"
                />
              </div>
            </div>

            <div>
              <div className="flex items-center gap-3 mb-6">
                <Truck className="h-6 w-6 text-[#028475]" />
                <h3 className="text-xl font-semibold text-[#004235]">Benefits for Suppliers</h3>
              </div>
              <div className="grid gap-4">
                <BenefitCard
                  icon={<DollarSign className="h-6 w-6 text-[#028475]" />}
                  title="Immediate Payment"
                  description="Get paid upfront with no delay or risk"
                />
                <BenefitCard
                  icon={<Building2 className="h-6 w-6 text-[#028475]" />}
                  title="Expanded Customer Base"
                  description="Expand buyer base without onboarding credit risks"
                />
                <BenefitCard
                  icon={<CheckCircle2 className="h-6 w-6 text-[#028475]" />}
                  title="Focus on Core Business"
                  description="Focus on fulfillment, not collections"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Eligibility & Risk Controls Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Eligibility & Risk Controls</h2>
            <p className="text-gray-600 max-w-3xl">
              StreamLnk maintains high standards for BNPL eligibility to ensure a healthy ecosystem.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <EligibilityCard
              icon={<Building2 className="h-8 w-8 text-[#028475]" />}
              title="Verified Business Accounts"
              description="Available only to verified business accounts"
            />
            <EligibilityCard
              icon={<LineChart className="h-8 w-8 text-[#028475]" />}
              title="StreamIndex Score"
              description="StreamIndex score must meet threshold"
            />
            <EligibilityCard
              icon={<Wallet className="h-8 w-8 text-[#028475]" />}
              title="BNPL Limits"
              description="BNPL limits assigned based on transaction volume and payment history"
            />
            <EligibilityCard
              icon={<Clock className="h-8 w-8 text-[#028475]" />}
              title="Late Payment Policies"
              description="Late fees or suspensions may apply for past-due accounts"
            />
          </div>
        </div>
      </section>

      {/* Integration with Treasury Tools Section */}
      <section className="py-16 md:py-24 bg-[#F2F2F2]">
        <div className="container px-4 md:px-6">
          <div className="grid gap-12 lg:grid-cols-2 items-center">
            <div>
              <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-6">Integration with Treasury Tools</h2>
              <p className="text-gray-600 mb-8">
                Manage all your BNPL activities seamlessly through our comprehensive Treasury Dashboard.
              </p>

              <div className="space-y-4">
                <IntegrationCard
                  icon={<BarChart3 className="h-6 w-6 text-[#028475]" />}
                  title="Comprehensive Dashboard"
                  description="View all BNPL activity in the Treasury Dashboard"
                />
                <IntegrationCard
                  icon={<Calendar className="h-6 w-6 text-[#028475]" />}
                  title="Payment Monitoring"
                  description="Monitor upcoming due dates, outstanding balances, and payment reminders"
                />
                <IntegrationCard
                  icon={<FileCheck className="h-6 w-6 text-[#028475]" />}
                  title="Export Capabilities"
                  description="Export BNPL invoices and summaries for your ERP or accounting system"
                />
              </div>
            </div>
            <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
              <Image
                src="/placeholder.svg?height=400&width=600"
                alt="Treasury dashboard interface"
                fill
                className="object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* How to Use BNPL Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">How to Use BNPL</h2>
            <p className="text-gray-600 max-w-3xl">
              Getting started with StreamLnk's Buy Now Pay Later option is simple and straightforward.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-4">
            <UsageStep
              number={1}
              title="Confirm Eligibility"
              description="Check your eligibility in your MyStreamLnk finance profile"
            />
            <UsageStep
              number={2}
              title="Select BNPL Option"
              description="Choose BNPL during checkout or quote acceptance"
            />
            <UsageStep
              number={3}
              title="Agree to Terms"
              description="Select payment terms (30, 45, or 60 days post-delivery)"
            />
            <UsageStep
              number={4}
              title="Manage Repayment"
              description="Track order and manage repayment via Treasury Center"
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-[#F2F2F2]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Smart Financing for Industrial Growth
            </h2>
            <p className="text-gray-600 max-w-3xl mb-8">
              StreamLnk's BNPL tools help qualified buyers scale faster—without straining cash flow or compromising
              supplier relationships.
            </p>
            <Button className="bg-[#004235] hover:bg-[#004235]/90 px-8 py-6 text-lg">Check Your Eligibility</Button>
          </div>
        </div>
      </section>
    </main>
  )
}
