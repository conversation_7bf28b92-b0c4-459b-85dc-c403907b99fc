import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON> } from "lucide-react"
import Link from "next/link"

export function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl">
          {/* Optional: Re-add if a specific badge is desired, styled consistently */}
          {/* <div className=\"inline-block rounded-lg bg-[#028475] px-3 py-1 text-sm text-white mb-4\">
            StreamGlobe Customs Agent Program
          </div> */}
          <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
            Power Global Trade: Become a Certified StreamLnk Clearance Partner
          </h1>
          <p className="text-xl md:text-2xl text-[#028475] mb-8">
            Seamlessly connect to international shipments, access auto-populated documentation, and provide real-time
            clearance updates. StreamGlobe is your gateway to digitizing and expanding your customs brokerage
            services.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button size="lg" className="bg-[#004235] hover:bg-[#028475] text-white transition-colors" asChild>
              <Link href="#apply">
                Apply Now <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white transition-colors"
              asChild
            >
              <Link href="#features">Explore Features</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
