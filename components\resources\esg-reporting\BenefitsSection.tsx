"use client";

import { <PERSON><PERSON><PERSON><PERSON>, TrendingUp, ShieldCheck, Users, Zap, Leaf } from 'lucide-react';

export default function BenefitsSection() {
  const benefits = [
    {
      icon: <Leaf className="text-[#028475] h-8 w-8 mr-4 flex-shrink-0" />,
      title: "Achieve Corporate Sustainability Targets",
      description: "Actively manage and improve the ESG performance of your supply chain."
    },
    {
      icon: <TrendingUp className="text-[#028475] h-8 w-8 mr-4 flex-shrink-0" />,
      title: "Simplify ESG Reporting",
      description: "Access readily available data and downloadable reports for your internal and external disclosures (e.g., CSRD, sustainability reports)."
    },
    {
      icon: <ShieldCheck className="text-[#028475] h-8 w-8 mr-4 flex-shrink-0" />,
      title: "Enhance Brand Value & Reputation",
      description: "Demonstrate a credible commitment to responsible and sustainable business practices."
    },
    {
      icon: <Zap className="text-[#028475] h-8 w-8 mr-4 flex-shrink-0" />,
      title: "Mitigate ESG-Related Risks",
      description: "Improve visibility into environmental and social risks within your supply chain."
    },
    {
      icon: <Users className="text-[#028475] h-8 w-8 mr-4 flex-shrink-0" />,
      title: "Attract ESG-Focused Investors & Customers",
      description: "Align your operations with the growing demand for sustainable businesses."
    },
    {
      icon: <CheckCircle className="text-[#028475] h-8 w-8 mr-4 flex-shrink-0" />,
      title: "Drive Positive Impact",
      description: "Contribute to a more environmentally and socially responsible global industrial ecosystem."
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-4 text-center">
            Drive Sustainability, Enhance Transparency, Meet Reporting Needs
          </h2>
          <p className="text-xl text-center text-[#028475] font-semibold mb-12">
            Benefits of Using StreamLnk's ESG Reporting Tools
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="bg-[#f3f4f6] p-6 rounded-lg shadow-md flex items-start">
                {benefit.icon}
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">{benefit.title}</h3>
                  <p className="text-gray-700 text-md">{benefit.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}