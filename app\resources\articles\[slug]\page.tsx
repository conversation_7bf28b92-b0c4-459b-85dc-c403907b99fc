"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import Link from "next/link";
import Image from "next/image"; // Assuming Next.js Image component for optimized images
import { Button } from "@/components/ui/button";
import { Share2, Rss, ChevronRight } from "lucide-react"; // Icons for sharing and subscription

// Placeholder data - this would come from a CMS or API in a real application
const articleData = {
  headline: "[Actual News Article Headline]",
  summary: "StreamLnk announces strategic partnership with [Partner Name] to enhance global logistics capabilities...",
  city: "[City, Country]",
  publicationDate: "[Date of Publication]",
  author: "[Author Name, e.g., StreamLnk Communications Team]",
  featuredImageUrl: "/placeholder-image.jpg", // Replace with actual image path or URL
  featuredVideoUrl: null, // Or a video URL
  introduction: [
    "This is the first paragraph of the introduction, setting the stage for the news.",
    "The second paragraph of the introduction provides a bit more context or a key takeaway."
  ],
  detailedExplanation: [
    {
      subheading: "Subheading for Section 1 (Optional)",
      paragraphs: [
        "Paragraph 1 under subheading 1. This section delves deeper into the specifics of the announcement.",
        "Paragraph 2 under subheading 1. It might include data, further explanations, or examples."
      ]
    },
    {
      subheading: "Subheading for Section 2 (Optional)",
      paragraphs: [
        "Paragraph 1 under subheading 2. This could cover another aspect of the news.",
        "Paragraph 2 under subheading 2. Elaborating on the implications or benefits."
      ]
    }
  ],
  quotes: [
    {
      quote: "This partnership marks a significant milestone for us, enabling greater efficiency and reach.",
      attribution: "[Name], [Title] at StreamLnk"
    },
    {
      quote: "We are excited to collaborate with StreamLnk to innovate within the logistics sector.",
      attribution: "[Name], [Title] at [Partner Name]"
    }
  ],
  impactAndOutlook: [
    "The first paragraph discussing the potential impact of this news and what it means for the industry or customers.",
    "The second paragraph looking towards the future, outlining next steps or long-term vision related to this announcement."
  ],
  slug: "actual-news-article-headline" // This would be dynamic based on the article
};

const relatedArticles = [
  { title: "Related Article 1", href: "/resources/articles/related-article-1" },
  { title: "Related Article 2", href: "/resources/articles/related-article-2" },
  { title: "Another Interesting Piece", href: "/resources/articles/another-interesting-piece" },
];

export default function IndividualArticlePage({ params }: { params: { slug: string } }) {
  // In a real app, you would fetch articleData based on params.slug
  const article = articleData; // Using placeholder for now

  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      {/* SEO Head Information - In a real Next.js app, use <Head> or Metadata API */}
      {/* <title>{`${article.headline} | StreamLnk News`}</title> */}
      {/* <meta name="description" content={article.summary} /> */}

      <main className="flex-grow">
        {/* Breadcrumbs */}
        <section className="bg-[#F2F2F2] py-4 border-b border-gray-200">
          <div className="container mx-auto px-4">
            <nav className="text-sm text-gray-600 flex items-center space-x-2">
              <Link href="/" className="hover:text-[#004235]">Home</Link>
              <ChevronRight className="h-4 w-4 text-gray-400" />
              <Link href="/press-center" className="hover:text-[#004235]">Press Center & Media</Link> {/* Assuming /press-center is the correct path */}
              <ChevronRight className="h-4 w-4 text-gray-400" />
              <Link href="/resources/articles" className="hover:text-[#004235]">News</Link> {/* Assuming /resources/articles is the news listing */}
              <ChevronRight className="h-4 w-4 text-gray-400" />
              <span className="font-medium text-[#004235]">{article.headline}</span>
            </nav>
          </div>
        </section>

        <div className="container mx-auto px-4 py-12 md:py-16">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 lg:gap-12">
            {/* Main Article Content */}
            <article className="lg:col-span-8">
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#004235] mb-3">
                {article.headline}
              </h1>
              <p className="text-lg md:text-xl text-gray-700 mb-6">
                {/* Optional, slightly smaller, providing more context - this is the summary */}
                {article.summary}
              </p>
              <div className="text-sm text-gray-500 mb-6 border-b pb-4">
                <span>{article.city} – {article.publicationDate}</span>
                <span className="mx-2">|</span>
                <span>By {article.author}</span>
              </div>

              {/* Featured Image or Video */}
              {article.featuredImageUrl && (
                <div className="mb-8 rounded-lg overflow-hidden shadow-lg">
                  <Image 
                    src={article.featuredImageUrl} 
                    alt={`Featured image for ${article.headline}`} 
                    width={800} 
                    height={450} 
                    className="w-full h-auto object-cover"
                    priority // Good for LCP
                  />
                </div>
              )}
              {/* TODO: Add video embed component if article.featuredVideoUrl exists */}

              {/* Body of the News Article */}
              <div className="prose prose-lg max-w-none text-gray-800">
                {/* Introduction */}
                {article.introduction.map((paragraph, index) => (
                  <p key={`intro-${index}`}>{paragraph}</p>
                ))}

                {/* Detailed Explanation */}
                {article.detailedExplanation.map((section, sectionIndex) => (
                  <div key={`section-${sectionIndex}`}>
                    {section.subheading && <h2 className="text-2xl font-semibold text-[#004235] mt-6 mb-3">{section.subheading}</h2>}
                    {section.paragraphs.map((paragraph, paraIndex) => (
                      <p key={`section-${sectionIndex}-para-${paraIndex}`}>{paragraph}</p>
                    ))}
                  </div>
                ))}

                {/* Quotes */}
                {article.quotes.length > 0 && (
                  <div className="my-8">
                    {article.quotes.map((quoteItem, index) => (
                      <blockquote key={`quote-${index}`} className="border-l-4 border-[#028475] pl-6 py-2 my-6 bg-[#f3f4f6] rounded-r-md">
                        <p className="text-xl italic text-gray-700 leading-relaxed">"{quoteItem.quote}"</p>
                        <footer className="mt-2 text-sm text-gray-600">– {quoteItem.attribution}</footer>
                      </blockquote>
                    ))}
                  </div>
                )}

                {/* Impact & Future Outlook */}
                <h2 className="text-2xl font-semibold text-[#004235] mt-8 mb-3">Impact & Future Outlook</h2>
                {article.impactAndOutlook.map((paragraph, index) => (
                  <p key={`outlook-${index}`}>{paragraph}</p>
                ))}
              </div>

              {/* About StreamLnk (Boilerplate) */}
              <div className="mt-12 pt-8 border-t border-gray-200">
                <h3 className="text-xl font-semibold text-[#004235] mb-2">About StreamLnk</h3>
                <p className="text-gray-700 text-sm">
                  StreamLnk is a leading innovator in global trade and logistics solutions, empowering businesses with advanced technology and data-driven insights to optimize their supply chains and achieve sustainable growth. We connect buyers and sellers worldwide through our comprehensive platform, fostering transparency and efficiency in industrial commerce.
                </p>
              </div>

              {/* Contact Information */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <h3 className="text-xl font-semibold text-[#004235] mb-2">Media Inquiries</h3>
                <p className="text-gray-700 text-sm">
                  Press Contact Name (if applicable)<br />
                  Email: <a href="mailto:<EMAIL>" className="text-[#028475] hover:text-[#004235]"><EMAIL></a><br />
                  {/* Phone: (Optional) */}
                </p>
                <p className="mt-4 text-sm">
                  <Link href="/press-center" className="text-[#028475] hover:text-[#004235] font-medium">
                    Visit our Press Center
                  </Link>
                </p>
              </div>

              {/* End of Article Marker */}
              <div className="mt-12 text-center text-gray-400">
                --- END --- 
              </div>

            </article>

            {/* Sidebar (Optional) */}
            <aside className="lg:col-span-4 space-y-8">
              {/* Share This Article */}
              <div className="bg-[#f3f4f6] p-6 rounded-lg shadow-sm">
                <h3 className="text-lg font-semibold text-[#004235] mb-4">Share This Article</h3>
                <div className="flex space-x-3">
                  {/* Replace with actual share links/buttons */}
                  <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white flex-1">
                    <Share2 className="h-4 w-4 mr-2" /> LinkedIn
                  </Button>
                  <Button variant="outline" className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white flex-1">
                    <Share2 className="h-4 w-4 mr-2" /> X (Twitter)
                  </Button>
                  {/* Add more share options as needed (e.g., Facebook, Email) */}
                </div>
              </div>

              {/* Related News/Articles */}
              {relatedArticles.length > 0 && (
                <div className="bg-[#f3f4f6] p-6 rounded-lg shadow-sm">
                  <h3 className="text-lg font-semibold text-[#004235] mb-4">Related News</h3>
                  <ul className="space-y-3">
                    {relatedArticles.map((related, index) => (
                      <li key={`related-${index}`} className="border-b border-gray-300 pb-3 last:border-b-0 last:pb-0">
                        <Link href={related.href} className="text-gray-700 hover:text-[#028475] hover:underline">
                          {related.title}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Subscribe to News Alerts */}
              <div className="bg-[#004235] p-6 rounded-lg shadow-sm text-white">
                <h3 className="text-lg font-semibold mb-3">Stay Updated</h3>
                <p className="text-sm mb-4 text-gray-200">Get the latest StreamLnk news and insights directly in your inbox.</p>
                {/* Basic form, can be expanded with actual form handling */}
                <form className="space-y-3">
                  <input 
                    type="email" 
                    placeholder="Enter your email" 
                    className="w-full px-3 py-2 rounded-md text-gray-800 border border-gray-300 focus:ring-2 focus:ring-[#028475] focus:border-transparent"
                  />
                  <Button type="submit" className="w-full bg-[#028475] hover:bg-opacity-90 text-white">
                    <Rss className="h-4 w-4 mr-2" /> Subscribe Now
                  </Button>
                </form>
              </div>
            </aside>
          </div>
        </div>
      </main>

      <BottomFooter />
    </div>
  );
}