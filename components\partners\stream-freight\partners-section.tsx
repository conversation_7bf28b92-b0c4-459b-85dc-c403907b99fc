import type React from "react"
import { Truck, Users, MapPin, Globe, FlaskRoundIcon as Flask, Shield } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

interface PartnerType {
  icon: React.ReactNode
  title: string
}

export function PartnersSection() {
  const partnerTypes: PartnerType[] = [
    {
      icon: <Users className="h-10 w-10 text-[#028475]" />,
      title: "Independent Truckers and Owner-Operators",
    },
    {
      icon: <Truck className="h-10 w-10 text-[#028475]" />,
      title: "Small, Medium, and Large Trucking Fleets",
    },
    {
      icon: <MapPin className="h-10 w-10 text-[#028475]" />,
      title: "Last-Mile and Regional Logistics Providers",
    },
    {
      icon: <Globe className="h-10 w-10 text-[#028475]" />,
      title: "Cross-Border Freight Carriers",
    },
    {
      icon: <Truck className="h-10 w-10 text-[#028475] transform rotate-90" />,
      title: "Rail Operators and Bulk Transporters",
    },
    {
      icon: <Flask className="h-10 w-10 text-[#028475]" />,
      title: "Specialized Delivery Partners",
    },
    {
      icon: <Shield className="h-10 w-10 text-[#028475]" />,
      title: "Freight Dispatchers and Brokers",
    },
  ]

  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-8 text-center">
          Who Should Join StreamFreight?
        </h2>
        <p className="text-lg text-gray-700 max-w-4xl mx-auto mb-12 text-center">
          We invite a diverse range of logistics providers to partner with us:
        </p>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {partnerTypes.map((partner, index) => (
            <Card key={index} className="border-none shadow-md hover:shadow-lg transition-shadow">
              <CardContent className="p-6 flex flex-col items-center text-center">
                <div className="mb-4 p-4 bg-white rounded-full shadow-sm">{partner.icon}</div>
                <h3 className="text-lg font-semibold text-[#004235]">{partner.title}</h3>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
