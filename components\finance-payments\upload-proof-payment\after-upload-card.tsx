import type { ReactNode } from "react"
import { Card, CardContent } from "@/components/ui/card"

interface AfterUploadCardProps {
  icon: ReactNode
  title: string
  description: string
}

export default function AfterUploadCard({ icon, title, description }: AfterUploadCardProps) {
  return (
    <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
      <CardContent className="pt-6 flex flex-col items-center text-center space-y-4">
        <div className="p-3 rounded-full bg-[#004235]/10">{icon}</div>
        <h3 className="text-lg font-semibold text-[#004235]">{title}</h3>
        <p className="text-gray-600">{description}</p>
      </CardContent>
    </Card>
  )
}
