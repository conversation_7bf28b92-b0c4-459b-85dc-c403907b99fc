import Image from "next/image"
import { <PERSON><PERSON><PERSON>, CheckCircle2, <PERSON>, FileCheck, Shield, AlertCircle, FileText, Bell, Lock } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import UploadRequirement from "./components/upload-requirement"
import UploadStep from "./components/upload-step"
import AfterUploadCard from "./components/after-upload-card"
import SecurityFeature from "./components/security-feature"
import UploadForm from "./components/upload-form"

export default function UploadProofOfPaymentPage() {
  return (
    <main className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-[#F2F2F2] py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
            <div className="flex flex-col justify-center space-y-4">
              <div className="inline-block rounded-lg bg-[#004235]/10 px-3 py-1 text-sm text-[#004235]">
                Payment Documentation
              </div>
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235]">
                Upload Proof of Payment
              </h1>
              <p className="text-gray-600 md:text-xl">
                Securely upload proof of payment through our platform to ensure your orders move forward without delay,
                confirm your transaction, and speed up coordination with partners.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button className="bg-[#004235] hover:bg-[#004235]/90">
                  Upload Now <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                <Button variant="outline" className="border-[#028475] text-[#028475]">
                  Learn More
                </Button>
              </div>
            </div>
            <div className="flex justify-center">
              <div className="relative w-full max-w-[500px] h-[300px] md:h-[400px]">
                <Image
                  src="/placeholder.svg?height=400&width=500"
                  alt="Upload proof of payment illustration"
                  fill
                  className="object-contain"
                  priority
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Upload Proof of Payment Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Why Upload Proof of Payment?</h2>
            <p className="text-gray-600 max-w-3xl">
              Uploading your proof of payment provides several benefits to ensure smooth order processing.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <div className="bg-white p-6 rounded-lg shadow-sm border border-[#f3f4f6] hover:border-[#028475] transition-colors">
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="p-3 rounded-full bg-[#004235]/10">
                  <Clock className="h-8 w-8 text-[#028475]" />
                </div>
                <h3 className="text-xl font-semibold text-[#004235]">Accelerates Shipment Release</h3>
                <p className="text-gray-600">Orders are flagged for fulfillment upon POP upload.</p>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-[#f3f4f6] hover:border-[#028475] transition-colors">
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="p-3 rounded-full bg-[#004235]/10">
                  <CheckCircle2 className="h-8 w-8 text-[#028475]" />
                </div>
                <h3 className="text-xl font-semibold text-[#004235]">Improves Payment Visibility</h3>
                <p className="text-gray-600">
                  Buyers, suppliers, and StreamLnk support teams get real-time confirmation.
                </p>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-[#f3f4f6] hover:border-[#028475] transition-colors">
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="p-3 rounded-full bg-[#004235]/10">
                  <AlertCircle className="h-8 w-8 text-[#028475]" />
                </div>
                <h3 className="text-xl font-semibold text-[#004235]">Avoids Delays</h3>
                <p className="text-gray-600">
                  Missing POPs can delay customs filing, freight bookings, or warehouse dispatch.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* What to Upload Section */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">What to Upload</h2>
            <p className="text-gray-600 max-w-3xl">Ensure your uploaded documents include the following information:</p>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <UploadRequirement icon={<FileText />} text="Bank name and logo" />
            <UploadRequirement icon={<FileText />} text="Sender and recipient account details" />
            <UploadRequirement icon={<FileText />} text="Amount transferred and currency" />
            <UploadRequirement icon={<FileText />} text="Date of transfer" />
            <UploadRequirement icon={<FileText />} text="Payment reference or invoice number" />
            <UploadRequirement icon={<FileText />} text="Confirmation stamp or tracking ID (if applicable)" />
          </div>

          <div className="mt-8 p-4 bg-white rounded-lg border border-[#f3f4f6] flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-3">
              <FileCheck className="h-6 w-6 text-[#028475]" />
              <span className="text-gray-700">Accepted formats: PDF, PNG, JPG</span>
            </div>
            <div className="flex items-center gap-3">
              <FileCheck className="h-6 w-6 text-[#028475]" />
              <span className="text-gray-700">Maximum file size: 10MB</span>
            </div>
          </div>
        </div>
      </section>

      {/* Upload Form Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Upload Your Proof of Payment</h2>
            <p className="text-gray-600 max-w-3xl">Use the form below to upload your proof of payment document.</p>
          </div>

          <div className="max-w-3xl mx-auto">
            <UploadForm />
          </div>
        </div>
      </section>

      {/* How to Upload Section */}
      <section className="py-16 md:py-24 bg-[#F2F2F2]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">How to Upload POP on StreamLnk</h2>
            <p className="text-gray-600 max-w-3xl">Follow these simple steps to upload your proof of payment.</p>
          </div>

          <div className="grid gap-6 md:grid-cols-5">
            <UploadStep
              number={1}
              title="Navigate to Payments"
              description="Go to the Payments or Order Details section of your portal (MyStreamLnk or E-Stream)"
            />
            <UploadStep number={2} title="Click Upload" description="Click on Upload Proof of Payment" />
            <UploadStep number={3} title="Attach Receipt" description="Attach your bank receipt or wire confirmation" />
            <UploadStep number={4} title="Match to Order" description="Match it to the correct invoice or order ID" />
            <UploadStep number={5} title="Submit" description="Submit. Confirmation email will follow" />
          </div>
        </div>
      </section>

      {/* What Happens After Upload Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">What Happens After Upload</h2>
            <p className="text-gray-600 max-w-3xl">
              Once you've uploaded your proof of payment, several processes are initiated.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <AfterUploadCard
              icon={<CheckCircle2 className="h-8 w-8 text-[#028475]" />}
              title="Dashboard Confirmation"
              description="A payment confirmation will appear in your dashboard"
            />
            <AfterUploadCard
              icon={<Bell className="h-8 w-8 text-[#028475]" />}
              title="Partner Notification"
              description="Suppliers and logistics providers are notified instantly"
            />
            <AfterUploadCard
              icon={<AlertCircle className="h-8 w-8 text-[#028475]" />}
              title="Issue Resolution"
              description="If there are issues, StreamLnk Support will contact you"
            />
            <AfterUploadCard
              icon={<Clock className="h-8 w-8 text-[#028475]" />}
              title="Workflow Resumption"
              description="Shipment and documentation workflows resume immediately once POP is validated"
            />
          </div>
        </div>
      </section>

      {/* Security & Compliance Section */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Security & Compliance</h2>
            <p className="text-gray-600 max-w-3xl">We take the security of your financial documents seriously.</p>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <SecurityFeature
              icon={<Lock className="h-8 w-8 text-[#028475]" />}
              title="Encrypted Storage"
              description="All uploads are encrypted and stored securely"
            />
            <SecurityFeature
              icon={<Shield className="h-8 w-8 text-[#028475]" />}
              title="Restricted Access"
              description="Only verified users and assigned parties can access uploaded POPs"
            />
            <SecurityFeature
              icon={<FileCheck className="h-8 w-8 text-[#028475]" />}
              title="Audit Trail"
              description="Audit trail is automatically logged for financial compliance"
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-[#F2F2F2]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Keep Your Supply Chain Moving</h2>
            <p className="text-gray-600 max-w-3xl mb-8">
              Uploading proof of payment ensures your supply chain doesn't get held up by administrative delays. Take
              control and keep your orders on track.
            </p>
            <Button className="bg-[#004235] hover:bg-[#004235]/90 px-8 py-6 text-lg">Upload Proof of Payment</Button>
          </div>
        </div>
      </section>
    </main>
  )
}
