import { Globe, Shield, CreditCard, DollarSign } from "lucide-react";
import PaymentMethodCard from "./payment-method-card"; // Assuming this component will be moved or path adjusted

export default function PaymentMethodsSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
            Supported Payment Methods & Currencies
          </h2>
          <p className="text-gray-600 max-w-3xl">
            StreamLnk supports a wide range of payment methods and currencies to facilitate global trade.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <PaymentMethodCard
            icon={<CreditCard className="h-10 w-10 text-[#028475]" />}
            title="Wire Transfers"
            features={[
              "Accept and send payments via SWIFT, SEPA, Fedwire, and domestic ACH networks",
              "Secure bank-to-bank settlement supported in over 120 countries",
            ]}
          />
          <PaymentMethodCard
            icon={<Globe className="h-10 w-10 text-[#028475]" />}
            title="Multi-Currency Transactions"
            features={[
              "Trade in your preferred currency, including USD, EUR, GBP, AED, JPY, CAD, MXN, CNY, and more",
              "Currency auto-detection at checkout and during quote acceptance",
            ]}
          />
          <PaymentMethodCard
            icon={<DollarSign className="h-10 w-10 text-[#028475]" />}
            title="Currency Pairing Flexibility"
            features={[
              "Pay in one currency, receive in another—StreamLnk handles conversion",
              "FX rates updated in real-time, with transparent margins",
            ]}
          />
          <PaymentMethodCard
            icon={<Shield className="h-10 w-10 text-[#028475]" />}
            title="Secure & Transparent"
            features={[
              "All payments are encrypted and verified via banking-level security standards",
              "StreamLnk's treasury operations include AML checks, beneficiary validation, and fraud monitoring",
              "Automatic reconciliation of invoices to your preferred ledger or ERP system",
            ]}
          />
        </div>
      </div>
    </section>
  );
}