"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox"; // Assuming Checkbox exists or can be created
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"; // Assuming Select components exist
import { Mail, Send } from "lucide-react";
import { useState } from 'react';

export default function NewsletterSection() {
  const [email, setEmail] = useState('');
  const [selectedIndustry, setSelectedIndustry] = useState('');
  const [showIndustrySelect, setShowIndustrySelect] = useState(false);

  const industries = [
    { value: "polymers-plastics", label: "Polymers & Plastics" },
    { value: "industrial-chemicals", label: "Industrial Chemicals" },
    { value: "energy", label: "Energy" },
    { value: "automotive", label: "Automotive Supply Chain" },
    { value: "packaging", label: "Packaging Solutions" },
    { value: "engineering-manufacturing", label: "Engineering & Manufacturing" },
    { value: "life-sciences-healthcare", label: "Life Sciences & Healthcare Logistics" },
    { value: "construction-materials", label: "Construction Materials" },
    { value: "agricultural-commodities", label: "Agricultural Commodities" },
    { value: "recycling-sustainable-materials", label: "Recycling & Sustainable Materials" },
    { value: "technology-electronics", label: "Technology & Electronics Components" },
    { value: "all", label: "All Industries" },
  ];

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // Handle newsletter subscription logic here
    console.log({ email, interestedInIndustry: showIndustrySelect, industry: selectedIndustry });
    alert(`Subscribed with ${email} for ${showIndustrySelect && selectedIndustry ? selectedIndustry : 'general'} insights!`);
    setEmail('');
    setSelectedIndustry('');
    setShowIndustrySelect(false);
  };

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto text-center bg-[#F2F2F2] p-8 md:p-12 rounded-xl shadow-xl">
          <Mail className="h-12 w-12 text-[#028475] mb-6 mx-auto" />
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Get the Latest Industrial Trade Intelligence Delivered to Your Inbox.
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            Stay Informed: Subscribe to our "StreamLnk Insights Newsletter" for regular updates on market trends, new reports, upcoming webinars, and platform innovations.
          </p>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Input 
                type="email" 
                placeholder="Your Email Address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required 
                className="w-full p-3 text-lg border-gray-300 focus:border-[#004235] focus:ring-[#004235] rounded-md"
              />
            </div>
            
            <div className="flex items-center justify-center space-x-2">
              <Checkbox 
                id="industryInterest"
                checked={showIndustrySelect}
                onCheckedChange={() => setShowIndustrySelect(!showIndustrySelect)}
              />
              <label 
                htmlFor="industryInterest" 
                className="text-sm font-medium text-gray-700 cursor-pointer"
              >
                I'm interested in insights for a specific industry
              </label>
            </div>

            {showIndustrySelect && (
              <div>
                <Select onValueChange={setSelectedIndustry} value={selectedIndustry}>
                  <SelectTrigger className="w-full p-3 text-lg border-gray-300 focus:border-[#004235] focus:ring-[#004235] rounded-md text-left">
                    <SelectValue placeholder="Select Industry" />
                  </SelectTrigger>
                  <SelectContent>
                    {industries.map(industry => (
                      <SelectItem key={industry.value} value={industry.value}>
                        {industry.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div>
              <Button 
                type="submit"
                className="bg-[#004235] hover:bg-[#028475] text-white w-full text-lg py-3"
                size="lg"
              >
                SUBSCRIBE TO INSIGHTS
                <Send className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
}