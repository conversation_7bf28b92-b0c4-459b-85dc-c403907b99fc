import type { ReactNode } from "react"

interface UploadRequirementProps {
  icon: ReactNode
  text: string
}

export default function UploadRequirement({ icon, text }: UploadRequirementProps) {
  return (
    <div className="flex items-center gap-3 p-4 bg-white rounded-lg border border-[#f3f4f6]">
      <div className="text-[#028475]">{icon}</div>
      <span className="text-gray-700">{text}</span>
    </div>
  )
}
