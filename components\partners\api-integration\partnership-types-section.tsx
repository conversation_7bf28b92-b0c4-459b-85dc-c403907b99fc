"use client"

import ReusableTabs, { type ReusableTabData } from "@/components/ui/reusable-tabs";
import { Card, CardContent } from "@/components/ui/card";
import { ShieldCheck, Truck, BarChart3, CreditCard, LineChart } from "lucide-react";

// Content component for Risk & Compliance
const RiskComplianceContent = () => (
  <Card className="border-0 shadow-none bg-transparent">
    <CardContent className="p-0">
      <div className="flex items-center mb-4">
        <ShieldCheck className="h-8 w-8 text-[#028475] mr-3" />
        <h3 className="text-2xl font-bold text-[#004235]">Risk & Compliance Platforms</h3>
      </div>
      <ul className="space-y-3 text-gray-700">
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Integrate with credit scoring engines for buyers, suppliers, and agents.
        </li>
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Facilitate sanctions and blacklist screening through API calls.
        </li>
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Exchange KYC/AML (Know Your Customer/Anti-Money Laundering) data for enhanced due diligence.
        </li>
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Partner on compliance or risk engines to provide embedded solutions.
        </li>
      </ul>
    </CardContent>
  </Card>
);

// Content component for Freight & Logistics
const FreightLogisticsContent = () => (
  <Card className="border-0 shadow-none bg-transparent">
    <CardContent className="p-0">
      <div className="flex items-center mb-4">
        <Truck className="h-8 w-8 text-[#028475] mr-3" />
        <h3 className="text-2xl font-bold text-[#004235]">
          Freight Visibility, Telematics & Logistics Systems
        </h3>
      </div>
      <ul className="space-y-3 text-gray-700">
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Share and consume real-time carrier lane data (e.g., integrations similar to Project44, FourKites).
        </li>
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Enable delivery status tracking API integrations for end-to-end visibility.
        </li>
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Provide predictive ETA (Estimated Time of Arrival) updates for end-users.
        </li>
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Share data on freight availability, shortages, and regional demand.
        </li>
      </ul>
    </CardContent>
  </Card>
);

// Content component for Pricing Providers
const PricingProvidersContent = () => (
  <Card className="border-0 shadow-none bg-transparent">
    <CardContent className="p-0">
      <div className="flex items-center mb-4">
        <BarChart3 className="h-8 w-8 text-[#028475] mr-3" />
        <h3 className="text-2xl font-bold text-[#004235]">Trade & Commodity Pricing Providers</h3>
      </div>
      <ul className="space-y-3 text-gray-700">
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Integrate market benchmarks from leading providers (e.g., ICIS, Platts, OPIS).
        </li>
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Offer live price feeds for polymers, chemicals, energy products, and offgrades within StreamLnk.
        </li>
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Become a StreamIndex™ partner, contributing to or consuming our proprietary regional velocity and pricing data.
        </li>
      </ul>
    </CardContent>
  </Card>
);

// Content component for Fintech & Payments
const FintechPaymentsContent = () => (
  <Card className="border-0 shadow-none bg-transparent">
    <CardContent className="p-0">
      <div className="flex items-center mb-4">
        <CreditCard className="h-8 w-8 text-[#028475] mr-3" />
        <h3 className="text-2xl font-bold text-[#004235]">Fintech, Payment & Trade Finance Providers</h3>
      </div>
      <ul className="space-y-3 text-gray-700">
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Integrate for milestone-based escrow payment release mechanisms.
        </li>
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Provide solutions for cross-border treasury visibility and management.
        </li>
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Embed BNPL (Buy Now, Pay Later) and invoice financing engines for StreamLnk users.
        </li>
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Facilitate API monetization partnerships for service expansion in the financial domain.
        </li>
      </ul>
    </CardContent>
  </Card>
);

// Content component for Data Analytics
const DataAnalyticsContent = () => (
  <Card className="border-0 shadow-none bg-transparent">
    <CardContent className="p-0">
      <div className="flex items-center mb-4">
        <LineChart className="h-8 w-8 text-[#028475] mr-3" />
        <h3 className="text-2xl font-bold text-[#004235]">
          Data Analytics & Business Intelligence (BI) Platforms
        </h3>
      </div>
      <ul className="space-y-3 text-gray-700">
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Enable data export from StreamLnk into popular BI tools (e.g., Snowflake, Tableau, Power BI).
        </li>
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Offer DaaS (Data-as-a-Service) API endpoints for subscription-based dashboards.
        </li>
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Partner on custom analytics solutions tailored to specific industry verticals.
        </li>
        <li className="flex items-start">
          <span className="text-[#028475] mr-2">•</span>
          Integrate AI/ML models for predictive insights on market trends, pricing, and logistics.
        </li>
      </ul>
    </CardContent>
  </Card>
);

const partnershipTabsData: ReusableTabData[] = [
  {
    id: "risk",
    title: "Risk & Compliance",
    icon: ShieldCheck,
    contentComponent: RiskComplianceContent,
  },
  {
    id: "freight",
    title: "Freight & Logistics",
    icon: Truck,
    contentComponent: FreightLogisticsContent,
  },
  {
    id: "pricing",
    title: "Pricing Providers",
    icon: BarChart3,
    contentComponent: PricingProvidersContent,
  },
  {
    id: "fintech",
    title: "Fintech & Payments",
    icon: CreditCard,
    contentComponent: FintechPaymentsContent,
  },
  {
    id: "analytics",
    title: "Data Analytics",
    icon: LineChart,
    contentComponent: DataAnalyticsContent,
  },
];

export function PartnershipTypesSection() {
  return (
    <section id="partnerships" className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <div className="mb-12 text-center">
            <h2 className="text-3xl font-bold text-[#004235] mb-4">Types of Partnerships We Support</h2>
            <div className="w-20 h-1 bg-[#028475] mx-auto mb-6"></div>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              We offer a range of opportunities for third-party platforms and data providers to integrate with
              StreamLnk's ecosystem, exchanging critical pricing, logistics, and compliance intelligence.
            </p>
          </div>

          <ReusableTabs
            tabsData={partnershipTabsData}
            defaultTabId="risk"
            // Props to match ReusableTabs default visual style or StreamLnk branding
            underlineColor="bg-[#004235]" // StreamLnk primary color (already default in ReusableTabs)
            activeTabTextColor="text-[#004235]" // StreamLnk primary color (already default in ReusableTabs)
            inactiveTabTextColor="text-[#004235]" // StreamLnk secondary color (already default in ReusableTabs)
            activeTabBgColor="bg-white" // (already default in ReusableTabs)
            inactiveTabBgColor="bg-[#f3f4f6]" // (already default in ReusableTabs)
            hoverTabBgColor="hover:bg-gray-200" // (already default in ReusableTabs)
            // The ReusableTabs component's TabsList is flex, not grid. This is a design change.
            // The py-3 for TabTriggers is handled by ReusableTabs's default py-6, or can be customized via tabTriggerClassName.
            // The flex-col for icon and text in trigger is not default in ReusableTabs, it's row (space-x-2).
            // We are adopting ReusableTabs design, so icon and text will be in a row.
            tabTriggerClassName="py-4 md:py-6" // Adjust padding if needed, ReusableTabs default is py-6
            tabContentClassName="mt-0 bg-[#F2F2F2] p-6 rounded-lg" // Apply original content background and padding here
          />
        </div>
      </div>
    </section>
  );
}
