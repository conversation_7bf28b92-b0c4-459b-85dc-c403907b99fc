"use client"

import { Users, Package, Truck, Archive, Ship, type LucideIcon } from "lucide-react";
import React from "react";
import CustomerPortals from "@/components/portals/customer-portals";
import SupplyPortals from "@/components/portals/supply-portals";
import LogisticsPortals from "@/components/portals/logistics-portals";
import ResourcePortals from "@/components/portals/resource-portals";
import FreightPortals from "@/components/portals/freight-portals";
import ReusableTabs, { type ReusableTabData } from "@/components/ui/reusable-tabs";


const portalTabsData: ReusableTabData[] = [
  {
    id: "customer-portals",
    title: "StreamLnk eCommerce",
    icon: Users,
    contentComponent: CustomerPortals,
  },
  {
    id: "supply-portals",
    title: "StreamLnk Supply Chain",
    icon: Package,
    contentComponent: SupplyPortals,
  },
  {
    id: "freight-portals",
    title: "StreamLnk Freight",
    icon: Truck,
    contentComponent: FreightPortals,
  },
  {
    id: "logistics-portals",
    title: "StreamLnk Global Forwarding",
    icon: Ship,
    contentComponent: LogisticsPortals,
  },
  {
    id: "resource-portal",
    title: "StreamLnk Resources",
    icon: Archive,
    contentComponent: ResourcePortals,
  },
]

export default function PortalsSection() {
  return (
    <div className="mb-16">
      <h2 className="text-[#004235] text-3xl font-bold mb-8 text-center">StreamLnk Portals</h2>
      <ReusableTabs 
        tabsData={portalTabsData} 
        defaultTabId="risk" 
        underlineColor="bg-[#004235]" 
        activeTabTextColor="text-[#004235]" 
        inactiveTabTextColor="text-[#004235]" 
        activeTabBgColor="bg-white" 
        inactiveTabBgColor="bg-[#f3f4f6]" 
        hoverTabBgColor="hover:bg-gray-200" 
        tabTriggerClassName="py-4 md:py-6" 
        tabContentClassName="mt-0 bg-[#F2F2F2] p-6 rounded-lg" 
      />
    </div>
  )
}
