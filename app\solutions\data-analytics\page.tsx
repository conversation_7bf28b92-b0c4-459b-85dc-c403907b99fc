"use client"

import { MainNav } from "@/components/main-nav"
import { <PERSON>Footer } from "@/components/main-footer"
import HeroSection from "@/components/solutions/data-analytics/HeroSection"
import ChallengesSection from "@/components/solutions/data-analytics/ChallengesSection"
import EcosystemInsightsSection from "@/components/solutions/data-analytics/EcosystemInsightsSection"
import DataTransformationProcessSection from "@/components/solutions/data-analytics/DataTransformationProcessSection"
import StakeholderBenefitsSection from "@/components/solutions/data-analytics/StakeholderBenefitsSection"
import SubscriptionPlansSection from "@/components/solutions/data-analytics/SubscriptionPlansSection"
import CTASection from "@/components/solutions/data-analytics/CTASection"

export default function DataAnalyticsPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <ChallengesSection />

      <EcosystemInsightsSection />

      <DataTransformationProcessSection />

      <StakeholderBenefitsSection />

      <SubscriptionPlansSection />

      <CTASection />

      <MainFooter />
    </div>
  )
}