import React from 'react';

interface TimelineStep {
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface TimelineProps {
  steps: TimelineStep[];
}

export function Timeline({ steps }: TimelineProps) {
  return (
    <div className="relative">
      {/* Process Steps Line - visible on medium screens and up */}
      <div className="hidden md:block absolute left-1/2 top-0 bottom-0 w-1 bg-[#028475] transform -translate-x-1/2"></div>

      <div className="space-y-12 relative">
        {steps.map((step, index) => (
          <div key={index} className="flex flex-col items-center md:flex-row md:relative md:items-stretch">
            {/* Text Content Box */}
            <div
              className={`w-full max-w-xl md:max-w-none md:w-1/2 ${
                index % 2 === 0
                  ? "md:pr-[56px] md:text-right"
                  : "md:pl-[56px] md:ml-auto"
              } order-2 md:order-none`}
            >
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-xl font-semibold text-[#004235] mb-2">{step.title}</h3>
                <p className="text-gray-700">{step.description}</p>
              </div>
            </div>

            {/* Icon */}
            <div
              className={`order-1 md:order-none my-4 md:my-0 md:absolute md:left-1/2 md:top-1/2 md:transform md:-translate-x-1/2 md:-translate-y-1/2 z-10`}
            >
              <div className="bg-[#004235] rounded-full p-3 shadow-lg">
                {step.icon}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}