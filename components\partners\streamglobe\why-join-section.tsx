import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, BarC<PERSON>, <PERSON><PERSON><PERSON>, Repeat } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

export function WhyJoinSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235]">
              Why Join StreamGlobe?
            </h2>
            <p className="mx-auto max-w-[700px] text-gray-600 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              Partnering with StreamGlobe offers significant advantages for customs professionals
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 mt-12">
          {[
            {
              icon: <Briefcase className="h-10 w-10 text-[#028475]" />,
              title: "Receive Pre-Assigned Shipments",
              description:
                "Get a consistent flow of real-world customs clearance jobs for various ports and cross-border movements.",
            },
            {
              icon: <FileCheck className="h-10 w-10 text-[#028475]" />,
              title: "One-Click Document Access",
              description: "Instantly see all relevant shipment documents in one organized package upon assignment.",
            },
            {
              icon: <ArrowUpRight className="h-10 w-10 text-[#028475]" />,
              title: "Streamlined POA Management",
              description: "Easily upload your Power of Attorney (POA) and other compliance paperwork.",
            },
            {
              icon: <Bell className="h-10 w-10 text-[#028475]" />,
              title: "Automated Reminders",
              description: "Stay on top of deadlines and missing documents with AI-powered alerts.",
            },
            {
              icon: <Repeat className="h-10 w-10 text-[#028475]" />,
              title: "System Synchronization",
              description:
                "Effortlessly sync your clearance statuses with port systems and customer delivery timelines.",
            },
            {
              icon: <BarChart className="h-10 w-10 text-[#028475]" />,
              title: "Maintain Transparent Performance",
              description: "Showcase your efficiency with metrics on clearance time and accuracy.",
            },
          ].map((item, index) => (
            <Card key={index} className="border-none shadow-md">
              <CardContent className="p-6 flex flex-col items-center text-center">
                <div className="mb-4">{item.icon}</div>
                <h3 className="text-xl font-bold mb-2 text-[#004235]">{item.title}</h3>
                <p className="text-gray-600">{item.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
