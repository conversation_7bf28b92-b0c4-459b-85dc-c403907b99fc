"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/resources/HeroSection";
import IntroductionSection from "@/components/resources/IntroductionSection";
import ResourceCategoriesSection from "@/components/resources/ResourceCategoriesSection";
import FeaturedResourcesSection from "@/components/resources/FeaturedResourcesSection";
import SubscriptionSection from "@/components/resources/SubscriptionSection";
import ContactSection from "@/components/resources/ContactSection";

export default function ResourceHubPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <IntroductionSection />
      <ResourceCategoriesSection />
      <FeaturedResourcesSection />
      <SubscriptionSection />
      <ContactSection />

      <BottomFooter />
    </div>
  );
}