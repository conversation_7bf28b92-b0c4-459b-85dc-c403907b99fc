import { Building, Leaf, Globe, BarChart3, <PERSON><PERSON><PERSON>ck, ShieldCheck, Truck } from "lucide-react";

export default function CapabilitiesSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Source Responsibly, Report Transparently: StreamLnk's ESG Capabilities
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamLnk is committed to enabling more sustainable industrial trade. We are building ESG considerations directly into our platform, providing tools for both buyers and suppliers:
          </p>

          <div className="space-y-8">
            {/* Supplier ESG Profile & Self-Declaration */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Building className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Supplier ESG Profile & Self-Declaration (E-Stream)</h3>
                  <ul className="space-y-2 text-gray-700 mb-4">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Suppliers can showcase their ESG certifications (e.g., ISO 14001, Fair Trade, specific recycled content certifications).</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Option to self-declare adherence to specific ESG policies and upload supporting documentation.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Opportunity to provide Product Carbon Footprint (PCF) data for their materials.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Product ESG Tagging & Filtering */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Leaf className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Product ESG Tagging & Filtering (MyStreamLnk - Buyer Portal)</h3>
                  <ul className="space-y-2 text-gray-700 mb-4">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Products tagged with clear ESG attributes: "Recycled Content," "Bio-Based," "Low Carbon Production," "Ethically Sourced" (based on verified supplier data).</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Buyers can filter product searches based on these ESG criteria.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Estimated Carbon Footprint for Orders */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Globe className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Estimated Carbon Footprint for Orders (MyStreamLnk & MyStreamLnk+)</h3>
                  <ul className="space-y-2 text-gray-700 mb-4">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>During quoting, StreamLnk provides an estimated CO2e impact for the material (if PCF data available) and the selected logistics option.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Highlights "Lower Emission" logistics routes.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* ESG Impact Center */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <BarChart3 className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">ESG Impact Center (MyStreamLnk - Buyer Portal)</h3>
                  <ul className="space-y-2 text-gray-700 mb-4">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>A dedicated dashboard for buyers to track the aggregated ESG impact of their purchases made through StreamLnk (e.g., total CO2e savings vs. baseline, % of spend on sustainable materials).</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Downloadable ESG sourcing reports for their internal corporate reporting.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Traceability Solutions */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <FileCheck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Traceability Solutions (Future Enhancement - Blockchain)</h3>
                  <p className="text-gray-700 mb-4">
                    Exploring blockchain integration for enhanced traceability of recycled materials and validation of sustainability claims.
                  </p>
                </div>
              </div>
            </div>

            {/* iScore ESG Component */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <ShieldCheck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">iScore™ ESG Component (Future Enhancement)</h3>
                  <p className="text-gray-700 mb-4">
                    Integrating key ESG performance indicators into the overall iScore™ for suppliers and logistics partners.
                  </p>
                </div>
              </div>
            </div>

            {/* Promotion of Sustainable Logistics Partners */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Truck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Promotion of Sustainable Logistics Partners</h3>
                  <p className="text-gray-700 mb-4">
                    Highlighting freight carriers (StreamFreight, StreamGlobe) with demonstrated commitments to emissions reduction or use of alternative fuels.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>
      </section>
  );
}