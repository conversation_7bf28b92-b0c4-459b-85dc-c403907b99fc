import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ck, Shield<PERSON>heck, Truck, ArrowRight } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function SafetyComplianceSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Prioritizing Safety and Regulatory Adherence at Every Step
          </h2>
          <h3 className="text-xl font-semibold text-[#028475] mb-6">
            Ensuring Safety & Compliance in Chemical Trade
          </h3>
          <p className="text-lg text-gray-700 mb-8">
            StreamLnk integrates multiple layers to support safe and compliant chemical trade:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <FileCheck className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Mandatory MSDS/SDS Uploads</h4>
              </div>
              <p className="text-gray-700">For all listed chemical products.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <CheckCircle className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Verification of Supplier Certifications</h4>
              </div>
              <p className="text-gray-700">For handling and quality standards.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <Truck className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Partnerships with Hazmat-Certified Carriers</h4>
              </div>
              <p className="text-gray-700">Ensuring transport is managed by qualified providers.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <Beaker className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Traceability Features (Future)</h4>
              </div>
              <p className="text-gray-700">Exploring solutions for enhanced batch tracking and chain of custody.</p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="bg-[#004235] p-2 rounded-full mr-3">
                  <ShieldCheck className="h-5 w-5 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-[#004235]">Regulatory Information Hub</h4>
              </div>
              <p className="text-gray-700">Access to resources on key chemical regulations in different jurisdictions.</p>
            </div>
          </div>

          <Button
            variant="outline"
            className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white mt-4"
            asChild
          >
            <Link href="/solutions/risk-compliance">
              Learn More About Our Commitment to Chemical Safety & Compliance
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}