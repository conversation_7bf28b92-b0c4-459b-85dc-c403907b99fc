"use client";

import Link from "next/link";
import { ArrowR<PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function CTASection() {
  return (
    <section className="py-16 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Lay the Foundation for Success with StreamLnk's Digital Construction Solutions.
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            Build Your Next Project with the Power of StreamLnk
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-6">
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                Request A Demo
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
             <Button
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
              size="lg"
              asChild
            >
              <Link href="/solutions/e-stream"> {/* Assuming this link exists or will be created */}
                Suppliers: List Materials
              </Link>
            </Button>
            <Button
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
              size="lg"
              asChild
            >
              <Link href="/solutions/my-streamlnk"> {/* Assuming this link exists or will be created */}
                Buyers: Optimize Sourcing
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}