"use client";

import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, MessageSquarePlus } from "lucide-react";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Success in Action: How StreamLnk is Transforming Industrial Supply Chains
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Discover real-world examples and pilot program insights demonstrating the tangible benefits our integrated platform delivers to suppliers, buyers, and logistics partners across the globe.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
                size="lg"
                asChild
              >
                <Link href="/case-studies#share-story">
                  SHARE YOUR SUCCESS
                  <MessageSquarePlus className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button
                variant="outline"
                className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
                size="lg"
                asChild
              >
                <Link href="/request-demo?source=case-studies-hero">
                  REQUEST A DEMO
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/case-studies/case-study-hero.webp" // Placeholder - suggest user to replace
              alt="StreamLnk Case Studies Hero Image"
              fill
              className="object-cover"
              priority
            />
            {/* TODO: Consider adding a more relevant image or illustration for case studies */}
          </div>
        </div>
      </div>
    </section>
  );
}