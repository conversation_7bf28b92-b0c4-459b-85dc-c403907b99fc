import { Mail, Calendar, CheckCircle, ArrowRight } from "lucide-react"

export function RequestDemoWhatToExpect() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            What to Expect After You Request a Demo
          </h2>
          <h3 className="text-xl font-semibold text-[#028475] mb-6 text-center">Your Path to StreamLnk Clarity</h3>
            
          <div className="space-y-6">
            <div className="flex items-start">
              <div className="bg-[#004235] rounded-full p-2 mr-4 mt-1">
                <Mail className="h-5 w-5 text-white" />
              </div>
              <div>
                <h4 className="font-semibold text-[#004235] text-lg">Confirmation</h4>
                <p className="text-gray-600">You'll receive an email confirming we've received your request.</p>
              </div>
            </div>
              
            <div className="flex items-start">
              <div className="bg-[#004235] rounded-full p-2 mr-4 mt-1">
                <Calendar className="h-5 w-5 text-white" />
              </div>
              <div>
                <h4 className="font-semibold text-[#004235] text-lg">Scheduling</h4>
                <p className="text-gray-600">A StreamLnk specialist will contact you (usually via email) within one business day to understand your needs better and schedule a convenient time for your live demo (typically 30-60 minutes).</p>
              </div>
            </div>
              
            <div className="flex items-start">
              <div className="bg-[#004235] rounded-full p-2 mr-4 mt-1">
                <CheckCircle className="h-5 w-5 text-white" />
              </div>
              <div>
                <h4 className="font-semibold text-[#004235] text-lg">Personalized Demo</h4>
                <p className="text-gray-600">We'll tailor the demonstration to your specific interests and business role, focusing on the StreamLnk features most relevant to you.</p>
              </div>
            </div>
              
            <div className="flex items-start">
              <div className="bg-[#004235] rounded-full p-2 mr-4 mt-1">
                <ArrowRight className="h-5 w-5 text-white" />
              </div>
              <div>
                <h4 className="font-semibold text-[#004235] text-lg">Next Steps</h4>
                <p className="text-gray-600">After the demo, we'll discuss potential next steps, whether it's onboarding, a pilot program, or providing further information.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}