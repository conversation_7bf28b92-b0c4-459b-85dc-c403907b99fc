import { Building2, <PERSON><PERSON><PERSON><PERSON>2, <PERSON>Sign, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>3, Truck, Wallet } from "lucide-react";
import BenefitCard from "@/components/finance-payments/bnpl/benefit-card";

export default function BenefitsSection() {
  return (
    <section className="py-16 md:py-24 bg-[#f3f4f6]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Benefits of BNPL</h2>
          <p className="text-gray-600 max-w-3xl">
            Our BNPL solution offers significant advantages for both buyers and suppliers.
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2">
          <div>
            <div className="flex items-center gap-3 mb-6">
              <Building2 className="h-6 w-6 text-[#028475]" />
              <h3 className="text-xl font-semibold text-[#004235]">Benefits for Buyers</h3>
            </div>
            <div className="grid gap-4">
              <BenefitCard
                icon={<Wallet className="h-6 w-6 text-[#028475]" />}
                title="Improved Cash Flow"
                description="Secure materials without immediate cash outflow"
              />
              <BenefitCard
                icon={<FileCheck className="h-6 w-6 text-[#028475]" />}
                title="Consolidated Billing"
                description="Consolidate multiple shipments under one deferred billing cycle"
              />
              <BenefitCard
                icon={<BarChart3 className="h-6 w-6 text-[#028475]" />}
                title="Better Planning"
                description="Improve working capital and budget planning"
              />
            </div>
          </div>

          <div>
            <div className="flex items-center gap-3 mb-6">
              <Truck className="h-6 w-6 text-[#028475]" />
              <h3 className="text-xl font-semibold text-[#004235]">Benefits for Suppliers</h3>
            </div>
            <div className="grid gap-4">
              <BenefitCard
                icon={<DollarSign className="h-6 w-6 text-[#028475]" />}
                title="Immediate Payment"
                description="Get paid upfront with no delay or risk"
              />
              <BenefitCard
                icon={<Building2 className="h-6 w-6 text-[#028475]" />}
                title="Expanded Customer Base"
                description="Expand buyer base without onboarding credit risks"
              />
              <BenefitCard
                icon={<CheckCircle2 className="h-6 w-6 text-[#028475]" />}
                title="Focus on Core Business"
                description="Focus on fulfillment, not collections"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}