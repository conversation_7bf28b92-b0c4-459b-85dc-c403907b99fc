import { CheckCircle } from "lucide-react"

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Struggling to Gauge True Market Conditions?
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            In the complex world of industrial materials, reliable and timely market benchmarks are often elusive. Businesses face:
          </p>

          <div className="space-y-4 max-w-3xl mx-auto">
            {[
              "Difficulty accessing objective, real-time pricing data for specific product grades and regions.",
              "Lack of standardized metrics to assess logistics lane efficiency or carrier reliability.",
              "Inability to benchmark their own performance against true market averages.",
              "Challenges in identifying emerging market trends or potential supply/demand imbalances proactively.",
              "Over-reliance on anecdotal information or outdated reports for strategic decision-decision-making."
            ].map((challenge, index) => (
              <div key={index} className="flex items-start p-4 bg-gray-50 rounded-lg">
                <CheckCircle className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
                <p className="text-gray-800">{challenge}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}