import { Users, Briefcase, CheckCircle2 } from "lucide-react"

export default function WhyUseEscrowSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Why Use Escrow?</h2>
          <p className="text-gray-600 max-w-3xl">
            Escrow payments provide significant benefits for both buyers and suppliers in B2B transactions.
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2">
          <div className="space-y-6">
            <div className="flex items-center gap-3">
              <Users className="h-6 w-6 text-[#028475]" />
              <h3 className="text-xl font-semibold text-[#004235]">For Buyers</h3>
            </div>
            <ul className="space-y-3">
              <li className="flex items-start gap-2">
                <CheckCircle2 className="h-5 w-5 text-[#028475] mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">Reduce risk in first-time supplier relationships</span>
              </li>
              <li className="flex items-start gap-2">
                <CheckCircle2 className="h-5 w-5 text-[#028475] mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">
                  Ensure funds are only released when goods or documents meet expectations
                </span>
              </li>
            </ul>
          </div>

          <div className="space-y-6">
            <div className="flex items-center gap-3">
              <Briefcase className="h-6 w-6 text-[#028475]" />
              <h3 className="text-xl font-semibold text-[#004235]">For Suppliers</h3>
            </div>
            <ul className="space-y-3">
              <li className="flex items-start gap-2">
                <CheckCircle2 className="h-5 w-5 text-[#028475] mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">Confirm buyer payment commitment upfront</span>
              </li>
              <li className="flex items-start gap-2">
                <CheckCircle2 className="h-5 w-5 text-[#028475] mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">Get paid promptly as each phase is fulfilled</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  )
}