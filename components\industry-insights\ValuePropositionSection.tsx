"use client";

import { Lightbulb, Target, BarChart, Globe, TrendingUp } from "lucide-react";

export default function ValuePropositionSection() {
  const values = [
    {
      icon: <Lightbulb className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Sector-Specific Analysis",
      description: "Deep dives into trends, challenges, and opportunities within polymers, chemicals, energy, and other key industrial verticals."
    },
    {
      icon: <BarChart className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Data-Driven Perspectives",
      description: "Insights grounded in real-time transactional and operational data from the StreamLnk ecosystem."
    },
    {
      icon: <Target className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Actionable Intelligence",
      description: "Practical takeaways that you can apply to your sourcing, sales, logistics, and financial strategies."
    },
    {
      icon: <Globe className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Global Coverage, Regional Nuance",
      description: "Understanding of how global trends impact specific geographic markets."
    },
    {
      icon: <TrendingUp className="h-10 w-10 text-[#028475] mb-4" />,
      title: "Future-Focused Outlooks",
      description: "Predictive analysis and forecasting to help you anticipate market shifts."
    }
  ];

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            The Value of Specialized Industry Intelligence
          </h2>
          <p className="text-xl text-gray-700 mb-6">
            Why Deep Industry Knowledge Matters in Today's Market
          </p>
          <p className="text-lg text-gray-600">
            Understanding the nuances of your specific industrial sector is crucial for strategic decision-making, risk mitigation, and identifying growth opportunities. Generic market data often falls short. StreamLnk provides:
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {values.map((value, index) => (
            <div key={index} className="bg-[#F2F2F2] p-6 rounded-lg flex flex-col items-center text-center shadow-lg hover:shadow-xl transition-shadow duration-300">
              {value.icon}
              <h3 className="font-semibold text-[#004235] text-xl mb-2">{value.title}</h3>
              <p className="text-gray-600 text-sm">{value.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}