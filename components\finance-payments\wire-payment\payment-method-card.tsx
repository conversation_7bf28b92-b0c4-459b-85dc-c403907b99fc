import type { ReactNode } from "react"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { CheckCircle2 } from "lucide-react"

interface PaymentMethodCardProps {
  icon: ReactNode
  title: string
  features: string[]
}

export default function PaymentMethodCard({ icon, title, features }: PaymentMethodCardProps) {
  return (
    <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
      <CardHeader className="flex flex-row items-center gap-4">
        {icon}
        <h3 className="text-xl font-semibold text-[#004235]">{title}</h3>
      </CardHeader>
      <CardContent>
        <ul className="space-y-2">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start gap-2">
              <CheckCircle2 className="h-5 w-5 text-[#028475] mt-0.5 flex-shrink-0" />
              <span className="text-gray-600">{feature}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  )
}
