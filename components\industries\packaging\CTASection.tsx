import Link from "next/link";
import { <PERSON>R<PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export function PackagingCTASection() {
  return (
    <section className="py-16 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Ready to Innovate Your Packaging Supply Chain?
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            Connect with us to explore how StreamLnk can streamline your sourcing, logistics, and sustainability efforts in the packaging industry.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                Request a Demo
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>

            <Button
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
              size="lg"
              asChild
            >
              <Link href="/contact-us">
                Contact Us
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}