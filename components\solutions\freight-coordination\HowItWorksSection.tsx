"use client";

import { WorkflowTimeline, WorkflowStep } from "@/components/ui/WorkflowTimeline";

export default function HowItWorksSection() {
  const timelineSteps: WorkflowStep[] = [
    {
      number: 1,
      title: "Order Initiation",
      description: "An order is placed on StreamLnk requiring shipment."
    },
    {
      number: 2,
      title: "AI Logistics Planning",
      description: "The system determines the optimal transport modes and potential carriers."
    },
    {
      number: 3,
      title: "Carrier Engagement",
      description: "For land freight, RFQs are pushed to carriers on StreamFreight. For sea freight, booking requests are sent via API to ocean carriers through StreamGlobe."
    },
    {
      number: 4,
      title: "Coordination with Other Services",
      description: "Seamless handoffs are managed with StreamPak (for pre-shipment packaging/warehousing) and StreamGlobe+ (for customs clearance at ports)."
    },
    {
      number: 5,
      title: "Continuous Monitoring",
      description: "All legs of the journey are tracked, with updates fed back to the main platform."
    }
  ];

  return (
    <WorkflowTimeline
      title="From First Mile to Last Mile – Intelligently Connected"
      steps={timelineSteps}
    />
  );
}