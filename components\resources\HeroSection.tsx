"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4 text-center">
        <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
          Your Knowledge Center for Smarter Industrial Trade: Welcome to the StreamLnk Resource Hub
        </h1>
        <p className="text-xl text-gray-700 mb-8 max-w-3xl mx-auto">
          Stay informed, optimize your strategies, and master the complexities of global sourcing, logistics, and compliance. Access a wealth of expert insights, practical guides, market data, and platform tools curated by StreamLnk.
        </p>
        <div className="max-w-xl mx-auto">
          <form className="flex gap-2">
            <Input 
              type="search" 
              placeholder='Search Resources (e.g., "Polymer Pricing Trends," "Customs Automation Guide")...' 
              className="flex-grow focus-visible:ring-[#028475] border-gray-300"
            />
            <Button type="submit" className="bg-[#004235] hover:bg-[#028475] text-white">
              <Search className="h-5 w-5 mr-2 md:hidden" />
              <span className="hidden md:inline">Search</span>
            </Button>
          </form>
        </div>
      </div>
    </section>
  );
}