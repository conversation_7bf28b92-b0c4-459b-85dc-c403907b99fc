import { Recycle, DollarSign, LineChart, Workflow } from "lucide-react"

export default function WhyItMattersSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container px-4 md:px-6">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">Why StreamLnk Auctions Matter</h2>
          <p className="text-lg text-gray-700">
            This isn't just another online marketplace. StreamLnk Auctions represent a synchronized, intelligent system
            designed to transform industrial trade.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-5xl mx-auto">
          <div className="flex flex-col items-center text-center">
            <div className="rounded-full bg-[#004235]/10 w-16 h-16 flex items-center justify-center mb-4">
              <Recycle className="h-8 w-8 text-[#004235]" />
            </div>
            <h3 className="text-xl font-semibold text-[#004235] mb-2">Reduce Waste</h3>
            <p className="text-gray-700">Minimize unsold inventory and material obsolescence.</p>
          </div>

          <div className="flex flex-col items-center text-center">
            <div className="rounded-full bg-[#004235]/10 w-16 h-16 flex items-center justify-center mb-4">
              <DollarSign className="h-8 w-8 text-[#004235]" />
            </div>
            <h3 className="text-xl font-semibold text-[#004235] mb-2">Improve Liquidity</h3>
            <p className="text-gray-700">Enable faster conversion of inventory to cash for suppliers.</p>
          </div>

          <div className="flex flex-col items-center text-center">
            <div className="rounded-full bg-[#004235]/10 w-16 h-16 flex items-center justify-center mb-4">
              <LineChart className="h-8 w-8 text-[#004235]" />
            </div>
            <h3 className="text-xl font-semibold text-[#004235] mb-2">Enhance Price Discovery</h3>
            <p className="text-gray-700">Facilitate dynamic pricing based on real-time supply and demand.</p>
          </div>

          <div className="flex flex-col items-center text-center">
            <div className="rounded-full bg-[#004235]/10 w-16 h-16 flex items-center justify-center mb-4">
              <Workflow className="h-8 w-8 text-[#004235]" />
            </div>
            <h3 className="text-xl font-semibold text-[#004235] mb-2">Shape a More Agile Supply Chain</h3>
            <p className="text-gray-700">Contribute to a more responsive and efficient industrial ecosystem.</p>
          </div>
        </div>

        <div className="mt-12 max-w-3xl mx-auto text-center">
          <h3 className="text-2xl font-bold text-[#004235] mb-4">Join the Movement Towards Smarter Trade</h3>
          <p className="text-lg text-gray-700">
            Unlock new opportunities for buying and selling industrial products through a dynamic, transparent, and
            intelligent auction environment.
          </p>
        </div>
      </div>
    </section>
  )
}
