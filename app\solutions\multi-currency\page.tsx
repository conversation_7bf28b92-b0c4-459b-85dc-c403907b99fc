"use client"

import { MainNav } from "@/components/main-nav"
import { BottomFooter } from "@/components/bottom-footer"
import HeroSection from "@/components/solutions/multi-currency/HeroSection";
import ChallengesSection from "@/components/solutions/multi-currency/ChallengesSection";
import SolutionOverviewSection from "@/components/solutions/multi-currency/SolutionOverviewSection";
import WorkflowSection from "@/components/solutions/multi-currency/WorkflowSection";
import BenefitsSection from "@/components/solutions/multi-currency/BenefitsSection";
import CallToActionSection from "@/components/solutions/multi-currency/CallToActionSection";

export default function MultiCurrencyPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <ChallengesSection />

      <SolutionOverviewSection />

      <WorkflowSection />

      <BenefitsSection />

      <CallToActionSection />

      <BottomFooter />
    </div>
  )
}