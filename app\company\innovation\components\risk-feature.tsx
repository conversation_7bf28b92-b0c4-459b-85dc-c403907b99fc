import type { ReactNode } from "react"
import { Card, CardContent } from "@/components/ui/card"

interface RiskFeatureProps {
  icon: ReactNode
  title: string
  description: string
}

export default function RiskFeature({ icon, title, description }: RiskFeatureProps) {
  return (
    <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors h-full">
      <CardContent className="pt-6 flex flex-col h-full">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 rounded-full bg-[#004235]/10 flex-shrink-0">{icon}</div>
          <h3 className="font-semibold text-[#004235]">{title}</h3>
        </div>
        <p className="text-gray-600 text-sm">{description}</p>
      </CardContent>
    </Card>
  )
}
