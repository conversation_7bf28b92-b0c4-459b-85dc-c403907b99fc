import Link from "next/link"
import { BottomFooter } from "@/components/bottom-footer"

export function MainFooter() {
  const footerData = {
    "columns": [
      {
        "title": "Get to Know Us",
        "items": [
          {"label": "About StreamLnk", "targetDescription": "/about-us"},
          {"label": "Careers at StreamLnk", "targetDescription": "/careers"},
          {"label": "Press Center & Media", "targetDescription": "/press-releases"},
          {"label": "Investor Relations", "targetDescription": "#"},
          {"label": "StreamLnk Blog / Delivered Magazine", "targetDescription": "#"},
          {"label": "ESG & Sustainability", "targetDescription": "#"},
          {"label": "Supplier Diversity Commitment", "targetDescription": "#"},
          {"label": "Innovation at StreamLnk", "targetDescription": "#"}
        ]
      },
      {
        "title": "Partner With StreamLnk",
        "items": [
          {"label": "Sell Industrial Products on E-Stream", "targetDescription": "/partner/e-stream-supplier"},
          {"label": "Offer Freight Services", "targetDescription": "/partner/stream-freight"},
          {"label": "Provide Sea Freight Services", "targetDescription": "/partner/streamglobe-carrier"},
          {"label": "Become a Customs Clearance Agent", "targetDescription": "/partner/streamglobe-agent"},
          {"label": "List Packaging & Warehousing Services", "targetDescription": "/partner/streampak-partner"},
          {"label": "Join as an Agent/Distributor", "targetDescription": "/partner/partner-with-us"},
          {"label": "Become a Data or API Partner", "targetDescription": "/partner/api-integration"},
          {"label": "Participate in StreamLnk Auctions", "targetDescription": "/partner/auctions"},
          {"label": "View All Partnership Opportunities", "targetDescription": "/partner"}
        ]
      },
      {
        "title": "StreamLnk for Your Business",
        "items": [
          {"label": "Source Industrial Materials", "targetDescription": "/login"},
          {"label": "Request a Business Account", "targetDescription": "/business-account"},
          {"label": "Global Logistics Solutions", "targetDescription": "/solutions/global-logistics"},
          {"label": "Trade Finance & Payments", "targetDescription": "/solutions/trade-finance"},
          {"label": "Data Insights & Market Intelligence", "targetDescription": "/solutions/data-insights"},
          {"label": "Compliance & Risk Management Tools", "targetDescription": "/solutions/compliance-risk"},
          {"label": "Auction Marketplace for Buyers", "targetDescription": "/auctions"},
          {"label": "Solutions by Industry", "targetDescription": "/industry-sectors"}
        ]
      },
      {
        "title": "Customer & Partner Support",
        "items": [
          {"label": "Your StreamLnk Account & Portals", "targetDescription": "/login"},
          {"label": "Track Your Shipment", "targetDescription": "/track"},
          {"label": "Manage Your Orders & Quotes", "targetDescription": "/account/orders"},
          {"label": "Billing & Payment Support", "targetDescription": "/support/billing"},
          {"label": "Document Management Help", "targetDescription": "#"},
          {"label": "Compliance & Onboarding Assistance", "targetDescription": "#"},
          {"label": "API & Developer Support", "targetDescription": "/developer-portal"},
          {"label": "Report an Issue / Contact Support", "targetDescription": "/contact-us"},
          {"label": "Frequently Asked Questions", "targetDescription": "/faq"}
        ]
      },
      {
        "title": "StreamLnk Tools & Resources",
        "metadata": {
          "isOptional": true,
          "focus": "Resources/Tools"
        },
        "items": [
          {"label": "StreamIndex™ Market Intelligence", "targetDescription": "/tools/streamindex"},
          {"label": "iScore™ Partner Ratings", "targetDescription": "/tools/iscore"},
          {"label": "ESG Reporting Tools for Business", "targetDescription": "/tools/esg-reporting"},
          {"label": "Mobile App Access", "targetDescription": "#"},
          {"label": "StreamLnk Tier & Rewards Program", "targetDescription": "/rewards"},
          {"label": "Glossary of Trade Terms", "targetDescription": "#"},
          {"label": "Whitepapers & Case Studies", "targetDescription": "#"}
        ]
      }
    ]
  }

  return (
    <footer className="bg-[#3A3A3A] text-white">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8">
          {footerData.columns.map((column, colIndex) => (
            <div key={colIndex}>
              <h3 className="text-lg font-semibold mb-6">{column.title}</h3>
              <ul className="space-y-4">
                {column.items.map((item, itemIndex) => (
                  <li key={itemIndex}>
                    <Link href={item.targetDescription || "#"} className="text-gray-400 hover:text-white transition-colors">
                      {item.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-700">
        <BottomFooter />
      </div>
    </footer>
  )
}