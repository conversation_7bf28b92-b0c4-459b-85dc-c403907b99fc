import type React from "react"
import Image from "next/image"
import {
  ArrowRight,
  Leaf,
  Users,
  Shield,
  Truck,
  Recycle,
  FileText,
  Globe,
  Building2,
  Lock,
  BarChart3,
  CheckCircle2,
  Factory,
  Heart,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import EsgPillar from "./components/esg-pillar"
import EnvironmentalInitiative from "./components/environmental-initiative"
import SocialInitiative from "./components/social-initiative"
import GovernanceFeature from "./components/governance-feature"
import RoadmapItem from "./components/roadmap-item"
import InvolvementCard from "./components/involvement-card"

export default function EsgSustainabilityPage() {
  return (
    <main className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-[#F2F2F2] py-16 md:py-24 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="/placeholder.svg?height=800&width=1600"
            alt="Sustainable global trade"
            fill
            className="object-cover"
          />
        </div>
        <div className="container px-4 md:px-6 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <div className="inline-block rounded-lg bg-[#004235]/10 px-3 py-1 text-sm text-[#004235] mb-4">
              ESG & Sustainability
            </div>
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235] mb-4">
              Powering Sustainable Trade: Our Commitment to Environmental, Social, and Governance Excellence
            </h1>
            <p className="text-gray-600 md:text-xl mb-8">
              At StreamLnk, we believe that the future of industrial trade must be efficient, transparent, and
              sustainable. Discover how we are embedding ESG principles into our platform and operations.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button className="bg-[#004235] hover:bg-[#004235]/90">
                Read Our Latest Sustainability Report <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button variant="outline" className="border-[#028475] text-[#028475]">
                Learn About Our Initiatives
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Section 1: Our ESG Philosophy */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Integrating Sustainability into the Core of Industrial Commerce
            </h2>
            <p className="text-gray-600">
              StreamLnk is dedicated to transforming the global industrial supply chain not just through technological
              innovation, but also through a profound commitment to Environmental, Social, and Governance (ESG)
              principles. We recognize that our platform has the potential to influence how materials are sourced,
              transported, and managed worldwide, and we embrace the responsibility that comes with this.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            <EsgPillar
              icon={<Leaf className="h-10 w-10 text-[#028475]" />}
              title="Empower Sustainable Choices"
              description="Provide our users with the tools and information to make more environmentally and socially responsible decisions."
            />
            <EsgPillar
              icon={<FileText className="h-10 w-10 text-[#028475]" />}
              title="Enhance Transparency & Traceability"
              description="Leverage technology to bring clarity to complex supply chains, fostering accountability."
            />
            <EsgPillar
              icon={<Recycle className="h-10 w-10 text-[#028475]" />}
              title="Promote Efficient Resource Use"
              description="Reduce waste and inefficiencies inherent in traditional trade processes."
            />
            <EsgPillar
              icon={<Shield className="h-10 w-10 text-[#028475]" />}
              title="Uphold Ethical Governance"
              description="Operate with integrity, ensuring fair practices and robust oversight."
            />
          </div>

          <div className="mt-8 text-center">
            <p className="text-gray-600">
              We believe that sustainable business practices are not just a moral imperative but also a driver of
              long-term value, resilience, and innovation for StreamLnk and the entire industrial ecosystem we serve.
            </p>
          </div>
        </div>
      </section>

      {/* Section 2: Environmental Stewardship */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <div className="inline-block rounded-full bg-[#004235]/10 p-2 mb-4">
              <Leaf className="h-6 w-6 text-[#028475]" />
            </div>
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Minimizing Our Footprint, Maximizing Positive Impact
            </h2>
            <p className="text-gray-600">
              StreamLnk is actively working to reduce the environmental impact of industrial supply chains.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2">
            <EnvironmentalInitiative
              icon={<Truck className="h-8 w-8 text-[#028475]" />}
              title="Optimizing Logistics for Reduced Emissions"
              features={[
                "Our AI-powered routing and freight consolidation tools aim to minimize empty miles and optimize transportation routes, thereby reducing fuel consumption and carbon emissions.",
                "We partner with and promote carriers committed to sustainable freight practices (e.g., using alternative fuels, modern fleets).",
              ]}
            />

            <EnvironmentalInitiative
              icon={<Recycle className="h-8 w-8 text-[#028475]" />}
              title="Promoting Sustainable Materials & Circular Economy"
              features={[
                "Our platform features will include tagging and filtering for recycled materials, bio-based polymers, and low-carbon products, making it easier for buyers to source sustainable alternatives.",
                "We are exploring integrations to support the traceability of recycled content and foster a more circular economy for industrial materials. (e.g., Future: Blockchain for material provenance).",
              ]}
            />

            <EnvironmentalInitiative
              icon={<BarChart3 className="h-8 w-8 text-[#028475]" />}
              title="Reducing Waste through Efficiency"
              features={[
                "By digitizing documentation, streamlining communication, and improving demand forecasting (via our Auction Engine and StreamResources+), we help reduce overproduction, spoilage, and wasted resources.",
                "Our platform helps suppliers efficiently liquidate slow-moving or surplus inventory, preventing it from becoming waste.",
              ]}
            />

            <EnvironmentalInitiative
              icon={<Globe className="h-8 w-8 text-[#028475]" />}
              title="Platform Operations"
              features={[
                "Committing to energy-efficient cloud infrastructure (e.g., AWS/GCP commitments to renewable energy).",
                "Encouraging remote work and sustainable office practices for our own team.",
              ]}
            />
          </div>
        </div>
      </section>

      {/* Section 3: Social Responsibility */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <div className="inline-block rounded-full bg-[#004235]/10 p-2 mb-4">
              <Users className="h-6 w-6 text-[#028475]" />
            </div>
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Fostering Fair, Inclusive, and Ethical Trade
            </h2>
            <p className="text-gray-600">
              Our social commitment extends to our employees, our users, and the broader communities impacted by global
              trade.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <SocialInitiative
              icon={<HandshakeIcon className="h-8 w-8 text-[#028475]" />}
              title="Ethical Sourcing & Fair Labor Practices"
              description="While we don't directly control supplier operations, our compliance onboarding includes checks and encourages adherence to international labor standards. We aim to provide tools for greater supply chain visibility, which can help identify and mitigate risks related to unethical labor."
            />

            <SocialInitiative
              icon={<Users className="h-8 w-8 text-[#028475]" />}
              title="Supplier Diversity & Inclusion"
              description="Our Supplier Diversity Commitment aims to provide equal opportunities for businesses of all sizes and backgrounds to access global markets through StreamLnk. We are developing features to highlight and promote businesses owned by underrepresented groups."
            />

            <SocialInitiative
              icon={<Lock className="h-8 w-8 text-[#028475]" />}
              title="Data Privacy & Security"
              description="We are committed to protecting the data of all our users, adhering to stringent global data privacy regulations (GDPR, CCPA, etc.). Our robust security measures ensure that sensitive commercial information is safeguarded."
            />

            <SocialInitiative
              icon={<Heart className="h-8 w-8 text-[#028475]" />}
              title="Employee Well-being & Development"
              description="Fostering a diverse, inclusive, and supportive work environment for our global team. Investing in continuous learning and professional development for our employees."
            />

            <SocialInitiative
              icon={<Building2 className="h-8 w-8 text-[#028475]" />}
              title="Community Engagement"
              description="Exploring partnerships with organizations that support sustainable development and ethical practices in the industries we serve."
            />

            <div className="relative h-[250px] rounded-lg overflow-hidden shadow-lg">
              <Image
                src="/placeholder.svg?height=250&width=400"
                alt="Diverse team collaboration"
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end">
                <div className="p-4 text-white">
                  <p className="text-sm font-medium">
                    Our diverse team working together to build a more sustainable future
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 4: Governance & Ethics */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <div className="inline-block rounded-full bg-[#004235]/10 p-2 mb-4">
              <Shield className="h-6 w-6 text-[#028475]" />
            </div>
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Upholding the Highest Standards of Corporate Conduct
            </h2>
            <p className="text-gray-600">
              Strong governance is the bedrock of our commitment to sustainability and long-term value creation.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <GovernanceFeature
              icon={<FileText className="h-8 w-8 text-[#028475]" />}
              title="Ethical Business Practices"
              description="Our Code of Business Conduct & Ethics guides all our operations and interactions. Zero-tolerance policy for bribery, corruption, and unethical behavior."
            />

            <GovernanceFeature
              icon={<BarChart3 className="h-8 w-8 text-[#028475]" />}
              title="Transparent Reporting & Accountability"
              description="Commitment to transparent reporting on our ESG performance (e.g., future: annual sustainability report). Internal audit and risk management processes to ensure compliance and identify areas for improvement."
            />

            <GovernanceFeature
              icon={<Users className="h-8 w-8 text-[#028475]" />}
              title="Board Oversight"
              description="Our Board of Directors (or relevant committee) will oversee our ESG strategy and performance, ensuring alignment with our overall business objectives."
            />

            <GovernanceFeature
              icon={<HandshakeIcon className="h-8 w-8 text-[#028475]" />}
              title="Stakeholder Engagement"
              description="We actively engage with our investors, customers, suppliers, employees, and partners to understand their ESG expectations and integrate their feedback."
            />
          </div>
        </div>
      </section>

      {/* Section 5: Our ESG Roadmap & Future Commitments */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Journeying Towards a More Sustainable Future
            </h2>
            <p className="text-gray-600">
              Sustainability is an ongoing journey, not a destination. StreamLnk is committed to continuous improvement
              and innovation in our ESG practices.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <RoadmapItem
              icon={<FileText className="h-8 w-8 text-[#028475]" />}
              title="Enhanced ESG Product Tags"
              description="Developing enhanced ESG-specific product tags and search filters across all portals."
              timeframe="2023-2024"
            />
            <RoadmapItem
              icon={<Truck className="h-8 w-8 text-[#028475]" />}
              title="Carbon Footprint Estimation"
              description="Integrating shipment-level carbon footprint estimation tools."
              timeframe="2023-2024"
            />
            <RoadmapItem
              icon={<HandshakeIcon className="h-8 w-8 text-[#028475]" />}
              title="Certification Partnerships"
              description="Expanding partnerships with sustainability certification bodies."
              timeframe="2024"
            />
            <RoadmapItem
              icon={<BarChart3 className="h-8 w-8 text-[#028475]" />}
              title="First Sustainability Report"
              description="Publishing our first formal Sustainability Report."
              timeframe="2024"
            />
            <RoadmapItem
              icon={<CheckCircle2 className="h-8 w-8 text-[#028475]" />}
              title="Measurable Impact Targets"
              description="Setting measurable targets for reducing the environmental impact facilitated through our platform."
              timeframe="2024-2025"
            />
            <RoadmapItem
              icon={<Globe className="h-8 w-8 text-[#028475]" />}
              title="Global ESG Standards Alignment"
              description="Aligning our reporting with recognized ESG frameworks (GRI, SASB, TCFD)."
              timeframe="2025"
            />
          </div>

          <div className="mt-12 relative h-[200px] rounded-lg overflow-hidden shadow-lg">
            <Image
              src="/placeholder.svg?height=200&width=1200"
              alt="ESG roadmap timeline"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end">
              <div className="p-6 text-white">
                <h3 className="text-lg font-semibold mb-2">StreamLnk ESG Roadmap</h3>
                <p className="text-sm opacity-90">
                  Our journey towards building a more sustainable and responsible global trade ecosystem.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 6: Get Involved & Learn More */}
      <section className="py-16 md:py-24 bg-[#F2F2F2]">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Partner with Us in Building Responsible Supply Chains
            </h2>
            <p className="text-gray-600">
              We believe that collaboration is key to driving meaningful change. If you share our commitment to ESG and
              sustainability, we invite you to get involved.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <InvolvementCard
              icon={<Factory className="h-8 w-8 text-[#028475]" />}
              title="For Suppliers"
              description="Highlight your sustainable products and practices on E-Stream."
              buttonText="Join E-Stream"
              buttonLink="#"
            />
            <InvolvementCard
              icon={<Building2 className="h-8 w-8 text-[#028475]" />}
              title="For Buyers"
              description="Utilize our platform to source from verified, sustainable suppliers."
              buttonText="Start Sourcing"
              buttonLink="#"
            />
            <InvolvementCard
              icon={<HandshakeIcon className="h-8 w-8 text-[#028475]" />}
              title="For Partners"
              description="Collaborate with us on innovative solutions for greener logistics and more transparent supply chains."
              buttonText="Explore Partnerships"
              buttonLink="#"
            />
          </div>

          <div className="mt-12 flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="outline" className="border-[#028475] text-[#028475]">
              Read Our Supplier Diversity Statement
            </Button>
            <Button variant="outline" className="border-[#028475] text-[#028475]">
              Contact Us About ESG Partnerships
            </Button>
            <Button variant="outline" className="border-[#028475] text-[#028475]">
              Provide Feedback on Our ESG Initiatives
            </Button>
          </div>
        </div>
      </section>
    </main>
  )
}

function HandshakeIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M20.42 4.58a5.4 5.4 0 0 0-7.65 0l-.77.78-.77-.78a5.4 5.4 0 0 0-7.65 0C1.46 6.7 1.33 10.28 4 13l8 8 8-8c2.67-2.72 2.54-6.3.42-8.42z" />
    </svg>
  )
}
