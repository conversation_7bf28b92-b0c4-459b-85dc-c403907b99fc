// Placeholder for fetching challenge-specific content based on slug
// In a real app, this data would come from a CMS or database
const getChallengeContent = async (slug: string) => {
  // Simulate fetching data
  await new Promise(resolve => setTimeout(resolve, 100)); 
  // Example content structure - adapt as needed
  if (slug === "sample-case-study-1") {
    return {
      title: "The Mounting Operational Hurdles",
      paragraphs: [
        "Acme Corp, a leader in global manufacturing, found itself grappling with increasingly complex supply chain dynamics. Their legacy systems, a patchwork of disparate software and manual processes, were struggling to keep pace with their expanding operations. This resulted in significant data silos, making it nearly impossible to gain a holistic view of their inventory, shipments, and supplier performance.",
        "Key pain points included frequent delays in order fulfillment, rising transportation costs due to inefficient routing, and a lack of real-time visibility into their goods in transit. Furthermore, managing compliance documentation across various international regulations became a cumbersome and error-prone task, exposing Acme Corp to potential risks and penalties. The inability to proactively identify and mitigate disruptions was severely impacting their bottom line and customer satisfaction.",
        "The existing infrastructure also hindered collaboration between internal teams and external partners. Communication was fragmented, leading to misunderstandings and slow response times. Acme Corp recognized the urgent need for a unified, modern platform to streamline their logistics and regain control over their supply chain."
      ]
    };
  }
  // Fallback or default content
  return {
    title: "The Challenge Faced",
    paragraphs: [
      "Detailed description of the specific problems and pain points the client was facing before StreamLnk. This section typically involves 2-3 paragraphs outlining the operational inefficiencies, market pressures, or strategic obstacles that prompted the search for a new solution.",
      "Consider aspects like outdated systems, lack of visibility, communication breakdowns, rising costs, compliance difficulties, or inability to scale effectively. The more specific and relatable the challenges, the more impactful the case study will be."
    ]
  };
};

interface ChallengeSectionProps {
  caseStudySlug: string;
}

const ChallengeSection: React.FC<ChallengeSectionProps> = async ({ caseStudySlug }) => {
  const content = await getChallengeContent(caseStudySlug);

  return (
    <section className="py-12 md:py-16 lg:py-20 bg-white">
      <div className="container mx-auto px-4 md:px-6">
        <h2 className="text-3xl md:text-4xl font-semibold text-[#004235] mb-8 text-center">
          {content.title}
        </h2>
        <div className="max-w-3xl mx-auto space-y-6 text-gray-700 leading-relaxed">
          {content.paragraphs.map((paragraph, index) => (
            <p key={index} className="text-lg">
              {paragraph}
            </p>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ChallengeSection;