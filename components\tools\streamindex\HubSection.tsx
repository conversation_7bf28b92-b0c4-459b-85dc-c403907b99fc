"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>lter, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, TrendingUp } from "lucide-react";

export default function HubSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            You Have Arrived at the StreamIndex™ Hub
          </h2>
          <p className="text-xl text-gray-700 mb-10 text-center">
            The Power of Real-Time Benchmarking at Your Fingertips
          </p>
          <p className="text-lg text-gray-700 mb-12 text-center">
            The StreamIndex™ tool, a core feature of our StreamResources+ premium data service, provides unparalleled, interactive access to AI-driven benchmarks crucial for navigating the complexities of the global industrial materials market.
          </p>

          <h3 className="text-2xl font-semibold text-[#004235] mb-8 text-center">Key Capabilities You Can Access:</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <TrendingUp className="text-[#028475] h-8 w-8 mr-4 mt-1 flex-shrink-0" />
              <div>
                <h4 className="font-semibold text-[#004235] text-lg mb-1">Live Product Pricing Indices</h4>
                <p className="text-gray-600">Track and analyze real-time average market prices and volatility for specific polymers, chemicals, and other materials by grade, region, and Incoterm.</p>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <Ship className="text-[#028475] h-8 w-8 mr-4 mt-1 flex-shrink-0" />
              <div>
                <h4 className="font-semibold text-[#004235] text-lg mb-1">Dynamic Logistics Efficiency Indices</h4>
                <p className="text-gray-600">Monitor and compare performance across trade lanes, ports, and transport modes, including customs clearance speeds.</p>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <CheckSquare className="text-[#028475] h-8 w-8 mr-4 mt-1 flex-shrink-0" />
              <div>
                <h4 className="font-semibold text-[#004235] text-lg mb-1">Actionable Buyer Demand Indices</h4>
                <p className="text-gray-600">Gauge current market demand through aggregated RFQ volumes, auction activity, and search trends.</p>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start">
              <AlertTriangle className="text-[#028475] h-8 w-8 mr-4 mt-1 flex-shrink-0" />
              <div>
                <h4 className="font-semibold text-[#004235] text-lg mb-1">Insightful Compliance & Market Risk Indices</h4>
                <p className="text-gray-600">Identify regions or trade lanes with higher potential for disruptions or compliance issues.</p>
              </div>
            </div>
            <div className="bg-[#F2F2F2] p-6 rounded-lg flex items-start col-span-1 md:col-span-2">
              <BarChart3 className="text-[#028475] h-8 w-8 mr-4 mt-1 flex-shrink-0" />
              <div>
                <h4 className="font-semibold text-[#004235] text-lg mb-1">Interactive Charts & Granular Filtering</h4>
                <p className="text-gray-600">Drill down into the data, customize views, compare trends, and export information for your analysis.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}