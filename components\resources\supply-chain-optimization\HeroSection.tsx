"use client";

import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Tag } from "lucide-react";

export default function HeroSection() {
  // Get current date for publication, formatted as Month Day, Year
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  const categoryTags = ["Supply Chain Management", "Digitization", "Efficiency", "Logistics", "Procurement"];

  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <div className="mb-4 text-sm text-gray-600">
              <span>By The StreamLnk Insights Team | {currentDate}</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Unlocking Peak Performance: Optimizing Your Industrial Supply Chain in the Digital Age
            </h1>
            <div className="flex flex-wrap gap-2 mb-8">
              {categoryTags.map((tag) => (
                <span key={tag} className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-[#004235]">
                  <Tag className="mr-1.5 h-4 w-4" />
                  {tag}
                </span>
              ))}
            </div>
            <p className="text-xl text-gray-700 mb-8">
              In today's volatile global market, an optimized supply chain is essential for success. This guide explores how digital platforms like StreamLnk empower businesses to build resilient, agile, and data-driven supply chains.
            </p>
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white px-6"
              size="lg"
              asChild
            >
              <Link href="/request-demo?solution=supply-chain-optimization&source=resource-hero">
                OPTIMIZE NOW
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          <div className="relative w-full h-[300px] md:h-[450px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/resource-supply-chain-hero.webp" // Placeholder - suggest user to replace
              alt="Optimizing Industrial Supply Chain with Digital Solutions"
              fill
              className="object-cover"
              priority
            />
            {/* TODO: User to replace with a relevant image for supply chain optimization */}
          </div>
        </div>
      </div>
    </section>
  );
}