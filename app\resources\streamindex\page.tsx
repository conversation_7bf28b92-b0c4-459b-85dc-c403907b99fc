"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/resources/streamindex/HeroSection";
import ChallengeSection from "@/components/resources/streamindex/ChallengeSection";
import IntroStreamIndexSection from "@/components/resources/streamindex/IntroStreamIndexSection";
import HowItWorksSection from "@/components/resources/streamindex/HowItWorksSection";
import BenefitsSection from "@/components/resources/streamindex/BenefitsSection";
import AccessSection from "@/components/resources/streamindex/AccessSection";

export default function StreamIndexPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <ChallengeSection />
      <IntroStreamIndexSection />
      <HowItWorksSection />
      <BenefitsSection />
      <AccessSection />

      <BottomFooter />
    </div>
  );
}