import { But<PERSON> } from "@/components/ui/button"
import { ArrowR<PERSON> } from "lucide-react"
import Link from "next/link"

export function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl">
          <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
            Represent the Future of Industrial Trade: Partner with MyStreamLnk+
          </h1>
          <p className="text-xl md:text-2xl text-[#028475] mb-8">
            Join StreamLnk as an independent sales agent or regional distributor. Leverage our powerful digital platform
            to onboard customers, manage their accounts, track transactions, and earn commissions, revolutionizing your
            B2B sales career.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button size="lg" className="bg-[#004235] hover:bg-[#028475] text-white transition-colors" asChild>
              <Link href="#apply">
                Apply to Become an Agent <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white transition-colors"
              asChild
            >
              <Link href="#learn-more">Learn More</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
