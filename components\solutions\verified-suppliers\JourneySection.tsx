"use client"

import { WorkflowTimeline, WorkflowStep } from "@/components/ui/WorkflowTimeline";

export function JourneySection() {
  const timelineSteps: WorkflowStep[] = [
    {
      number: 1,
      title: "Register/Login to MyStreamLnk",
      description: "Access your personalized customer portal."
    },
    {
      number: 2,
      title: "Utilize Advanced Search",
      description: "Use filters for product type, grade, region, certifications, and iScore™ range."
    },
    {
      number: 3,
      title: "Review Supplier Profiles",
      description: "Examine detailed profiles, product catalogs, available compliance documents, and iScore™ ratings to assess suitability."
    },
    {
      number: 4,
      title: "Engage & Transact Securely",
      description: "Connect directly with suppliers, request quotes, negotiate terms, and manage transactions within the secure StreamLnk platform."
    }
  ];

  return (
    <WorkflowTimeline
      title="Your Journey to Confident Sourcing Starts Here"
      subtitle="Finding and engaging verified suppliers on StreamLnk is straightforward:"
      steps={timelineSteps}
    />
  )
}