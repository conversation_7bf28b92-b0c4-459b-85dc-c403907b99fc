import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl">
          <div className="flex items-center mb-6">
            <div className="w-12 h-1 bg-[#028475] mr-3"></div>
            <span className="text-[#028475] font-medium">CUSTOMS AUTOMATION & COMPLIANCE</span>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
            Navigate Global Customs with Confidence: Automated, Compliant, Connected
          </h1>
          <p className="text-lg text-gray-700 mb-8">
            Leave customs complexities behind. StreamLnk's integrated platform, featuring StreamGlobe+, automates documentation, connects you with expert customs agents, and ensures your industrial material shipments clear borders smoothly and efficiently.
          </p>
          <Button 
            className="bg-[#004235] hover:bg-[#028475] text-white px-6"
            size="lg"
            asChild
          >
            <Link href="/request-demo">
              SIMPLIFY YOUR CUSTOMS – REQUEST A DEMO
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}