import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowR<PERSON> } from "lucide-react"
import Link from "next/link"

export function CtaSection() {
  return (
    <section id="apply" className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235]">
              Ready to Modernize Your Customs Clearance Operations?
            </h2>
            <p className="mx-auto max-w-[700px] text-gray-600 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              Join StreamGlobe and become a pivotal part of a digitally streamlined global trade network. Reduce
              administrative burdens, increase your visibility, and focus on what you do best.
            </p>
          </div>
          <div className="w-full max-w-sm space-y-2">
            <Link href="/apply-streamglobe">
              <Button className="w-full bg-[#004235] hover:bg-[#003228] text-white">
                Apply to Join StreamGlobe Today <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
            <p className="text-xs text-gray-500">Portal Access: StreamGlobe</p>
          </div>
        </div>
      </div>
    </section>
  )
}
