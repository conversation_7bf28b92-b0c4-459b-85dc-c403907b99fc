import Link from "next/link"
import Image from "next/image"
import { ChevronRight, Globe, BarChart3, Brain, Users, MicroscopeIcon as MagnifyingGlass, Shield } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { CountrySelector } from "@/components/country-selector"
import { MainNav } from "@/components/main-nav"
import { WhoWeAre } from "@/components/who-we-are"
import { WhatWeDo } from "@/components/what-we-do"
import { HeroSlider } from "@/components/hero-slider"
import { MainFooter } from "@/components/main-footer"

export default function HomePage() {
  return (
    <div className="flex min-h-screen flex-col">
      <CountrySelector />
      <MainNav />

      <main>
        {/* Hero Section with Video Slider */}
        <HeroSlider />

        {/* News/Blog Section */}
        <section className="bg-white py-8 border-b">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="p-6">
                <div className="text-sm text-[#18b793] font-medium mb-2">Energy Forward</div>
                <div className="text-sm text-gray-500 mb-2">April 5, 2025</div>
                <h3 className="font-semibold mb-2">
                  Four things you should know about emissions abatement
                </h3>
                <Link href="#" className="text-[#18b793] text-sm font-medium flex items-center mt-4">
                  Read More <ChevronRight className="h-4 w-4 ml-1" />
                </Link>
              </div>

              <div className="p-6 border-l border-r">
                <div className="text-sm text-[#18b793] font-medium mb-2">New</div>
                <div className="text-sm text-gray-500 mb-2">April 5, 2025</div>
                <h3 className="font-semibold mb-2">
                  Sinochem Selects StreamLnk' Cordant™ for Enterprise-Wide Asset Management
                </h3>
                <Link href="#" className="text-[#18b793] text-sm font-medium flex items-center mt-4">
                  Read More <ChevronRight className="h-4 w-4 ml-1" />
                </Link>
              </div>

              <div className="p-6">
                <div className="text-sm text-[#18b793] font-medium mb-2">Energy Forward</div>
                <div className="text-sm text-gray-500 mb-2">April 5, 2025</div>
                <h3 className="font-semibold mb-2">
                  How GenAI is driving the art of the possible
                </h3>
                <Link href="#" className="text-[#18b793] text-sm font-medium flex items-center mt-4">
                  Read More <ChevronRight className="h-4 w-4 ml-1" />
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Who We Are Section */}
        <WhoWeAre />

        {/* What We Do Section */}
        <WhatWeDo />



        {/* Industry Solutions Section */}
        <section className="py-16 md:py-24 bg-[#3A3A3A] text-white">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-16">
              <div className="bg-[#111111] rounded-lg overflow-hidden">
                <div className="relative">
                  <Image
                    src="/images/What we do/Join our suppliers.webp"
                    alt="Join our suppliers - Digital supplier network"
                    width={600}
                    height={400}
                    className="w-full"
                  />
                </div>
                <div className="p-8">
                  <div className="w-12 h-1 bg-[#18b793] mb-6"></div>
                  <h3 className="text-3xl font-bold mb-4">Join our suppliers</h3>
                  <p className="text-gray-300 mb-6">
                    Join StreamLnk's trusted digital supplier network and unlock new, high-value trading opportunities. Our state-of-the-art supplier portal simplifies the entire procurement process—from contract management to seamless order fulfillment.
                  </p>
                  <Link href="#" className="inline-flex items-center text-[#18b793] font-medium group">
                    <span className="mr-2">REGISTER AS A SUPPLIER</span>
                    <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </div>

              <div className="bg-[#111111] rounded-lg overflow-hidden">
                <div className="relative">
                  <Image
                    src="/images/What we do/Shipment Tracking.webp"
                    alt="Shipment tracking - Global logistics dashboard"
                    width={600}
                    height={400}
                    className="w-full"
                  />
                </div>
                <div className="p-8">
                  <div className="w-12 h-1 bg-[#18b793] mb-6"></div>
                  <h3 className="text-3xl font-bold mb-4">Shipment Tracking</h3>
                  <p className="text-gray-300 mb-6">
                    Experience the future of energy logistics with StreamLnk's smart customer dashboard. Gain instant, real-time visibility into every aspect of your orders—track shipments, access key documents, manage orders effortlessly all in one place, and make smarter decisions in real-time.
                  </p>
                  <Link href="#" className="inline-flex items-center text-[#18b793] font-medium group">
                    <span className="mr-2">TRACK YOUR SHIPMENT</span>
                    <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Reports and Policies Section */}
        <section className="py-16 md:py-24">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-[#004235] mb-12 flex items-center">
              <span className="w-10 h-1 bg-[#18b793] mr-3"></span>
              Reports and policies
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Card 1: Corporate Sustainability Report */}
              <div className="relative group overflow-hidden rounded-lg cursor-pointer">
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-black/20 z-10"></div>
                <Image
                  src="/images/homepage/Read our 2023 Corporate Sustainability Report.webp"
                  alt="Read our 2023 Corporate Sustainability Report"
                  width={400}
                  height={550}
                  className="w-full h-[34.375rem] object-cover transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-[#126053]/0 z-15 transition-all duration-400 ease-out transform translate-y-full group-hover:translate-y-0 group-hover:bg-[#126053]/95"></div>
                <div className="absolute bottom-0 left-0 p-6 z-20 transition-transform duration-400 ease-out group-hover:translate-y-[-60px]">
                  <div className="w-10 h-1 bg-[#18b793] mb-3"></div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    Read our 2023 <br /> Corporate Sustainability <br /> Report
                  </h3>
                  <div className="max-h-0 opacity-0 overflow-hidden transition-all duration-400 ease-out group-hover:max-h-[100px] group-hover:opacity-100 mt-4">
                    <p className="text-white text-sm mb-4">Find out more about our commitment to sustainability in our 2023 report.</p>
                    <Link href="#" className="inline-block border border-white rounded px-4 py-2 text-xs font-medium text-white transition-colors hover:bg-white hover:text-[#126053]">
                      READ MORE
                    </Link>
                  </div>
                </div>
              </div>

              {/* Card 2: Diversity Equity & Inclusion Report */}
              <div className="relative group overflow-hidden rounded-lg cursor-pointer">
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-black/20 z-10"></div>
                <Image
                  src="/images/homepage/Explore our Diversity Equity & Inclusion annual report.webp"
                  alt="Explore our Diversity Equity & Inclusion annual report"
                  width={400}
                  height={550}
                  className="w-full h-[34.375rem] object-cover transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-[#126053]/0 z-15 transition-all duration-400 ease-out transform translate-y-full group-hover:translate-y-0 group-hover:bg-[#126053]/95"></div>
                <div className="absolute bottom-0 left-0 p-6 z-20 transition-transform duration-400 ease-out group-hover:translate-y-[-60px]">
                  <div className="w-10 h-1 bg-[#18b793] mb-3"></div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    Explore our Diversity <br /> Equity & Inclusion <br /> annual report
                  </h3>
                  <div className="max-h-0 opacity-0 overflow-hidden transition-all duration-400 ease-out group-hover:max-h-[100px] group-hover:opacity-100 mt-4">
                    <p className="text-white text-sm mb-4">Learn more about our commitment to DEI in our annual report.</p>
                    <Link href="#" className="inline-block border border-white rounded px-4 py-2 text-xs font-medium text-white transition-colors hover:bg-white hover:text-[#126053]">
                      READ MORE
                    </Link>
                  </div>
                </div>
              </div>

              {/* Card 3: Annual Report */}
              <div className="relative group overflow-hidden rounded-lg cursor-pointer">
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-black/20 z-10"></div>
                <Image
                  src="/images/homepage/Our 2024 annual report.webp"
                  alt="Our 2024 annual report"
                  width={400}
                  height={550}
                  className="w-full h-[34.375rem] object-cover transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-[#126053]/0 z-15 transition-all duration-400 ease-out transform translate-y-full group-hover:translate-y-0 group-hover:bg-[#126053]/95"></div>
                <div className="absolute bottom-0 left-0 p-6 z-20 transition-transform duration-400 ease-out group-hover:translate-y-[-60px]">
                  <div className="w-10 h-1 bg-[#18b793] mb-3"></div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    Our 2024 annual report
                  </h3>
                  <div className="max-h-0 opacity-0 overflow-hidden transition-all duration-400 ease-out group-hover:max-h-[100px] group-hover:opacity-100 mt-4">
                    <p className="text-white text-sm mb-4">Read our Annual Report to learn more about our company and commitments.</p>
                    <Link href="#" className="inline-block border border-white rounded px-4 py-2 text-xs font-medium text-white transition-colors hover:bg-white hover:text-[#126053]">
                      READ MORE
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Industry insights and StreamLnk news Section */}
        <section className="py-16 md:py-24 bg-white">
          <div className="container mx-auto px-4 text-center mb-12">
            <div className="flex flex-col items-center justify-center">
              <div className="w-10 h-1 bg-[#18b793] mb-4"></div>
              <h2 className="text-3xl md:text-4xl font-bold text-[#004235]">
                Industry insights and StreamLnk news
              </h2>
            </div>
          </div>

          <div className="container mx-auto px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Left column - Large featured article */}
              <div className="relative rounded-lg overflow-hidden shadow-sm h-[450px]">
                <Image
                  src="/images/homepage/blog 1.webp"
                  alt="Supply Chain Innovation"
                  width={800}
                  height={600}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black/60 z-10"></div>
                <div className="absolute bottom-0 left-0 p-5 z-20">
                  <div className="flex items-center mb-2">
                    <div className="w-5 h-0.5 bg-[#18b793] mr-2"></div>
                    <div className="text-[#18b793] text-xs font-medium">CATEGORY 1</div>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    Blog title Blog title
                  </h3>
                  <p className="text-gray-200 mb-3 text-xs leading-relaxed">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore do eiusmod consec Lorem ipsum elit...
                  </p>
                  <div className="inline-block border border-white rounded">
                    <Link
                      href="#"
                      className="inline-flex items-center justify-center h-7 px-4 py-0 text-xs font-medium text-white transition-colors hover:bg-white hover:text-[#004235]"
                    >
                      Read More
                    </Link>
                  </div>
                </div>
              </div>

              {/* Right column - Two smaller articles */}
              <div className="space-y-8">
                {/* First small article */}
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Image
                      src="/images/homepage/blog 2.webp"
                      alt="Category 2"
                      width={300}
                      height={300}
                      className="w-full aspect-square object-cover rounded-sm shadow-sm"
                    />
                  </div>
                  <div className="col-span-2 flex flex-col justify-evenly h-full">
                    <div className="flex items-center">
                      <div className="w-5 h-0.5 bg-[#18b793] mr-2"></div>
                      <div className="text-[#18b793] text-xs font-medium">CATEGORY 2</div>
                    </div>
                    <h3 className="text-base font-bold">
                      Blog title here
                    </h3>
                    <p className="text-gray-600 text-xs leading-relaxed">
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor elit...
                    </p>
                    <Link href="#" className="text-[#18b793] text-xs font-medium flex items-center">
                      Read More <ChevronRight className="h-3 w-3 ml-0.5" />
                    </Link>
                  </div>
                </div>

                {/* Second small article */}
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Image
                      src="/images/homepage/blog 3.webp"
                      alt="Category 3"
                      width={300}
                      height={300}
                      className="w-full aspect-square object-cover rounded-sm shadow-sm"
                    />
                  </div>
                  <div className="col-span-2 flex flex-col justify-evenly h-full">
                    <div className="flex items-center">
                      <div className="w-5 h-0.5 bg-[#18b793] mr-2"></div>
                      <div className="text-[#18b793] text-xs font-medium">CATEGORY 3</div>
                    </div>
                    <h3 className="text-base font-bold">
                      Blog title here
                    </h3>
                    <p className="text-gray-600 text-xs leading-relaxed">
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor elit...
                    </p>
                    <Link href="#" className="text-[#18b793] text-xs font-medium flex items-center">
                      Read More <ChevronRight className="h-3 w-3 ml-0.5" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Be a Part of Our Story Section */}
        <section className="py-16 md:py-24 bg-gray-50">
          <div className="container mx-auto px-8 max-w-screen-xl">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
              {/* Left column - Text content */}
              <div className="flex flex-col justify-center">
                <div className="text-[#18b793] text-sm font-medium mb-3 flex items-center">
                  <span className="w-8 h-1 bg-[#18b793] mr-2"></span>
                  CAREERS
                </div>
                <h2 className="text-3xl font-bold text-[#004235] mb-4">Be a part of our story</h2>
                <p className="text-gray-600 mb-6">
                  Check out our Careers site to learn more about how you can be a part of the team.
                </p>
                <Link href="#" className="text-[#18b793] text-sm font-medium flex items-center">
                  EXPLORE <ChevronRight className="h-4 w-4 ml-1" />
                </Link>
              </div>

              {/* Middle column - Career opportunities */}
              <div className="relative group overflow-hidden rounded-lg cursor-pointer h-[450px]">
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-black/20 z-10"></div>
                <Image
                  src="/images/homepage/Career opportunities.webp"
                  alt="Career opportunities"
                  width={500}
                  height={450}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-[#126053]/0 z-15 transition-all duration-400 ease-out transform translate-y-full group-hover:translate-y-0 group-hover:bg-[#126053]/95"></div>
                <div className="absolute bottom-0 left-0 p-6 z-20 transition-transform duration-400 ease-out group-hover:translate-y-[-60px]">
                  <div className="w-10 h-1 bg-[#18b793] mb-3"></div>
                  <h3 className="text-xl font-bold text-white mb-2">Career opportunities</h3>
                  <div className="max-h-0 opacity-0 overflow-hidden transition-all duration-400 ease-out group-hover:max-h-[100px] group-hover:opacity-100 mt-4">
                    <p className="text-white text-sm mb-4">Explore exciting career paths and opportunities at StreamLnk.</p>
                    <Link href="#" className="inline-block border border-white rounded px-4 py-2 text-xs font-medium text-white transition-colors hover:bg-white hover:text-[#126053]">
                      LEARN MORE
                    </Link>
                  </div>
                </div>
              </div>

              {/* Right column - Application status */}
              <div className="relative group overflow-hidden rounded-lg cursor-pointer h-[450px]">
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-black/20 z-10"></div>
                <Image
                  src="/images/homepage/Application status.webp"
                  alt="Application status"
                  width={500}
                  height={450}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-[#126053]/0 z-15 transition-all duration-400 ease-out transform translate-y-full group-hover:translate-y-0 group-hover:bg-[#126053]/95"></div>
                <div className="absolute bottom-0 left-0 p-6 z-20 transition-transform duration-400 ease-out group-hover:translate-y-[-60px]">
                  <div className="w-10 h-1 bg-[#18b793] mb-3"></div>
                  <h3 className="text-xl font-bold text-white mb-2">Application status</h3>
                  <div className="max-h-0 opacity-0 overflow-hidden transition-all duration-400 ease-out group-hover:max-h-[100px] group-hover:opacity-100 mt-4">
                    <p className="text-white text-sm mb-4">Check the status of your application and next steps in the process.</p>
                    <Link href="#" className="inline-block border border-white rounded px-4 py-2 text-xs font-medium text-white transition-colors hover:bg-white hover:text-[#126053]">
                      CHECK STATUS
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section removed as requested */}
      </main>

      {/* Footer */}
      <MainFooter />
    </div>
  )
}
