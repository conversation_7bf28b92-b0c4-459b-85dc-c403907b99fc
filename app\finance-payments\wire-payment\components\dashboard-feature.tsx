import type { ReactNode } from "react"

interface DashboardFeatureProps {
  icon: ReactNode
  title: string
}

export default function DashboardFeature({ icon, title }: DashboardFeatureProps) {
  return (
    <div className="flex items-center gap-3 p-3 rounded-md bg-white">
      <div className="p-2 rounded-full bg-[#004235]/10">{icon}</div>
      <span className="font-medium text-gray-700">{title}</span>
    </div>
  )
}
