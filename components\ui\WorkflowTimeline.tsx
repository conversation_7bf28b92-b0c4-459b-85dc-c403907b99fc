import React from 'react';
import { TimelineItem } from './TimelineItem';

export interface WorkflowStep {
  number: number;
  title: string;
  description: string;
}

interface WorkflowTimelineProps {
  title: string;
  subtitle?: string;
  steps: WorkflowStep[];
  footer?: string;
}

export function WorkflowTimeline({ title, subtitle, steps, footer }: WorkflowTimelineProps) {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-10 text-center">
            {title}
          </h2>
          
          {subtitle && (
            <p className="text-lg text-gray-700 mb-10 text-center">
              {subtitle}
            </p>
          )}

          <div className="space-y-6">
            {steps.map((step) => (
              <TimelineItem
                key={step.number}
                number={step.number}
                title={step.title}
                description={step.description}
              />
            ))}
          </div>

          {footer && (
            <div className="mt-8 text-center">
              <p className="text-[#004235] font-medium">{footer}</p>
            </div>
          )}
        </div>
      </div>
    </section>
  );
}