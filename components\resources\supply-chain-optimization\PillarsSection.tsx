"use client";

import { CheckCircle } from "lucide-react";

const optimizationPillars = [
  { name: "Cost Reduction", description: "Minimize operational and procurement expenses." },
  { name: "Improved Speed & Reliability", description: "Enhance delivery times and service consistency." },
  { name: "End-to-End Visibility", description: "Gain real-time insights across the entire supply chain." },
  { name: "Proactive Risk Mitigation", description: "Identify and address potential disruptions early." },
  { name: "Increased Agility", description: "Respond quickly to market changes and demands." },
  { name: "Better Sustainability", description: "Implement eco-friendly practices and reduce waste." },
];

export default function PillarsSection() {
  return (
    <section className="py-12 md:py-20 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Understanding the Pillars of Supply Chain Optimization
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center leading-relaxed">
            An optimized supply chain simultaneously achieves multiple critical objectives. True optimization means fostering seamless workflows and data flow across the entire value chain, not just improving individual silos. It's about creating a holistic, interconnected ecosystem that drives peak performance.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {optimizationPillars.map((pillar) => (
              <div key={pillar.name} className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                <div className="flex items-center mb-3">
                  <CheckCircle className="h-7 w-7 text-[#028475] mr-3" />
                  <h3 className="text-xl font-semibold text-[#004235]">{pillar.name}</h3>
                </div>
                <p className="text-gray-600 text-sm leading-relaxed">{pillar.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}