import { Users, Building, Truck, ShieldCheck } from "lucide-react";

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Make Smarter Partnership Decisions, Reduce Risk, Foster Quality
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            Benefits for Different Users:
          </p>

          <div className="grid md:grid-cols-2 gap-8">
            {/* For Buyers */}
            <div className="bg-[#F2F2F2] p-8 rounded-lg">
              <div className="flex items-center mb-4">
                <Users className="h-8 w-8 text-[#004235] mr-3" />
                <h3 className="text-xl font-semibold text-[#004235]">For Buyers (MyStreamLnk Users)</h3>
              </div>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  <span>Select Suppliers with Confidence: View iScore™ badges and summary ratings during product discovery to choose reliable, high-performing suppliers.</span>
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  <span>Mitigate Supply Risk: Identify potentially risky suppliers based on low operational or compliance scores.</span>
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  <span>Access Detailed Reports (Premium): Get in-depth iScore™ reports on potential suppliers before committing to large orders.</span>
                </li>
              </ul>
            </div>

            {/* For Suppliers */}
            <div className="bg-[#F2F2F2] p-8 rounded-lg">
              <div className="flex items-center mb-4">
                <Building className="h-8 w-8 text-[#004235] mr-3" />
                <h3 className="text-xl font-semibold text-[#004235]">For Suppliers (E-Stream Users)</h3>
              </div>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  <span>Showcase Your Reliability: A high iScore™ acts as a badge of honor, attracting more buyers and potentially better terms.</span>
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  <span>Benchmark Your Performance: Understand how your operational and compliance metrics compare to anonymized averages.</span>
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  <span>Identify Areas for Improvement: Use detailed feedback within your iScore™ to enhance your services.</span>
                </li>
              </ul>
            </div>

            {/* For Logistics & Service Providers */}
            <div className="bg-[#F2F2F2] p-8 rounded-lg">
              <div className="flex items-center mb-4">
                <Truck className="h-8 w-8 text-[#004235] mr-3" />
                <h3 className="text-xl font-semibold text-[#004235]">For Logistics & Service Providers</h3>
              </div>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  <span>Gain Preferred Partner Status: High iScores can lead to more job assignments and better tier benefits within StreamLnk.</span>
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  <span>Demonstrate Service Excellence: Use your iScore™ to build trust with shippers and StreamLnk Operations.</span>
                </li>
              </ul>
            </div>

            {/* For StreamLnk */}
            <div className="bg-[#F2F2F2] p-8 rounded-lg">
              <div className="flex items-center mb-4">
                <ShieldCheck className="h-8 w-8 text-[#004235] mr-3" />
                <h3 className="text-xl font-semibold text-[#004235]">For StreamLnk (Platform Integrity)</h3>
              </div>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  <span>Maintains Ecosystem Quality: Encourages all participants to uphold high standards.</span>
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  <span>Powers AI Matching: iScore™ is a key input for our AI in recommending partners.</span>
                </li>
                <li className="flex items-start">
                  <span className="text-[#028475] mr-2">•</span>
                  <span>Informs Risk Management: Helps our internal teams identify and manage platform-wide risk.</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}