"use client";

import { Globe, Truck, Package, FileText, BarChart3, ShieldCheck } from "lucide-react";

export default function PlatformOverviewSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            One Platform, Every Mode, Every Border: Logistics Orchestrated by StreamLnk
          </h2>
          <p className="text-lg text-gray-700 mb-10">
            StreamLnk simplifies global logistics by integrating specialized portals and AI-driven intelligence into a single, cohesive ecosystem. We provide tools and networks to manage:
          </p>

          <div className="space-y-8">
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Globe className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Ocean Freight (StreamGlobe)</h3>
                  <p className="text-gray-700 mb-4">
                    Seamless integration with major global sea carriers for real-time schedules, bookings, and container tracking. Facilitates efficient port-to-port movements.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Truck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Land Freight (StreamFreight)</h3>
                  <p className="text-gray-700 mb-4">
                    Access to a vetted network of trucking companies and rail operators for domestic and cross-border first-mile and last-mile delivery, as well as intermodal connections. Live bidding and real-time tracking.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <FileText className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Customs Clearance (StreamGlobe+)</h3>
                  <p className="text-gray-700 mb-4">
                    Connect with licensed customs agents worldwide for automated document flow, efficient declarations, and real-time status updates, including automated POA management.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Package className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Warehousing & Packaging (StreamPak)</h3>
                  <p className="text-gray-700 mb-4">
                    Coordinate with certified partners for product storage, repackaging, labeling, and other value-added services necessary for specific markets or customer requirements. Real-time inventory visibility.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <BarChart3 className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">AI-Powered Orchestration & Optimization</h3>
                  <p className="text-gray-700 mb-4">
                    Our intelligent backend optimizes routes, suggests carriers, automates document handoffs, and provides predictive ETAs.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <ShieldCheck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">End-to-End Shipment Management & Visibility</h3>
                  <p className="text-gray-700 mb-4">
                    Track all your global shipments from origin to destination within your MyStreamLnk or E-Stream portal, regardless of the complexity of the journey.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}