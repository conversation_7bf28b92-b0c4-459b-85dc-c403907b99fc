import { ArrowR<PERSON> } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export default function CtaSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
            Simplify Financial Operations Across Your Supply Chain
          </h2>
          <p className="text-gray-600 max-w-3xl mb-8">
            With the StreamLnk Invoicing Center, you'll spend less time chasing paperwork and more time optimizing
            your cash flow.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button className="bg-[#004235] hover:bg-[#004235]/90 px-8 py-6 text-lg">
              Get Started <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button variant="outline" className="border-[#028475] text-[#028475] px-8 py-6 text-lg">
              Request Demo
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}