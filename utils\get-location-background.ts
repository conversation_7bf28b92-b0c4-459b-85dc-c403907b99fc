interface Landmark {
  city: string
  image: string
  name: string
}

// Cache for landmark data to reduce API calls
let landmarkCache: {
  landmark: Landmark
  timestamp: number
} | null = null

// Cache duration in milliseconds (1 hour)
const CACHE_DURATION = 60 * 60 * 1000

export async function getLocationBackground(): Promise<Landmark> {
  try {
    console.log("Getting location background...")

    // Check if we have cached data that's still valid
    const now = Date.now()
    if (landmarkCache && now - landmarkCache.timestamp < CACHE_DURATION) {
      console.log("Using cached landmark data")
      return landmarkCache.landmark
    }

    // Get Unsplash API key from environment variables
    const unsplashAccessKey = process.env.UNSPLASH_ACCESS_KEY

    if (!unsplashAccessKey) {
      console.error("UNSPLASH_ACCESS_KEY is not set in environment variables")
      return getRandomFallbackLandmark()
    }

    // Try to get a random landmark image from Unsplash
    try {
      // Use a simple query for landmarks to ensure we get results
      const queries = [
        "famous landmark",
        "city skyline",
        "architectural landmark",
        "famous building",
        "iconic landmark",
      ]

      // Select a random query
      const randomQuery = queries[Math.floor(Math.random() * queries.length)]
      console.log(`Searching Unsplash for: "${randomQuery}"`)

      const unsplashUrl = `https://api.unsplash.com/photos/random?query=${encodeURIComponent(randomQuery)}&orientation=landscape`
      console.log(`Fetching from Unsplash URL: ${unsplashUrl}`)

      const unsplashResponse = await fetch(unsplashUrl, {
        headers: {
          Authorization: `Client-ID ${unsplashAccessKey}`,
        },
        next: { revalidate: 3600 }, // Cache for 1 hour
      })

      if (!unsplashResponse.ok) {
        console.error(`Unsplash API error: ${unsplashResponse.status} ${unsplashResponse.statusText}`)
        return getRandomFallbackLandmark()
      }

      const unsplashData = await unsplashResponse.json()
      console.log("Unsplash API response:", JSON.stringify(unsplashData, null, 2).substring(0, 200) + "...")

      if (unsplashData && unsplashData.urls) {
        const landmark: Landmark = {
          city: unsplashData.location?.city || "Beautiful Location",
          image: unsplashData.urls.regular,
          name: unsplashData.alt_description || "Landmark",
        }

        // Cache the result
        landmarkCache = {
          landmark,
          timestamp: now,
        }

        console.log(`Successfully fetched image from Unsplash: ${landmark.name}, ${landmark.city}`)
        return landmark
      } else {
        console.error("Unexpected Unsplash API response format")
        return getRandomFallbackLandmark()
      }
    } catch (unsplashError) {
      console.error("Error fetching from Unsplash:", unsplashError)
      return getRandomFallbackLandmark()
    }
  } catch (error) {
    console.error("Error in getLocationBackground:", error)
    return getRandomFallbackLandmark()
  }
}

// Helper function to get a fallback landmark
function getRandomFallbackLandmark(): Landmark {
  console.log("Using fallback landmark")

  // Fallback images if API fails or no API key
  const fallbackLandmarks: Landmark[] = [
    {
      city: "New York",
      image: "https://images.unsplash.com/photo-1522083165195-3424ed129620?q=80&w=3270&auto=format&fit=crop",
      name: "Statue of Liberty",
    },
    {
      city: "San Francisco",
      image: "https://images.unsplash.com/photo-1501594907352-04cda38ebc29?q=80&w=3270&auto=format&fit=crop",
      name: "Golden Gate Bridge",
    },
    {
      city: "Paris",
      image: "https://images.unsplash.com/photo-1511739001486-6bfe10ce785f?q=80&w=3270&auto=format&fit=crop",
      name: "Eiffel Tower",
    },
    {
      city: "London",
      image: "https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?q=80&w=3270&auto=format&fit=crop",
      name: "London Eye",
    },
    {
      city: "Tokyo",
      image: "https://images.unsplash.com/photo-1536098561742-ca998e48cbcc?q=80&w=2936&auto=format&fit=crop",
      name: "Tokyo Tower",
    },
    {
      city: "Sydney",
      image: "https://images.unsplash.com/photo-1506973035872-a4ec16b8e8d9?q=80&w=2670&auto=format&fit=crop",
      name: "Sydney Opera House",
    },
    {
      city: "Rome",
      image: "https://images.unsplash.com/photo-1552832230-c0197dd311b5?q=80&w=2670&auto=format&fit=crop",
      name: "Colosseum",
    },
    {
      city: "Dubai",
      image: "https://images.unsplash.com/photo-1582672060674-bc2bd808a8f5?q=80&w=2670&auto=format&fit=crop",
      name: "Burj Khalifa",
    },
  ]

  // Return a random fallback
  const randomIndex = Math.floor(Math.random() * fallbackLandmarks.length)
  return fallbackLandmarks[randomIndex]
}

