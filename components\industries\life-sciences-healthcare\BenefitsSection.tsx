"use client"

import { TrendingUp, ShieldCheck, Search, Thermometer, FileText, Zap } from 'lucide-react';

const benefits = [
  {
    icon: ShieldCheck,
    title: "Enhanced Regulatory Compliance",
    description: "Streamline documentation and adhere to stringent global healthcare standards."
  },
  {
    icon: Thermometer,
    title: "Improved Product Integrity",
    description: "Ensure the safe and secure handling of temperature-sensitive and high-value materials."
  },
  {
    icon: Search,
    title: "Resilient & Transparent Sourcing",
    description: "Access a global network of verified suppliers for critical APIs and specialized materials."
  },
  {
    icon: TrendingUp,
    title: "Optimized Cold Chain Logistics",
    description: "Reduce the risk of spoilage and ensure timely delivery of life-saving products."
  },
  {
    icon: FileText,
    title: "Reduced Administrative Burden",
    description: "Automate compliance checks, document management, and logistics coordination."
  },
  {
    icon: Zap,
    title: "Faster Time-to-Market",
    description: "Accelerate R&D and production cycles with more efficient material sourcing."
  }
];

export default function BenefitsSection() {
  return (
    <section className="py-16 md:py-24 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Secure Your Supply Chain, Accelerate Innovation, Improve Patient Outcomes
          </h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            Benefits for the Life Sciences & Healthcare Industry
          </p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 flex flex-col items-center text-center">
              <div className="p-4 bg-[#028475] rounded-full mb-4 inline-block">
                <benefit.icon className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2">{benefit.title}</h3>
              <p className="text-gray-600 text-sm flex-grow">
                {benefit.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}