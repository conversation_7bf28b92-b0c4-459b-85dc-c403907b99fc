import Link from "next/link";
import Image from "next/image";
import { SocialIcon } from "./social-icon";

export function CareersFooter() {
  return (
    <footer className="bg-[#3A3A3A] text-white">
      {/* Equal Opportunity Employer Statement */}
      <div className="bg-[#2D2D2D] py-4">
        <div className="container mx-auto px-4 text-center text-sm text-gray-300">
          <p>
            StreamL<PERSON> is an Equal Opportunity Employer. Employment decisions are
            made without regard to race, color, religion, national or ethnic
            origin, sex, sexual orientation, gender identity or expression, age,
            disability, protected veteran status or other characteristics
            protected by law.
          </p>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 gap-10 md:grid-cols-12">
          {/* Logo - Spanning 3 columns on md and up */}
          <div className="md:col-span-3 flex justify-center md:justify-start">
            <Link href="/" legacyBehavior>
              <a>
                <Image
                  src="/images/homepage/main header logo.svg" // Assuming this is the correct path for the logo
                  alt="StreamLnk Logo"
                  width={250} // Adjusted size for the image
                  height={60} // Adjusted size for the image
                  className="h-auto"
                />
              </a>
            </Link>
          </div>

          {/* Help Section - Spanning 3 columns on md and up */}
          <div className="md:col-span-3">
            <h3 className="mb-5 text-lg font-semibold text-white">Help</h3>
            <ul className="space-y-3">
              <li>
                <Link href="#" legacyBehavior>
                  <a className="text-gray-400 hover:text-white transition-colors">
                    Accessibility
                  </a>
                </Link>
              </li>
              <li>
                <Link href="#" legacyBehavior>
                  <a className="text-gray-400 hover:text-white transition-colors">
                    Candidate Privacy Notice
                  </a>
                </Link>
              </li>
              <li>
                <Link href="#" legacyBehavior>
                  <a className="text-gray-400 hover:text-white transition-colors">
                    Recruitment Fraud Alert
                  </a>
                </Link>
              </li>
              <li>
                <Link href="#" legacyBehavior>
                  <a className="text-gray-400 hover:text-white transition-colors">
                    Recruitment/ Staffing Agencies
                  </a>
                </Link>
              </li>
            </ul>
          </div>

          {/* Explore More Section - Spanning 3 columns on md and up */}
          <div className="md:col-span-3">
            <h3 className="mb-5 text-lg font-semibold text-white">Explore more</h3>
            <ul className="space-y-3">
              <li>
                <Link href="#" legacyBehavior>
                  <a className="text-gray-400 hover:text-white transition-colors">
                    Contingent Worker Program
                  </a>
                </Link>
              </li>
              <li>
                <Link href="#" legacyBehavior>
                  <a className="text-gray-400 hover:text-white transition-colors">
                    Company Leadership
                  </a>
                </Link>
              </li>
              <li>
                <Link href="#" legacyBehavior>
                  <a className="text-gray-400 hover:text-white transition-colors">
                    Digital Transformation
                  </a>
                </Link>
              </li>
              <li>
              </li>
              <li>
                <Link href="#" legacyBehavior>
                  <a className="text-gray-400 hover:text-white transition-colors">
                    StreamLnk Home
                  </a>
                </Link>
              </li>
            </ul>
          </div>

          {/* Let's Stay in Touch Section - Spanning 3 columns on md and up */}
          <div className="md:col-span-3">
            <h3 className="mb-5 text-lg font-semibold text-white">Let's stay in touch</h3>
            <p className="text-sm text-gray-400 mb-3">
              Share your StreamLnk experience
            </p>
            <div className="flex space-x-3">
              <SocialIcon platform="facebook" href="#" />
              <SocialIcon platform="instagram" href="#" />
              <SocialIcon platform="linkedin" href="#" />
              <SocialIcon platform="youtube" href="#" />
              <SocialIcon platform="twitter" href="#" />
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar with Copyright and Links */}
      <div className="border-t border-gray-700 py-6">
        <div className="container mx-auto flex flex-col items-center justify-between gap-4 px-4 md:flex-row">
          <p className="text-sm text-gray-400">
            Copyright © {new Date().getFullYear()} StreamLnk - all rights reserved
          </p>
          <nav className="flex flex-wrap justify-center gap-x-6 gap-y-2 md:justify-end">
            <Link href="/careers" legacyBehavior>
              <a className="text-sm text-gray-400 hover:text-white transition-colors">
                Careers
              </a>
            </Link>
            <Link href="/privacy" legacyBehavior> 
              <a className="text-sm text-gray-400 hover:text-white transition-colors">
                Privacy
              </a>
            </Link>
            <Link href="/terms" legacyBehavior>
              <a className="text-sm text-gray-400 hover:text-white transition-colors">
                Terms
              </a>
            </Link>
            <Link href="/cookies" legacyBehavior> 
              <a className="text-sm text-gray-400 hover:text-white transition-colors">
                Cookies
              </a>
            </Link>
          </nav>
        </div>
      </div>
    </footer>
  );
}