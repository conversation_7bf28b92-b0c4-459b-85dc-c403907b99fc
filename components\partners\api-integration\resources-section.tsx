import { BarChart3, FileText, Map, LineChart } from "lucide-react"

export function ResourcesSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <div className="mb-12 text-center">
            <h2 className="text-3xl font-bold text-[#004235] mb-4">StreamResources+ & Developer Portal Access</h2>
            <div className="w-20 h-1 bg-[#028475] mx-auto mb-6"></div>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              Approved partners can gain access to premium insights and tools via our StreamResources+ portal and the
              dedicated Developer Portal
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200 flex flex-col">
              <div className="flex items-center mb-6">
                <BarChart3 className="h-8 w-8 text-[#028475] mr-3" />
                <h3 className="text-xl font-bold text-[#004235]">KPI Scoring Feeds</h3>
              </div>
              <p className="text-gray-700 mb-4 flex-grow">
                Access performance data on partners, routes, and suppliers (anonymized and aggregated) to enhance your
                analytics and decision-making capabilities.
              </p>
              <div className="bg-[#F2F2F2] p-4 rounded-md">
                <p className="text-sm text-gray-600">
                  <span className="font-semibold">Sample data point:</span> Supplier reliability scores across 12 global
                  regions, updated weekly
                </p>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200 flex flex-col">
              <div className="flex items-center mb-6">
                <Map className="h-8 w-8 text-[#028475] mr-3" />
                <h3 className="text-xl font-bold text-[#004235]">Regional Demand Trends</h3>
              </div>
              <p className="text-gray-700 mb-4 flex-grow">
                Insights into product demand shifts and market hotspots, helping you identify emerging opportunities and
                optimize your offerings.
              </p>
              <div className="bg-[#F2F2F2] p-4 rounded-md">
                <p className="text-sm text-gray-600">
                  <span className="font-semibold">Sample data point:</span> Month-over-month demand changes for key
                  polymer categories by geographic region
                </p>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200 flex flex-col">
              <div className="flex items-center mb-6">
                <FileText className="h-8 w-8 text-[#028475] mr-3" />
                <h3 className="text-xl font-bold text-[#004235]">Document Compliance Heatmaps</h3>
              </div>
              <p className="text-gray-700 mb-4 flex-grow">
                Visualizations of compliance adherence across different regions or product types, helping you understand
                regulatory landscapes and risk factors.
              </p>
              <div className="bg-[#F2F2F2] p-4 rounded-md">
                <p className="text-sm text-gray-600">
                  <span className="font-semibold">Sample data point:</span> Documentation error rates by document type
                  and country of origin
                </p>
              </div>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200 flex flex-col">
              <div className="flex items-center mb-6">
                <LineChart className="h-8 w-8 text-[#028475] mr-3" />
                <h3 className="text-xl font-bold text-[#004235]">StreamIndex™ Price Movement Tracker</h3>
              </div>
              <p className="text-gray-700 mb-4 flex-grow">
                Access to our proprietary index on industrial commodity price movements, providing valuable market
                intelligence for pricing strategies and forecasting.
              </p>
              <div className="bg-[#F2F2F2] p-4 rounded-md">
                <p className="text-sm text-gray-600">
                  <span className="font-semibold">Sample data point:</span> Weekly price trend indicators for 50+
                  industrial commodities across major markets
                </p>
              </div>
            </div>
          </div>

          <div className="mt-12 p-6 bg-[#004235] text-white rounded-lg shadow-sm">
            <h3 className="text-xl font-bold mb-4">Developer Portal Preview</h3>
            <p className="mb-4">
              Our comprehensive developer portal provides everything you need to successfully integrate with StreamLnk:
            </p>
            <ul className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <li className="flex items-center">
                <span className="text-[#028475] mr-2">•</span>
                Interactive API documentation
              </li>
              <li className="flex items-center">
                <span className="text-[#028475] mr-2">•</span>
                Code samples in multiple languages
              </li>
              <li className="flex items-center">
                <span className="text-[#028475] mr-2">•</span>
                Authentication guides
              </li>
              <li className="flex items-center">
                <span className="text-[#028475] mr-2">•</span>
                Webhook configuration tools
              </li>
              <li className="flex items-center">
                <span className="text-[#028475] mr-2">•</span>
                Testing environment
              </li>
              <li className="flex items-center">
                <span className="text-[#028475] mr-2">•</span>
                Performance monitoring dashboard
              </li>
            </ul>
            <p className="text-sm opacity-90">Full access granted to approved integration partners</p>
          </div>
        </div>
      </div>
    </section>
  )
}
