import { Card, CardContent } from "@/components/ui/card"
import {
  Truck,
  Warehouse,
  Package,
  FileCheck,
  ClipboardCheck,
  FlaskConical,
  CreditCard,
  Users,
  Briefcase,
} from "lucide-react"

export function WhoShouldListSection() {
  const serviceCategories = [
    {
      icon: <Truck className="h-10 w-10 text-[#004235]" />,
      title: "Freight Carriers & 3PLs",
      description:
        "Providers of road, rail, sea, and air transport, as well as comprehensive third-party logistics solutions.",
    },
    {
      icon: <Warehouse className="h-10 w-10 text-[#004235]" />,
      title: "Warehouse Operators & Storage Partners",
      description:
        "Facilities offering secure bulk or packaged goods storage, inventory management, and cross-docking.",
    },
    {
      icon: <Package className="h-10 w-10 text-[#004235]" />,
      title: "Packaging Providers",
      description: "Specialists in industrial bagging, labeling, repackaging, kitting, and custom packaging solutions.",
    },
    {
      icon: <FileCheck className="h-10 w-10 text-[#004235]" />,
      title: "Customs Clearance Agents & Brokers",
      description:
        "Licensed professionals managing import/export declarations, duties, and compliance at national and international borders.",
    },
    {
      icon: <ClipboardCheck className="h-10 w-10 text-[#004235]" />,
      title: "Compliance, Certification & Inspection Firms",
      description:
        "Experts in document preparation (MSDS, REACH, FDA), quality testing, pre-shipment inspections, and COA generation.",
    },
    {
      icon: <FlaskConical className="h-10 w-10 text-[#004235]" />,
      title: "Inspection & Testing Laboratories",
      description: "Facilities providing product validation, material analysis, and certification services.",
    },
    {
      icon: <CreditCard className="h-10 w-10 text-[#004235]" />,
      title: "Trade Finance & Escrow Partners",
      description:
        "Institutions offering BNPL (Buy Now, Pay Later), milestone-based payment release, and other trade finance solutions.",
    },
    {
      icon: <Users className="h-10 w-10 text-[#004235]" />,
      title: "Sourcing Agencies & Buying Offices",
      description: "Firms acting on behalf of customers to procure materials and manage supply chains.",
    },
    {
      icon: <Briefcase className="h-10 w-10 text-[#004235]" />,
      title: "Industrial Consultants & Advisors",
      description: "Experts providing specialized advice in logistics, supply chain optimization, or market entry.",
    },
  ]

  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Who Should List Their Services on StreamLnk?
          </h2>
          <p className="text-lg text-gray-700 mb-12 max-w-4xl">
            We welcome qualified and reputable service providers from across the entire industrial value chain. If your
            business supports global trade, StreamLnk is the place to be seen. This includes:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {serviceCategories.map((category, index) => (
              <Card key={index} className="border-none shadow-md hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="mb-4">{category.icon}</div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">{category.title}</h3>
                  <p className="text-gray-600">{category.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
