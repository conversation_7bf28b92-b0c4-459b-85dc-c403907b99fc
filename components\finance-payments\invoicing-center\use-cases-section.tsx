import { Building2, Users, DollarSign, Truck } from "lucide-react"
import UseCaseCard from "@/components/finance-payments/invoicing-center/use-case-card"

export default function UseCasesSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Use Cases</h2>
          <p className="text-gray-600 max-w-3xl">
            The Invoicing Center serves various roles across your supply chain.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <UseCaseCard
            icon={<Building2 className="h-8 w-8 text-[#028475]" />}
            title="Suppliers"
            description="Bill for shipped goods and track buyer payment compliance"
            features={["Generate invoices from shipments", "Track payment status", "Manage receivables efficiently"]}
          />
          <UseCaseCard
            icon={<Users className="h-8 w-8 text-[#028475]" />}
            title="Buyers"
            description="Centralize payable tracking and automate invoice reconciliation"
            features={["Organize incoming invoices", "Schedule payments", "Match invoices to purchase orders"]}
          />
          <UseCaseCard
            icon={<DollarSign className="h-8 w-8 text-[#028475]" />}
            title="Agents & Distributors"
            description="Monitor commission-based invoicing and payout cycles"
            features={["Track commission invoices", "Monitor payment schedules", "Generate commission reports"]}
          />
          <UseCaseCard
            icon={<Truck className="h-8 w-8 text-[#028475]" />}
            title="Freight & Packaging Partners"
            description="Submit and validate service invoices tied to shipment IDs"
            features={["Link invoices to shipments", "Track service billing", "Manage logistics-related payments"]}
          />
        </div>
      </div>
    </section>
  )
}