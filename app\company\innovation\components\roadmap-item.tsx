import type { ReactNode } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Clock } from "lucide-react"

interface RoadmapItemProps {
  icon: ReactNode
  title: string
  description: string
  timeframe: string
}

export default function RoadmapItem({ icon, title, description, timeframe }: RoadmapItemProps) {
  return (
    <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors h-full">
      <CardContent className="pt-6 flex flex-col h-full">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 rounded-full bg-[#004235]/10 flex-shrink-0">{icon}</div>
          <h3 className="font-semibold text-[#004235]">{title}</h3>
        </div>
        <p className="text-gray-600 text-sm mb-4">{description}</p>
        <div className="mt-auto flex items-center gap-2 text-sm text-[#028475]">
          <Clock className="h-4 w-4" />
          <span>{timeframe}</span>
        </div>
      </CardContent>
    </Card>
  )
}
