"use client";

import Link from "next/link";
import { <PERSON><PERSON><PERSON>, Bar<PERSON>hart2, Search } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function GettingStartedSection() {
  return (
    <section className="py-16 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Embed Sustainability into Your Supply Chain Operations Today.
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            Getting Started with ESG Tracking on StreamLnk
          </p>
          <p className="text-md text-gray-600 mb-10">
            Many ESG tracking features are integrated directly into your MyStreamLnk Customer Portal and E-Stream Supplier Portal. For advanced aggregated analytics and custom ESG reporting solutions, explore our StreamResources+ offerings.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-4">
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/request-demo?tool=esg-reporting&source=cta-main">
                REQUEST ESG DEMO
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>

            <Button
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/my-streamlnk/features/sustainable-sourcing"> {/* Placeholder link */}
                LEARN SUSTAINABLE SOURCING
                <Search className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          
          <div className="mt-4 flex justify-center">
            <Button
              variant="link"
              className="text-[#028475] hover:text-[#004235] w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/solutions/streamresources#esg-insights"> {/* Placeholder link */}
                EXPLORE ESG INSIGHTS
                <BarChart2 className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}