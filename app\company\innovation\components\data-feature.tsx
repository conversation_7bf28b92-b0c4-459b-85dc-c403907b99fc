import type { ReactNode } from "react"

interface DataFeatureProps {
  icon: ReactNode
  title: string
  description: string
}

export default function DataFeature({ icon, title, description }: DataFeatureProps) {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-[#f3f4f6] hover:border-[#028475] transition-colors">
      <div className="flex items-start gap-4">
        <div className="p-2 rounded-full bg-[#004235]/10 flex-shrink-0">{icon}</div>
        <div>
          <h3 className="font-semibold text-[#004235] mb-2">{title}</h3>
          <p className="text-gray-600 text-sm">{description}</p>
        </div>
      </div>
    </div>
  )
}
