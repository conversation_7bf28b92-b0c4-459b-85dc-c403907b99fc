"use client";

import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';

const featuredWhitepapers = [
  {
    id: 1,
    coverImage: "/images/whitepapers/cover-ai-polymers.jpg", // Placeholder image path
    title: "The AI Imperative: Revolutionizing Procurement in the Polymer Industry",
    summary: "This whitepaper explores how artificial intelligence is transforming traditional polymer sourcing, from predictive pricing to automated supplier matching and risk assessment...",
    pdfLink: "/whitepapers/ai-polymer-procurement.pdf" // Placeholder PDF link
  },
  {
    id: 2,
    coverImage: "/images/whitepapers/cover-resilient-supply-chains.jpg", // Placeholder image path
    title: "Building Resilient Supply Chains: A Framework for Post-Pandemic Industrial Trade",
    summary: "Learn key strategies for enhancing visibility, diversifying sourcing, and leveraging digital platforms like StreamLnk to build more robust and agile global supply chains...",
    pdfLink: "/whitepapers/resilient-supply-chains.pdf" // Placeholder PDF link
  },
  {
    id: 3,
    coverImage: "/images/whitepapers/cover-data-goldmine.jpg", // Placeholder image path
    title: "The Data Goldmine: Monetizing Insights from B2B Industrial Marketplaces",
    summary: "An exploration of how platforms like StreamLnk are creating new value through data analytics, benchmarking (StreamIndex™), and predictive intelligence...",
    pdfLink: "/whitepapers/data-goldmine-b2b.pdf" // Placeholder PDF link
  }
];

interface WhitepaperCardProps {
  whitepaper: typeof featuredWhitepapers[0];
}

function WhitepaperCard({ whitepaper }: WhitepaperCardProps) {
  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden flex flex-col border border-gray-200 hover:shadow-xl transition-shadow duration-300">
      <div className="relative w-full h-48">
        <Image 
          src={whitepaper.coverImage}
          alt={`Cover image for ${whitepaper.title}`}
          layout="fill"
          objectFit="cover"
          className="bg-gray-200" // Placeholder background if image fails to load
        />
      </div>
      <div className="p-6 flex flex-col flex-grow">
        <h3 className="text-xl font-semibold text-[#004235] mb-3 leading-tight">{whitepaper.title}</h3>
        <p className="text-gray-600 text-sm mb-4 flex-grow leading-relaxed">{whitepaper.summary}</p>
        <Button 
          variant="outline"
          className="mt-auto border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white transition-colors w-full"
          asChild
        >
          <Link href={whitepaper.pdfLink} target="_blank" rel="noopener noreferrer">
            <Download className="mr-2 h-4 w-4" />
            DOWNLOAD PDF
          </Link>
        </Button>
      </div>
    </div>
  );
}

export default function FeaturedWhitepapersSection() {
  return (
    <section className="py-16 md:py-24 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-3">
            Our Latest & Most Downloaded Research
          </h2>
          <p className="text-xl text-[#028475] mb-4">
            Featured Whitepapers
          </p>
          <div className="w-20 h-1 bg-[#028475] mx-auto"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredWhitepapers.map((paper) => (
            <WhitepaperCard key={paper.id} whitepaper={paper} />
          ))}
        </div>
      </div>
    </section>
  );
}