import type { ReactNode } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { CheckCircle2 } from "lucide-react"

interface PartnershipTypeProps {
  icon: ReactNode
  title: string
  focus: string
  examples: string[]
  benefit: string
}

export default function PartnershipType({ icon, title, focus, examples, benefit }: PartnershipTypeProps) {
  return (
    <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors">
      <CardContent className="pt-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 rounded-full bg-[#004235]/10 flex-shrink-0">{icon}</div>
          <h3 className="text-xl font-semibold text-[#004235]">{title}</h3>
        </div>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-[#028475] mb-1">Focus:</h4>
            <p className="text-gray-600">{focus}</p>
          </div>
          <div>
            <h4 className="font-medium text-[#028475] mb-1">Examples:</h4>
            <ul className="space-y-2">
              {examples.map((example, index) => (
                <li key={index} className="flex items-start gap-2">
                  <CheckCircle2 className="h-5 w-5 text-[#028475] mt-0.5 flex-shrink-0" />
                  <span className="text-gray-600">{example}</span>
                </li>
              ))}
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-[#028475] mb-1">Benefit for Partner:</h4>
            <p className="text-gray-600">{benefit}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
