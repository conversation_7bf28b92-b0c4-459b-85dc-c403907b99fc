"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CheckCircle2 } from "lucide-react"

export default function ContactForm() {
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Simulate form submission
    setTimeout(() => {
      setIsSubmitted(true)
    }, 1000)
  }

  if (isSubmitted) {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <div className="p-3 rounded-full bg-green-100 mb-4">
          <CheckCircle2 className="h-8 w-8 text-green-600" />
        </div>
        <h3 className="text-xl font-semibold text-[#004235] mb-2">Thank You for Your Interest!</h3>
        <p className="text-gray-600 text-center max-w-md">
          We've received your partnership proposal and will review it shortly. A member of our Global Partnerships team
          will be in touch with you soon.
        </p>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-2">
          <Label htmlFor="company-name">Company Name</Label>
          <Input
            id="company-name"
            placeholder="Your company name"
            required
            className="border-[#f3f4f6] focus-visible:ring-[#028475]"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="website">Company Website</Label>
          <Input
            id="website"
            type="url"
            placeholder="https://example.com"
            required
            className="border-[#f3f4f6] focus-visible:ring-[#028475]"
          />
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-2">
          <Label htmlFor="contact-name">Contact Name</Label>
          <Input
            id="contact-name"
            placeholder="Your full name"
            required
            className="border-[#f3f4f6] focus-visible:ring-[#028475]"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="contact-email">Contact Email</Label>
          <Input
            id="contact-email"
            type="email"
            placeholder="<EMAIL>"
            required
            className="border-[#f3f4f6] focus-visible:ring-[#028475]"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="partnership-type">Partnership Type of Interest</Label>
        <Select>
          <SelectTrigger className="border-[#f3f4f6] focus:ring-[#028475]">
            <SelectValue placeholder="Select partnership type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="strategic">Strategic Alliance</SelectItem>
            <SelectItem value="technology">Technology & Integration Partnership</SelectItem>
            <SelectItem value="ecosystem">Ecosystem & Service Partnership</SelectItem>
            <SelectItem value="channel">Channel & Reseller Partnership</SelectItem>
            <SelectItem value="data">Data & Insights Partnership</SelectItem>
            <SelectItem value="other">Other (Please specify)</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="proposal">Partnership Proposal</Label>
        <Textarea
          id="proposal"
          placeholder="Please describe your proposed partnership, including how you envision mutual value creation and any specific ideas for collaboration."
          rows={5}
          required
          className="border-[#f3f4f6] focus-visible:ring-[#028475]"
        />
      </div>

      <Button type="submit" className="w-full bg-[#004235] hover:bg-[#004235]/90">
        Submit Partnership Proposal
      </Button>
    </form>
  )
}
