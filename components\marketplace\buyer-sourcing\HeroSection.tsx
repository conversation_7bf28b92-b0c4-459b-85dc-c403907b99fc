import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <div className="flex items-center mb-4">
           {/*   <div className="w-10 h-1 bg-[#028475] mr-3"></div> */}
              {/* <span className="text-[#028475] font-semibold text-sm tracking-wider uppercase">MARKETPLACE</span> */}
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Source Smarter: Your Global Industrial Materials Marketplace
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Access verified suppliers, real-time pricing, and integrated logistics. StreamLnk's MyStreamLnk portal simplifies your entire industrial material sourcing and procurement journey.
            </p>
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white px-6"
              size="lg"
              asChild
            >
              <Link href="/mystreamlnk">
                Explore MyStreamLnk
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/marketplace/buyer-sourcing/hero-placeholder.jpg" // Placeholder image - replace with actual image
              alt="Global Marketplace for Industrial Materials on StreamLnk"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
}