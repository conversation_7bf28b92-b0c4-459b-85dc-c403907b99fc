"use client"

import { CheckCircle } from "lucide-react"

export default function EcosystemIntegrationSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            The Power of StreamLnk's Integrated Ecosystem
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            Our solutions aren't standalone tools – they are deeply interconnected components of a single, intelligent ecosystem. When you source a product, the platform can automatically coordinate logistics, manage payments, track compliance documents, and feed data into your analytics dashboard. This end-to-end integration eliminates manual handoffs, reduces errors, and provides unprecedented efficiency and control.
          </p>
            
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Seamless Data Flow</h3>
                  <p className="text-gray-600">Information entered once propagates throughout the system, eliminating redundant data entry and reducing errors.</p>
                </div>
              </div>
            </div>
              
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Unified Partner Network</h3>
                  <p className="text-gray-600">Access our entire ecosystem of verified suppliers, logistics providers, and financial partners through a single platform.</p>
                </div>
              </div>
            </div>
              
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Intelligent Automation</h3>
                  <p className="text-gray-600">AI-powered workflows that anticipate needs and automate routine tasks across the entire supply chain.</p>
                </div>
              </div>
            </div>
              
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Comprehensive Analytics</h3>
                  <p className="text-gray-600">Gain insights across all aspects of your operations with unified reporting and dashboards.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}