"use server"

import { resend, isResend<PERSON>onfigured } from "@/lib/resend"
import { ConfirmationEmail } from "@/components/emails/confirmation-email"
import { WelcomeEmail } from "@/components/emails/welcome-email"

type EmailType = "confirmation" | "welcome" | "password-reset"

export async function sendEmail({
  to,
  type,
  data,
}: {
  to: string
  type: EmailType
  data: Record<string, any>
}) {
  // Check if Resend is configured
  if (!isResendConfigured()) {
    console.error("Resend API key is not configured")
    return {
      error: "Email service is not configured. Please contact support.",
    }
  }

  try {
    // Select the appropriate email template based on type
    let subject = ""
    let react = null

    switch (type) {
      case "confirmation":
        subject = "Confirm your StreamLnk account"
        react = ConfirmationEmail({
          confirmationUrl: data.confirmationUrl,
          email: to
        })
        break
      case "welcome":
        subject = "Welcome to StreamLnk!"
        react = WelcomeEmail({
          name: data.name || "there",
          dashboardUrl: data.dashboardUrl,
        })
        break
      case "password-reset":
        subject = "Reset your StreamLnk password"
        // Add password reset email component when needed
        break
      default:
        return {
          error: "Invalid email type",
        }
    }

    // Send the email using Resend
    const { data: responseData, error } = await resend!.emails.send({
      from: "StreamLnk <<EMAIL>>",
      to: [to],
      subject,
      react,
    })

    if (error) {
      console.error("Error sending email:", error)
      return {
        error: "Failed to send email. Please try again.",
      }
    }

    return {
      success: true,
      messageId: responseData?.id,
    }
  } catch (error) {
    console.error("Unexpected error sending email:", error)
    return {
      error: "An unexpected error occurred. Please try again.",
    }
  }
}

