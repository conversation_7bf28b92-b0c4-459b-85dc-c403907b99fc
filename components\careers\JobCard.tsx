"use client"

import Link from "next/link";
import { ChevronDown, Star } from "lucide-react";

interface JobCardProps {
  title: string;
  category: string;
  jobId: string;
  location: string;
  date: string;
  description: string;
  locationsAvailable?: number; // Made optional
}

export function JobCard({ title, category, jobId, location, date, description, locationsAvailable }: JobCardProps) {
    return (
        <div className="border-b py-6">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-lg font-semibold text-[#00A991] hover:underline">
                <Link href="#">{title}</Link>
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                {category} | {jobId} | {location} | {date}
              </p>
              {locationsAvailable && (
                 <p className="mt-1 text-sm text-[#00A991] flex items-center cursor-pointer">
                   Job available in {locationsAvailable} locations <ChevronDown className="ml-1 h-4 w-4" />
                 </p>
              )}
              <p className="mt-3 text-sm text-gray-600">{description}</p>
            </div>
            <button className="ml-4 mt-1 text-gray-400 hover:text-[#00A991]">
              <Star className="h-5 w-5" />
            </button>
          </div>
        </div>
      )
}