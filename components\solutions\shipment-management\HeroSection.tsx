import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl">
          <div className="flex items-center mb-6">
            <div className="w-12 h-1 bg-[#028475] mr-3"></div>
            <span className="text-[#028475] font-medium">COMPREHENSIVE SHIPMENT MANAGEMENT</span>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
            Master Your Shipments: End-to-End Visibility and Control with StreamLnk
          </h1>
          <p className="text-lg text-gray-700 mb-8">
            Say goodbye to scattered information and blind spots in your supply chain. StreamLnk provides a unified platform to manage all your industrial material shipments, offering real-time tracking, automated updates, and centralized documentation.
          </p>
          <Button
            className="bg-[#004235] hover:bg-[#028475] text-white px-6"
            size="lg"
            asChild
          >
            <Link href="/request-demo">
              See Shipment Management in Action – Request Demo
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}