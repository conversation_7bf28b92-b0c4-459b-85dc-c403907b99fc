import { FileTex<PERSON>, Refresh<PERSON><PERSON>, Zap } from "lucide-react"

export function WhatIsStreamGlobeSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235]">
              What Is StreamGlobe?
            </h2>
            <p className="mx-auto max-w-[700px] text-gray-600 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              StreamGlobe is the dedicated customs clearance hub within StreamLnk's Global Forwarding division. It's an
              intelligent platform engineered to connect pre-vetted, licensed customs agents and brokers with a steady
              flow of incoming international shipments requiring expert clearance.
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-3 md:gap-8 mt-12">
          <div className="flex flex-col items-center space-y-2 rounded-lg border border-gray-200 p-6 shadow-sm">
            <div className="rounded-full bg-[#F2F2F2] p-3">
              <FileText className="h-6 w-6 text-[#028475]" />
            </div>
            <h3 className="text-xl font-bold text-[#004235]">Automating Document Delivery</h3>
            <p className="text-center text-gray-600">
              Receive pre-assigned shipments with all necessary documentation (commercial invoice, B/L, packing list,
              COA, etc.) automatically accessible.
            </p>
          </div>
          <div className="flex flex-col items-center space-y-2 rounded-lg border border-gray-200 p-6 shadow-sm">
            <div className="rounded-full bg-[#F2F2F2] p-3">
              <RefreshCw className="h-6 w-6 text-[#028475]" />
            </div>
            <h3 className="text-xl font-bold text-[#004235]">Centralizing Updates</h3>
            <p className="text-center text-gray-600">
              Update real-time clearance statuses directly in the portal, visible to relevant stakeholders like buyers,
              suppliers, and freight providers.
            </p>
          </div>
          <div className="flex flex-col items-center space-y-2 rounded-lg border border-gray-200 p-6 shadow-sm">
            <div className="rounded-full bg-[#F2F2F2] p-3">
              <Zap className="h-6 w-6 text-[#028475]" />
            </div>
            <h3 className="text-xl font-bold text-[#004235]">Synchronizing Processes</h3>
            <p className="text-center text-gray-600">
              Ensure smooth coordination between all parties involved in the import/export process.
            </p>
          </div>
        </div>
        <div className="mt-12 text-center">
          <p className="text-gray-600 md:text-lg">
            From import permits and duty calculations to final port releases, StreamGlobe provides you with the
            essential tools to manage, clear, and track your assigned shipments efficiently and transparently, all in
            one place.
          </p>
        </div>
      </div>
    </section>
  )
}
