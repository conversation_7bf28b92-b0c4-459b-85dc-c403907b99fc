import { CheckCircle } from "lucide-react"

export default function DataDrivenSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Data-Driven, AI-Powered, Continuously Updated
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamIndex™ is built on a foundation of comprehensive data and advanced analytics:
          </p>

          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Vast Data Ingestion</h3>
                  <p className="text-gray-600">StreamResources+ collects anonymized data from every transaction and interaction across all StreamLnk portals.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">AI & ML Processing</h3>
                  <p className="text-gray-600">Advanced algorithms cleanse, normalize, aggregate, and analyze this data, identifying patterns and calculating benchmark values.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Real-Time Updates</h3>
                  <p className="text-gray-600">StreamIndex™ figures are continuously updated to reflect the latest market conditions.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Accessible Insights</h3>
                  <p className="text-gray-600">Delivered to subscribers through interactive dashboards, premium features, DaaS API, and curated market intelligence reports.</p>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-12 p-6 bg-[#004235] text-white rounded-lg shadow-sm">
            <h3 className="text-xl font-bold mb-4">Accessible Through Multiple Channels</h3>
            <ul className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <li className="flex items-center">
                <span className="text-[#028475] mr-2">•</span>
                Interactive dashboards within the StreamResources+ portal
              </li>
              <li className="flex items-center">
                <span className="text-[#028475] mr-2">•</span>
                Premium features within E-Stream and MyStreamLnk (e.g., AI Price Advisor)
              </li>
              <li className="flex items-center">
                <span className="text-[#028475] mr-2">•</span>
                DaaS API for enterprise integration
              </li>
              <li className="flex items-center">
                <span className="text-[#028475] mr-2">•</span>
                Curated market intelligence reports
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  )
}