"use client";

import Link from "next/link";

const trendingTopics = [
  { name: "AI in Supply Chain", slug: "ai-supply-chain" },
  { name: "Global Logistics Costs", slug: "global-logistics-costs" },
  { name: "Sustainable Sourcing", slug: "sustainable-sourcing" },
  { name: "Polymer Market Outlook", slug: "polymer-market-outlook" },
  { name: "Customs Automation", slug: "customs-automation" },
  { name: "BNPL for B2B", slug: "bnpl-b2b" },
  { name: "Trade Compliance", slug: "trade-compliance" },
  { name: "ESG Reporting", slug: "esg-reporting" },
  { name: "Digital Freight", slug: "digital-freight" },
  { name: "Chemical Industry Trends", slug: "chemical-industry-trends" },
];

export default function TrendingTopicsSection() {
  return (
    <section className="py-12 md:py-16 bg-white">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
          Trending Now in Industrial Trade
        </h2>
        <p className="text-lg text-gray-700 mb-10 text-center max-w-2xl mx-auto">
          Explore the most discussed topics and emerging trends shaping the future of global commerce and supply chains.
        </p>
        <div className="flex flex-wrap justify-center gap-3 md:gap-4">
          {trendingTopics.map((topic) => (
            <Link
              key={topic.slug}
              href={`/resources/topics/${topic.slug}`} // Placeholder link, adjust as needed
              className="bg-[#f3f4f6] text-[#004235] hover:bg-[#028475] hover:text-white transition-colors duration-300 px-4 py-2 md:px-5 md:py-2.5 rounded-full text-sm md:text-base font-medium shadow-sm"
            >
              {topic.name}
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}