import { Globe, FileText, <PERSON><PERSON>heck, <PERSON><PERSON>hart3, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react"

export default function SolutionOverviewSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            StreamLnk & StreamGlobe+: Your Automated Gateway to Compliant Global Trade
          </h2>
          <p className="text-lg text-gray-700 mb-10">
            StreamLnk integrates customs clearance directly into your end-to-end trade workflow. Our StreamGlobe+ portal connects you with a network of licensed, vetted customs clearance agents, while our platform automates key processes:
          </p>

          <div className="space-y-8">
            {/* Feature 1 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <Globe className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Network of Vetted Customs Agents (StreamGlobe+)</h3>
                  <p className="text-gray-700">
                    Access qualified customs brokers in key ports and trade regions worldwide, all managed and performance-monitored through StreamLnk.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 2 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <FileText className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Automated Document Aggregation & Delivery</h3>
                  <p className="text-gray-700">
                    When an international shipment is initiated, StreamLnk automatically gathers necessary documents (Commercial Invoice, Packing List, B/L, COO) from E-Stream/MyStreamLnk and delivers them digitally to the assigned customs agent via StreamGlobe+.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 3 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <ShieldCheck className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">AI-Assisted HS Code Classification (Future Enhancement)</h3>
                  <p className="text-gray-700">
                    Tools to help suggest or verify Harmonized System (HS) codes for your products, reducing classification errors.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 4 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <FileText className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Automated POA Generation & Management</h3>
                  <p className="text-gray-700">
                    For door-to-door shipments requiring agent-specific Power of Attorney, StreamGlobe+ facilitates the generation of pre-filled templates and digital management of signed POAs.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 5 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <BarChart3 className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Real-Time Clearance Status Updates</h3>
                  <p className="text-gray-700">
                    Assigned customs agents update clearance milestones directly in StreamGlobe+ (e.g., Documents Submitted, Awaiting Inspection, Duties Paid, Cleared), providing live visibility to you in MyStreamLnk or E-Stream.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 6 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <FileText className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Centralized Compliance Document Storage</h3>
                  <p className="text-gray-700">
                    All customs-related documents for your shipments are archived securely and accessible through your portal's Document Vault.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 7 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <AlertTriangle className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Proactive Alerts for Document Needs or Issues</h3>
                  <p className="text-gray-700">
                    Receive notifications for missing information or if customs authorities flag an issue, enabling quicker resolution.
                  </p>
                </div>
              </div>
            </div>

            {/* Feature 8 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start">
                <div className="mr-4">
                  <BarChart3 className="h-8 w-8 text-[#028475]" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Integrated Duty & Tax Estimation (Future Enhancement)</h3>
                  <p className="text-gray-700">
                    Tools to provide estimated customs duties and taxes during the quoting phase.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}