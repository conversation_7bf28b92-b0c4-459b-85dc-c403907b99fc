"use client";

import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Info } from "lucide-react";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Elevate Your Market Intelligence: Choose Your StreamResources+ Subscription Plan
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Gain unparalleled access to real-time data, proprietary benchmarks, and predictive analytics from the StreamLnk ecosystem. Our tiered StreamResources+ subscriptions provide the insights you need to make smarter, faster, and more profitable decisions in global industrial trade.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                className="bg-[#004235] hover:bg-[#028475] text-white px-6 w-full sm:w-auto"
                size="lg"
                asChild
              >
                <Link href="#subscription-tiers"> {/* Link to comparison section on the same page */}
                  COMPARE PLANS
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button
                variant="outline"
                className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white px-6 w-full sm:w-auto"
                size="lg"
                asChild
              >
                <Link href="/request-demo?solution=streamresources-plus&source=hero">
                  REQUEST DEMO
                  <Info className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/placeholder-image.svg" // Placeholder - suggest user to replace with a relevant image for subscriptions/data analytics
              alt="StreamResources+ Subscription Plans"
              fill
              className="object-cover"
              priority
            />
            {/* TODO: Consider adding a more relevant image or illustration for data intelligence/subscriptions */}
          </div>
        </div>
      </div>
    </section>
  );
}