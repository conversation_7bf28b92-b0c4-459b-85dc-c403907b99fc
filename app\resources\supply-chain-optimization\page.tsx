"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/resources/supply-chain-optimization/HeroSection";
import IntroductionSection from "@/components/resources/supply-chain-optimization/IntroductionSection";
import PillarsSection from "@/components/resources/supply-chain-optimization/PillarsSection";
import ProcurementSection from "@/components/resources/supply-chain-optimization/ProcurementSection";
import LogisticsSection from "@/components/resources/supply-chain-optimization/LogisticsSection";
import InventorySection from "@/components/resources/supply-chain-optimization/InventorySection";
import ComplianceSection from "@/components/resources/supply-chain-optimization/ComplianceSection";
import TechnologySection from "@/components/resources/supply-chain-optimization/TechnologySection";
import ConclusionSection from "@/components/resources/supply-chain-optimization/ConclusionSection";
import CtaSection from "@/components/resources/supply-chain-optimization/CtaSection";

export default function SupplyChainOptimizationPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <IntroductionSection />
      <PillarsSection />
      <ProcurementSection />
      <LogisticsSection />
      <InventorySection />
      <ComplianceSection />
      <TechnologySection />
      <ConclusionSection />
      <CtaSection />

      <BottomFooter />
    </div>
  );
}