"use client";

import { useState, useEffect, useCallback, useMemo, memo } from "react";
import Link from "next/link";
import clsx from "clsx";
import * as Icons from "lucide-react";

import { Logo } from "./logo";
import { But<PERSON> } from "./ui/button";
import { CustomerPortalLogins } from "./customer-portal-logins";

// Import new component structure
import { DesktopNavLinks } from "./navigation/desktop-nav-links";
import { MobileNavMenu } from "./navigation/mobile-nav-menu";
import { navigationItems, customerPortalLinks } from "./navigation/navigation-data";

// Memoize static components for better performance
const MemoizedMobileNavMenu = memo(MobileNavMenu);
const MemoizedDesktopNavLinks = memo(DesktopNavLinks);
const MemoizedCustomerPortalLogins = memo(CustomerPortalLogins);

// Constants
const HEADER_HEIGHT = 80; // Approximate height of the top header bar
const SUBNAV_HEIGHT = 56; // Height of the sub navigation bar
const TOTAL_NAV_HEIGHT = HEADER_HEIGHT + SUBNAV_HEIGHT; // Total height when not scrolled
const STICKY_THRESHOLD = 150; // Scroll point to trigger sticky header transformation
const MOBILE_BREAKPOINT = 768; // Mobile breakpoint in pixels

// Custom hook for scroll behavior
function useScrollBehavior() {
  const [scrolled, setScrolled] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [visible, setVisible] = useState(true);
  
  // Optimized scroll handler with all dependencies properly declared
  const handleScroll = useCallback(() => {
    const currentScrollY = window.scrollY;

    setScrolled(currentScrollY > HEADER_HEIGHT); // Sticky starts after header height
    // Hide logic: Hide on scroll down past a threshold, show on scroll up
    setVisible(currentScrollY <= lastScrollY || currentScrollY < STICKY_THRESHOLD);
    setLastScrollY(currentScrollY);
  }, [lastScrollY]);

  useEffect(() => {
    // Initialize scroll state on mount
    handleScroll();
    window.addEventListener("scroll", handleScroll, { passive: true });
    // Cleanup
    return () => window.removeEventListener("scroll", handleScroll);
  }, [handleScroll]);

  return { scrolled, visible };
}

export function MainNav() {
  const { scrolled, visible } = useScrollBehavior();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  // Track open mega menu ID for styling the trigger (underline)
  const [openMegaMenu, setOpenMegaMenu] = useState<string | null>(null);

  // Close mobile menu and mega menus on route change
  useEffect(() => {
    const handleRouteChange = () => {
      setMobileMenuOpen(false);
      setOpenMegaMenu(null); // Close any open mega menus
    };

    // Listen for route changes
    window.addEventListener('popstate', handleRouteChange);
    return () => window.removeEventListener('popstate', handleRouteChange);
  }, []);

  // Close mega menu if window is resized past mobile breakpoint
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= MOBILE_BREAKPOINT && openMegaMenu !== null) {
        setOpenMegaMenu(null);
      }
      if (window.innerWidth >= MOBILE_BREAKPOINT && mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [openMegaMenu, mobileMenuOpen]);

  // Common styles for reuse
  const commonTransitionClasses = "transition-all duration-300";
  const commonContainerClasses = "container mx-auto px-4 h-full";
  const commonFlexClasses = "flex items-center h-full";
  
  // Memoized header component - optimized with proper dependencies
  const TopHeader = useMemo(() => {
    return (
      <header
        className={clsx(
          `bg-gradient-to-r from-[#004235] to-[#028475] ${commonTransitionClasses} z-50`,
          scrolled ? "fixed top-0 left-0 right-0 shadow-md" : "relative",
          (visible || !scrolled) ? "translate-y-0" : "-translate-y-full"
        )}
        style={{ height: HEADER_HEIGHT }}
      >
        <div className="container mx-auto px-4 h-full">
          <div className="flex items-center justify-between h-full">
            {/* Left Side: Logo and Sticky Nav Links */}
            <div className="flex items-center h-full">
              <Link href="/" className="flex items-center flex-shrink-0 mr-4">
                <Logo variant="light" width={200} height={45} className="h-10 md:h-12 w-auto" />
              </Link>
              {/* Navigation Links - Only visible when header is sticky */}
              {scrolled && (
                <MemoizedDesktopNavLinks 
                  variant="header" 
                  navigationItems={navigationItems} 
                  openMegaMenu={openMegaMenu} 
                  setOpenMegaMenu={setOpenMegaMenu} 
                />
              )}
            </div>

            {/* Right Side: Icons & Buttons */}
            <div className={`${commonFlexClasses} gap-4`}>
              {/* Search button - only visible when not scrolled */}
              {!scrolled && (
                <button className="text-white hover:text-opacity-80 transition-colors p-2 flex items-center gap-1" aria-label="Search">
                  <Icons.Search className="h-5 w-5" />
                  <span className="text-sm font-medium">Search</span>
                </button>
              )}

              {/* Location Selector with EN icon - only visible when not scrolled */}
              {!scrolled && (
                <Link 
                  href="/location"
                  className="flex items-center gap-1 text-white hover:text-opacity-80 transition-colors p-2 h-full"
                  aria-label="Location"
                >
                  <Icons.Globe className="h-5 w-5" />
                  <span className="text-sm font-medium">United States of America</span>
                </Link>
              )}

              {/* Customer Portal Logins for scrolled view */}
              {scrolled && (
                <div className="text-white">
                  <MemoizedCustomerPortalLogins variant="header" className="relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[3px] after:w-full after:bg-primary after:transition-transform after:duration-300 text-white after:bg-white" />
                </div>
              )}
            </div>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden text-white p-2 -mr-2 rounded-md focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              aria-label="Toggle menu"
              aria-expanded={mobileMenuOpen}
            >
              {mobileMenuOpen ? <Icons.X className="h-6 w-6" /> : <Icons.Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </header>
    );
  }, [scrolled, visible, openMegaMenu, mobileMenuOpen, setOpenMegaMenu, setMobileMenuOpen]);

  // Memoized subnav component
  const SubNav = useMemo(() => {
    return (
      <nav
        className={clsx(
          "bg-background border-b border-border hidden md:block transition-all duration-300 relative",
          scrolled ? "opacity-0 pointer-events-none invisible h-0" : "opacity-100 visible",
          // Ensure mega menus open *above* content even when subnav is invisible but header sticky
          openMegaMenu ? "z-50" : "z-20" // Raise z-index when mega menu is open
        )}
        style={{ height: `${SUBNAV_HEIGHT}px` }}
      >
        <div className="container mx-auto px-4 h-full">
          <div className="flex items-center h-full justify-between">
            {/* Main Navigation Links */}
            <MemoizedDesktopNavLinks 
              variant="subnav" 
              navigationItems={navigationItems} 
              openMegaMenu={openMegaMenu} 
              setOpenMegaMenu={setOpenMegaMenu} 
            />

            {/* Right Side - Customer Portal Logins */}
            <div className="flex items-center gap-4 h-full">
              <MemoizedCustomerPortalLogins variant="subnav" className="relative after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-[3px] after:w-full after:bg-primary after:transition-transform after:duration-300 text-foreground hover:text-primary after:bg-primary" />
            </div>
          </div>
        </div>
      </nav>
    );
  }, [scrolled, openMegaMenu, setOpenMegaMenu]);

  // Memoized mobile menu
  const MobileMenu = useMemo(() => {
    if (!mobileMenuOpen) return null;
    
    return (
      <MemoizedMobileNavMenu 
        navigationItems={navigationItems} 
        customerPortalLinks={customerPortalLinks} 
        setMobileMenuOpen={setMobileMenuOpen} 
      />
    );
  }, [mobileMenuOpen, setMobileMenuOpen]);

  return (
    <>
      {/* Top Header Bar */}
      {TopHeader}

      {/* Sub Navigation Bar */}
      {SubNav}

      {/* Add Padding to Body to prevent content jump */}
      <div 
        className={clsx(
          "transition-all duration-300",
          scrolled ? "pt-[0px]" : "pt-[0px]"
        )} 
        aria-hidden="true"
      />

      {/* Mobile Navigation Menu Overlay */}
      {MobileMenu}
    </>
  );
}
