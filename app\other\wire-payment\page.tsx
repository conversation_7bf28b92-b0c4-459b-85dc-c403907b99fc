import Image from "next/image"
import { Globe, Shield, ArrowRight, CreditCard, DollarSign, BarChart3, CheckCircle2, Building2 } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import PaymentMethodCard from "./components/payment-method-card"
import HowItWorksStep from "./components/how-it-works-step"
import UseCaseCard from "./components/use-case-card"
import DashboardFeature from "./components/dashboard-feature"

export default function WirePaymentPage() {
  return (
    <main className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-[#F2F2F2] py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
            <div className="flex flex-col justify-center space-y-4">
              <div className="inline-block rounded-lg bg-[#004235]/10 px-3 py-1 text-sm text-[#004235]">
                Global Payment Solutions
              </div>
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235]">
                Wire & Multi-Currency Payment Support
              </h1>
              <p className="text-gray-600 md:text-xl">
                Transact securely across borders with robust wire transfer and multi-currency payment capabilities
                designed for international scale.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button className="bg-[#004235] hover:bg-[#004235]/90">
                  Get Started <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                <Button variant="outline" className="border-[#028475] text-[#028475]">
                  Learn More
                </Button>
              </div>
            </div>
            <div className="flex justify-center">
              <div className="relative w-full max-w-[500px] h-[300px] md:h-[400px]">
                <Image
                  src="/placeholder.svg?height=400&width=500"
                  alt="Global payment illustration"
                  fill
                  className="object-contain"
                  priority
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Payment Methods & Currencies Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Supported Payment Methods & Currencies
            </h2>
            <p className="text-gray-600 max-w-3xl">
              StreamLnk supports a wide range of payment methods and currencies to facilitate global trade.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <PaymentMethodCard
              icon={<CreditCard className="h-10 w-10 text-[#028475]" />}
              title="Wire Transfers"
              features={[
                "Accept and send payments via SWIFT, SEPA, Fedwire, and domestic ACH networks",
                "Secure bank-to-bank settlement supported in over 120 countries",
              ]}
            />
            <PaymentMethodCard
              icon={<Globe className="h-10 w-10 text-[#028475]" />}
              title="Multi-Currency Transactions"
              features={[
                "Trade in your preferred currency, including USD, EUR, GBP, AED, JPY, CAD, MXN, CNY, and more",
                "Currency auto-detection at checkout and during quote acceptance",
              ]}
            />
            <PaymentMethodCard
              icon={<DollarSign className="h-10 w-10 text-[#028475]" />}
              title="Currency Pairing Flexibility"
              features={[
                "Pay in one currency, receive in another—StreamLnk handles conversion",
                "FX rates updated in real-time, with transparent margins",
              ]}
            />
            <PaymentMethodCard
              icon={<Shield className="h-10 w-10 text-[#028475]" />}
              title="Secure & Transparent"
              features={[
                "All payments are encrypted and verified via banking-level security standards",
                "StreamLnk's treasury operations include AML checks, beneficiary validation, and fraud monitoring",
                "Automatic reconciliation of invoices to your preferred ledger or ERP system",
              ]}
            />
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-16 md:py-24 bg-[#f3f4f6]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">How It Works</h2>
            <p className="text-gray-600 max-w-3xl">
              Our streamlined process makes international payments simple and efficient.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-5">
            <HowItWorksStep
              number={1}
              title="Select Your Payment Currency"
              description="Choose your preferred settlement currency during RFQ acceptance or at checkout."
            />
            <HowItWorksStep
              number={2}
              title="Receive Payment Instructions"
              description="Instantly receive bank account details and payment ID reference."
            />
            <HowItWorksStep
              number={3}
              title="Transfer Funds"
              description="Submit the wire through your bank or payment provider."
            />
            <HowItWorksStep
              number={4}
              title="Auto-Reconciliation"
              description="StreamLnk's platform automatically matches payments to open invoices."
            />
            <HowItWorksStep
              number={5}
              title="Confirmation & Clearance"
              description="Both parties receive confirmation, and shipments are cleared for dispatch."
            />
          </div>
        </div>
      </section>

      {/* Ideal For Section */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Ideal For</h2>
            <p className="text-gray-600 max-w-3xl">
              Our multi-currency payment solutions are designed for various business needs.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <UseCaseCard
              icon={<Building2 className="h-10 w-10 text-[#028475]" />}
              title="Cross-Border Buyers & Distributors"
              description="Seamless FX handling for businesses operating across multiple markets."
            />
            <UseCaseCard
              icon={<Globe className="h-10 w-10 text-[#028475]" />}
              title="International Suppliers"
              description="Transact in local currencies while billing internationally with ease."
            />
            <UseCaseCard
              icon={<CreditCard className="h-10 w-10 text-[#028475]" />}
              title="Freight and Logistics Partners"
              description="Operate across multi-region contracts with simplified payment processes."
            />
          </div>
        </div>
      </section>

      {/* Dashboard Section */}
      <section className="py-16 md:py-24 bg-[#F2F2F2]">
        <div className="container px-4 md:px-6">
          <div className="grid gap-12 lg:grid-cols-2 items-center">
            <div>
              <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-6">Real-Time Treasury Dashboard</h2>
              <p className="text-gray-600 mb-8">
                Users with access to StreamLnk's Treasury Center can monitor and manage all payment activities in one
                place.
              </p>

              <div className="space-y-4">
                <DashboardFeature
                  icon={<BarChart3 className="h-5 w-5 text-[#028475]" />}
                  title="Track incoming/outgoing wire payments"
                />
                <DashboardFeature icon={<DollarSign className="h-5 w-5 text-[#028475]" />} title="View live FX rates" />
                <DashboardFeature
                  icon={<CheckCircle2 className="h-5 w-5 text-[#028475]" />}
                  title="Reconcile payments by invoice, PO, and delivery"
                />
                <DashboardFeature
                  icon={<Building2 className="h-5 w-5 text-[#028475]" />}
                  title="Monitor payment statuses by counterparty and country"
                />
              </div>
            </div>
            <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
              <Image
                src="/placeholder.svg?height=400&width=600"
                alt="Treasury dashboard interface"
                fill
                className="object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-[#F2F2F2]">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center text-center">
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">
              Start Transacting Globally with Confidence
            </h2>
            <p className="text-gray-600 max-w-3xl mb-8">
              Wire transfers and multi-currency payments are just the beginning. StreamLnk makes global B2B trade
              frictionless, secure, and built for scale.
            </p>
            <Button className="bg-[#004235] hover:bg-[#004235]/90 px-8 py-6 text-lg">Get Started Today</Button>
          </div>
        </div>
      </section>
    </main>
  )
}
