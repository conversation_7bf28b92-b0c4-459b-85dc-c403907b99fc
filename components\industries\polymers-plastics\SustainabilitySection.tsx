import { Check<PERSON>ir<PERSON>, <PERSON>R<PERSON> } from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function SustainabilitySection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Driving the Circular Economy for Plastics with StreamLnk
          </h2>
          <p className="text-xl font-semibold text-[#028475] mb-6 text-center">
            Focus on Sustainability in Polymers
          </p>

          <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200 mb-8">
            <p className="text-gray-700 mb-6">
              StreamLnk is committed to supporting sustainability in the plastics industry by:
            </p>
            <ul className="space-y-4 text-gray-700">
              <li className="flex items-start">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-0 flex-shrink-0" />
                <span>Prominently featuring suppliers of recycled polymers (rPET, rHDPE, etc.) and bio-based plastics.</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-0 flex-shrink-0" />
                <span>Providing tools for buyers to filter and search for materials with specific recycled content percentages.</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-0 flex-shrink-0" />
                <span>Enabling traceability of sustainable material flows (future blockchain integration).</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-0 flex-shrink-0" />
                <span>Offering insights into the recycled polymer market via StreamResources+.</span>
              </li>
            </ul>

            <div className="mt-6">
              <Button
                variant="outline"
                className="border-[#028475] text-[#028475] hover:bg-[#028475] hover:text-white"
                asChild
              >
                <Link href="/solutions/esg-tracking-insights">
                  Learn More About Our ESG Solutions
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}