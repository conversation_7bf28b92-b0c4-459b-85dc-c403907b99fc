import Image from 'next/image';

interface HeroBannerProps {
  imageUrl: string;
  altText: string;
}

const HeroBanner: React.FC<HeroBannerProps> = ({ imageUrl, altText }) => {
  return (
    <section className="relative w-full h-[300px] md:h-[400px] lg:h-[500px]">
      <Image
        src={imageUrl}
        alt={altText}
        layout="fill"
        objectFit="cover"
        priority // Good for LCP
      />
      {/* Optional: Add an overlay or text on the banner */}
      {/* <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
        <h1 className="text-white text-4xl font-bold">Case Study Title</h1>
      </div> */}
    </section>
  );
};

export default HeroBanner;