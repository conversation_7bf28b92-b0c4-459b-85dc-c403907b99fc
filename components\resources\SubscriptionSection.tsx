"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON><PERSON>, Twitter } from "lucide-react"; // Assuming Twitter is X, or use appropriate icon
import Link from "next/link";

export default function SubscriptionSection() {
  return (
    <section className="py-16 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-4">
            Stay Ahead with Insights from StreamLnk
          </h2>
          <p className="text-xl text-gray-700 mb-6">
            Subscribe for Updates
          </p>
          <p className="text-lg text-gray-600 mb-8">
            Don't miss out on our latest research, analysis, and platform updates. Subscribe to our "StreamLnk Insights Newsletter."
          </p>
          <form className="flex flex-col sm:flex-row gap-3 mb-8">
            <Input 
              type="email" 
              placeholder="Your Email Address" 
              className="flex-grow focus-visible:ring-[#028475] border-gray-300"
            />
            <Button type="submit" className="bg-[#004235] hover:bg-[#028475] text-white px-6">
              SUBSCRIBE
            </Button>
          </form>
          <div className="flex justify-center items-center gap-4">
            <p className="text-gray-600">Follow us on:</p>
            <Link href="#" passHref legacyBehavior>
              <a target="_blank" rel="noopener noreferrer" className="text-[#028475] hover:text-[#004235]">
                <Linkedin className="h-6 w-6" />
              </a>
            </Link>
            <Link href="#" passHref legacyBehavior>
              <a target="_blank" rel="noopener noreferrer" className="text-[#028475] hover:text-[#004235]">
                <Twitter className="h-6 w-6" /> 
              </a>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}