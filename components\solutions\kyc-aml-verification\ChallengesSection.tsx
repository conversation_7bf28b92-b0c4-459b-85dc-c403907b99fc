import { AlertTriangle } from 'lucide-react'

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Why KYC/AML is Non-Negotiable in Today's Industrial Marketplace
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            In an interconnected global economy, verifying the identity and legitimacy of counterparties is more critical than ever, especially in high-value industrial trade. Without robust verification, businesses face:
          </p>

          <div className="space-y-4 max-w-3xl mx-auto">
            {[
                "Increased risk of engaging with fraudulent entities or shell companies.",
                "Exposure to money laundering activities and illicit financial flows.",
                "Potential violations of international sanctions and anti-terrorism financing regulations.",
                "Reputational damage from associating with non-compliant or unethical partners.",
                "Difficulty meeting regulatory due diligence requirements.",
                "Lack of trust hindering the willingness to engage in new cross-border trade relationships."
              ].map((challenge, index) => (
              <div key={index} className="flex items-start p-4 bg-gray-50 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-[#028475] mt-1 mr-3 flex-shrink-0" />
                <p className="text-gray-800">{challenge}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}