"use client"

import { MainNav } from "@/components/main-nav"
import { BottomFooter } from "@/components/bottom-footer"
import HeroSection from "@/components/solutions/global-logistics/HeroSection";
import ChallengesSection from "@/components/solutions/global-logistics/ChallengesSection";
import PlatformOverviewSection from "@/components/solutions/global-logistics/PlatformOverviewSection";
import BenefitsSection from "@/components/solutions/global-logistics/BenefitsSection";
import WorkflowVisualizationSection from "@/components/solutions/global-logistics/WorkflowVisualizationSection";
import CallToActionSection from "@/components/solutions/global-logistics/CallToActionSection";

export default function GlobalLogisticsPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <ChallengesSection />

      <PlatformOverviewSection />

      <BenefitsSection />

      <WorkflowVisualizationSection />

      <CallToActionSection />

      <BottomFooter />
    </div>
  )
}