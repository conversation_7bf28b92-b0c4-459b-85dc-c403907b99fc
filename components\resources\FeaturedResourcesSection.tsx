"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, FileText, Youtube, Newspaper } from "lucide-react"; // Added icons for cards

const featuredContent = [
  {
    type: "Article",
    icon: <Newspaper className="h-8 w-8 text-[#028475] mb-3" />,
    title: "Navigating Q4: Key Logistics Challenges and How to Prepare",
    excerpt: "A look at how recent global events are reshaping sourcing strategies...",
    linkText: "Read Now",
    href: "/blog/navigating-q4-logistics" // Placeholder
  },
  {
    type: "Whitepaper",
    icon: <FileText className="h-8 w-8 text-[#028475] mb-3" />,
    title: "The Future of AI in Industrial Procurement: A StreamLnk Perspective",
    excerpt: "This whitepaper explores how artificial intelligence is transforming traditional polymer sourcing...",
    linkText: "Download PDF",
    href: "/resources/whitepapers/ai-in-procurement" // Placeholder
  },
  {
    type: "Webinar Recording",
    icon: <Youtube className="h-8 w-8 text-[#028475] mb-3" />,
    title: "On-Demand Webinar: Maximizing Your Reach with E-Stream Auctions",
    excerpt: "Learn how suppliers can leverage the StreamLnk Auction Engine...",
    linkText: "Watch Now",
    href: "/resources/webinars/estream-auctions" // Placeholder
  }
];

export default function FeaturedResourcesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-[#004235] mb-4">
            Hot Off the Press: Our Latest & Most Popular Content
          </h2>
          <p className="text-xl text-gray-700">
            Featured Resources
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredContent.map((item, index) => (
            <div key={index} className="bg-[#F2F2F2] p-6 rounded-lg shadow-md flex flex-col items-start h-full">
              <div className="flex items-center mb-3">
                {item.icon} 
                <span className="ml-2 text-sm font-semibold text-[#004235]">{item.type.toUpperCase()}</span>
              </div>
              <h3 className="font-semibold text-[#004235] text-lg mb-2">{item.title}</h3>
              <p className="text-gray-600 text-sm mb-4 flex-grow">{item.excerpt}</p>
              <Button variant="link" className="text-[#028475] hover:text-[#004235] p-0 mt-auto" asChild>
                <Link href={item.href}>
                  {item.linkText}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}