"use client"

import { <PERSON>Nav } from "@/components/main-nav"
import { BottomFooter } from "@/components/bottom-footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import HeroSection from "@/components/solutions/shipment-management/HeroSection"
import ChallengesSection from "@/components/solutions/shipment-management/ChallengesSection"
import PlatformOverviewSection from "@/components/solutions/shipment-management/PlatformOverviewSection"
import HowItWorksSection from "@/components/solutions/shipment-management/HowItWorksSection"
import BenefitsSection from "@/components/solutions/shipment-management/BenefitsSection"
import CtaSection from "@/components/solutions/shipment-management/CtaSection"

export default function ShipmentManagementPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />

      <ChallengesSection />

      <PlatformOverviewSection />

      <HowItWorksSection />

      <BenefitsSection />

      <CtaSection />

      <BottomFooter />
    </div>
  )
}