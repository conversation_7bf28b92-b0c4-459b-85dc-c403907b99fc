"use client"

import { CheckCircle } from "lucide-react"

export function BusinessBenefitsSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Why Our Verified Network Matters for Your Business
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-10">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Reduce Sourcing Risk</h3>
                  <p className="text-gray-600">Significantly lower the risk of dealing with unqualified or non-compliant suppliers.</p>
                </div>
              </div>
            </div>
            
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Ensure Quality & Compliance</h3>
                  <p className="text-gray-600">Access suppliers who meet stringent quality and regulatory standards, backed by verified documentation.</p>
                </div>
              </div>
            </div>
            
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Save Time on Vetting</h3>
                  <p className="text-gray-600">Our pre-qualification process means you can focus on commercial terms rather than extensive background checks.</p>
                </div>
              </div>
            </div>
            
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Build Resilient Supply Chains</h3>
                  <p className="text-gray-600">Diversify your sourcing with trusted partners from various global regions.</p>
                </div>
              </div>
            </div>
            
            <div className="bg-[#F2F2F2] p-6 rounded-lg md:col-span-2">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Access Specialized & Sustainable Suppliers</h3>
                  <p className="text-gray-600">Easily find suppliers offering niche products, recycled materials, or those with strong ESG credentials.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}