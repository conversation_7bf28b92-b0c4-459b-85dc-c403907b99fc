"use client"

import { CheckCircle } from 'lucide-react';

const demands = [
  {
    title: "Stringent Regulatory Compliance",
    description: "Adherence to FDA, EMA, GDP (Good Distribution Practice), and other international/regional healthcare regulations."
  },
  {
    title: "Temperature-Controlled Logistics (Cold Chain)",
    description: "Managing the integrity of temperature-sensitive pharmaceuticals, biologics, and diagnostic materials throughout transit."
  },
  {
    title: "Product Integrity & Traceability",
    description: "Ensuring authenticity, preventing counterfeiting, and maintaining a clear chain of custody from raw material to finished product."
  },
  {
    title: "Specialized Material Sourcing",
    description: "Finding reliable global suppliers for high-purity pharmaceutical ingredients (APIs), excipients, medical-grade polymers, and specialized chemicals."
  },
  {
    title: "Quality Assurance & Validated Processes",
    description: "Working with suppliers and logistics partners who meet exacting quality standards and can provide validated documentation."
  },
  {
    title: "Secure & Time-Sensitive Deliveries",
    description: "Minimizing transit times and ensuring the secure handling of high-value, often life-saving, products."
  }
];

export default function ChallengesSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Meeting Uncompromising Standards in a Vital Industry?
          </h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            Critical Demands of the Life Sciences & Healthcare Supply Chain
          </p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {demands.map((demand, index) => (
            <div key={index} className="bg-gray-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
              <div className="flex items-center mb-3">
                <CheckCircle className="h-6 w-6 text-[#028475] mr-3 flex-shrink-0" />
                <h3 className="text-xl font-semibold text-[#004235]">{demand.title}</h3>
              </div>
              <p className="text-gray-600 text-sm">
                {demand.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}