import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  AreaChart, // Line Graph/Index Icon
  SearchCode, // Magnifying Glass/Industry Icon (alternative for specific industry focus)
  Globe, // Globe/Map Icon
  Settings2, // Gears/Consulting Icon
  Newspaper, // Newspaper/Lightbulb Icon
  ArrowRight
} from "lucide-react";

const analysisProducts = [
  {
    icon: <AreaChart className="h-10 w-10 text-[#028475] mb-4" />,
    title: "StreamIndex™ Market Trend Reports",
    description: "Regular (e.g., monthly, quarterly) reports providing detailed analysis of our proprietary StreamIndex™ benchmarks for specific industrial materials (polymers, chemicals, etc.). Includes price trends, volatility analysis, logistics efficiency changes, and demand/supply balance indicators by region.",
    availability: "Part of premium StreamResources+ subscriptions; sample reports may be available.",
    links: [
      { href: "/resources/market-analysis/sample-streamindex-report", text: "View Sample Report", type: "link" },
      { href: "/solutions/streamresources-plus/subscribe", text: "Subscribe to StreamResources+", type: "link" },
    ],
  },
  {
    icon: <SearchCode className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Sector-Specific Deep Dive Reports",
    description: 'In-depth analytical reports focusing on specific industrial sectors (e.g., "Global Automotive Polymer Sourcing Outlook 2025," "Impact of Energy Prices on Chemical Feedstock Availability"). Combines StreamLnk data with broader industry research.',
    availability: "Available for purchase individually or as part of higher-tier StreamResources+ subscriptions.",
    links: [
      { href: "/resources/market-analysis/sector-reports", text: "Browse Sector Reports", type: "link" },
    ],
  },
  {
    icon: <Globe className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Regional Market Outlooks",
    description: 'Focused analysis on the supply, demand, pricing, logistics, and risk landscape within specific geographic regions (e.g., "MENA Industrial Materials Market Review," "LATAM Polymer Trade Dynamics").',
    availability: "Part of premium StreamResources+ subscriptions; some may be available for individual purchase.",
    links: [
      { href: "/resources/market-analysis/regional-outlooks", text: "Explore Regional Outlooks", type: "link" },
    ],
  },
  {
    icon: <Settings2 className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Custom Research & Analytics Services",
    description: "For enterprise clients with specific intelligence needs, StreamLnk's data science team can provide bespoke research projects, custom dashboard development, and proprietary data analysis.",
    availability: "By direct engagement with the StreamResources+ team.",
    links: [
      { href: "/contact/custom-analysis-inquiry", text: "INQUIRE CUSTOM ANALYSIS", type: "button" },
    ],
  },
  {
    icon: <Newspaper className="h-10 w-10 text-[#028475] mb-4" />,
    title: 'Free Market Briefs & Summaries (via "Delivered by StreamLnk")',
    description: "We regularly publish high-level summaries and briefs of key market trends and StreamIndex™ movements on our \"Delivered by StreamLnk\" blog to provide value to the broader community.",
    availability: "",
    links: [
      { href: "/blog/market-briefs", text: "Read Latest Briefs", type: "link" },
    ],
  },
];

export default function AnalysisProductsSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Uncover Actionable Intelligence: Our Suite of Market Analysis Products
          </h2>
          <p className="text-lg text-gray-700">
            StreamLnk's Market Analysis Offerings (Powered by StreamResources+)
          </p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {analysisProducts.map((product, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-lg flex flex-col">
              <div className="flex justify-center md:justify-start">{product.icon}</div>
              <h3 className="text-xl font-semibold text-[#004235] mb-3 text-center md:text-left">{product.title}</h3>
              <p className="text-gray-600 mb-4 text-sm flex-grow text-center md:text-left">{product.description}</p>
              {product.availability && (
                <p className="text-xs text-gray-500 mb-4 italic text-center md:text-left">{product.availability}</p>
              )}
              <div className="mt-auto flex flex-col space-y-2 items-center md:items-start">
                {product.links.map((linkInfo, linkIndex) => (
                  linkInfo.type === "button" ? (
                    <Button
                      key={linkIndex}
                      className="bg-[#004235] hover:bg-[#028475] text-white w-full md:w-auto"
                      size="sm"
                      asChild
                    >
                      <Link href={linkInfo.href}>
                        {linkInfo.text}
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  ) : (
                    <Button
                      key={linkIndex}
                      variant="link"
                      className="text-[#028475] hover:text-[#004235] p-0 h-auto justify-center md:justify-start"
                      asChild
                    >
                      <Link href={linkInfo.href}>
                        {linkInfo.text}
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  )
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}