import Link from "next/link"
import Image from "next/image"
import { SocialIcon } from "./social-icon"

export function BottomFooter() {
  return (
    <footer className="bg-[#3A3A3A] text-white py-8">
      <div className="container mx-auto px-4">
        <div className="flex flex-col space-y-8">
          {/* Top Row: Logo, App Download, and Social Media */}
          <div className="flex flex-col md:flex-row justify-between items-center gap-6">
            {/* Logo */}
            <div className="flex items-center gap-2">
              <Image
                src="/images/homepage/main header logo.svg"
                alt="StreamLnk"
                width={250}
                height={60}
                className="h-16 w-auto"
              />
            </div>

            {/* StreamLnk App */}
            <div className="flex flex-col items-center md:items-start">
              <h3 className="text-white font-medium mb-2">StreamLnk App</h3>
              <p className="text-gray-400 text-sm mb-3">Manage your operations On the go</p>
              <div className="flex gap-3">
                <Link href="#" aria-label="Download on the App Store">
                  <Image 
                    src="/images/icons/iphone play store icon.svg" 
                    alt="Download on the App Store" 
                    width={120} 
                    height={40} 
                    className="h-10 w-auto"
                  />
                </Link>
                <Link href="#" aria-label="Get it on Google Play">
                  <Image 
                    src="/images/icons/google play icon.svg" 
                    alt="Get it on Google Play" 
                    width={135} 
                    height={40} 
                    className="h-10 w-auto"
                  />
                </Link>
              </div>
            </div>

            {/* Social Media */}
            <div className="flex flex-col items-center md:items-start">
              <h3 className="text-white font-medium mb-2">Connect with us</h3>
              <p className="text-gray-400 text-sm mb-3">Share your StreamLnk experience</p>
              <div className="flex gap-2">
                <SocialIcon platform="facebook" href="#" />
                <SocialIcon platform="instagram" href="#" />
                <SocialIcon platform="linkedin" href="#" />
                <SocialIcon platform="youtube" href="#" />
                <SocialIcon platform="twitter" href="#" />
                {/* If WhatsApp is desired, it can be added: <SocialIcon platform="whatsapp" href="#" /> */}
              </div>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="flex justify-center border-t border-gray-800 pt-6">
            <nav className="flex flex-wrap justify-center gap-x-8 gap-y-3">
              <Link href="/fraud-awareness" className="text-gray-400 hover:text-white transition-colors">
                Fraud Awareness
              </Link>
              <Link href="/legal" className="text-gray-400 hover:text-white transition-colors">
                Legal Notice
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white transition-colors">
                Terms of Use
              </Link>
              <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors">
                Privacy Notice
              </Link>
              <Link href="/dispute" className="text-gray-400 hover:text-white transition-colors">
                Dispute Resolution
              </Link>
              <Link href="/info" className="text-gray-400 hover:text-white transition-colors">
                Additional Information
              </Link>
              <Link href="/cookie-settings" className="text-gray-400 hover:text-white transition-colors">
                Cookie Settings
              </Link>
            </nav>
          </div>

          {/* Copyright */}
          <div className="text-center text-sm text-gray-400">
            Copyright © {new Date().getFullYear()} StreamLnk - all rights reserved
          </div>
        </div>
      </div>
    </footer>
  )
}

