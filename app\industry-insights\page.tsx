"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/industry-insights/HeroSection";
import ValuePropositionSection from "@/components/industry-insights/ValuePropositionSection";
import GatewaysSection from "@/components/industry-insights/GatewaysSection";
import TailoredInsightsSection from "@/components/industry-insights/TailoredInsightsSection";
import FeaturedReportsSection from "@/components/industry-insights/FeaturedReportsSection";
import NewsletterSection from "@/components/industry-insights/NewsletterSection";

export default function IndustryInsightsPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <ValuePropositionSection />
      <GatewaysSection />
      <TailoredInsightsSection />
      <FeaturedReportsSection />
      <NewsletterSection />

      <BottomFooter />
    </div>
  );
}