"use client";

import Link from 'next/link';
import { Tag, Brain, Network, ShieldCheck, Leaf, BarChartBig, ShoppingCart, Landmark, ArrowRight } from 'lucide-react';

const categories = [
  {
    name: "Artificial Intelligence & Machine Learning",
    icon: <Brain className="h-6 w-6 text-[#028475] mr-3" />,
    href: "/resources/whitepapers/category/ai-ml" // Placeholder link
  },
  {
    name: "Supply Chain Digitization & Transformation",
    icon: <Network className="h-6 w-6 text-[#028475] mr-3" />,
    href: "/resources/whitepapers/category/supply-chain-digitization" // Placeholder link
  },
  {
    name: "Logistics & Freight Optimization",
    icon: <ShoppingCart className="h-6 w-6 text-[#028475] mr-3" />,
    href: "/resources/whitepapers/category/logistics-freight" // Placeholder link
  },
  {
    name: "Risk Management & Compliance",
    icon: <ShieldCheck className="h-6 w-6 text-[#028475] mr-3" />,
    href: "/resources/whitepapers/category/risk-compliance" // Placeholder link
  },
  {
    name: "Sustainable Sourcing & ESG",
    icon: <Leaf className="h-6 w-6 text-[#028475] mr-3" />,
    href: "/resources/whitepapers/category/sustainability-esg" // Placeholder link
  },
  {
    name: "Market Analysis & Forecasting",
    icon: <BarChartBig className="h-6 w-6 text-[#028475] mr-3" />,
    href: "/resources/whitepapers/category/market-analysis" // Placeholder link
  },
  {
    name: "B2B E-commerce & Marketplace Dynamics",
    icon: <Tag className="h-6 w-6 text-[#028475] mr-3" />,
    href: "/resources/whitepapers/category/b2b-ecommerce" // Placeholder link
  },
  {
    name: "Fintech in Industrial Trade",
    icon: <Landmark className="h-6 w-6 text-[#028475] mr-3" />,
    href: "/resources/whitepapers/category/fintech-trade" // Placeholder link
  }
];

export default function CategoriesSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-3">
            Find Research Relevant to Your Interests
          </h2>
          <p className="text-xl text-[#028475] mb-4">
            Browse Whitepapers by Category
          </p>
          <div className="w-20 h-1 bg-[#028475] mx-auto"></div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {categories.map((category) => (
            <Link 
              key={category.name} 
              href={category.href}
              className="group flex items-center p-4 bg-gray-50 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 border border-transparent hover:border-[#028475] hover:bg-white"
            >
              {category.icon}
              <span className="text-base font-medium text-gray-700 group-hover:text-[#004235]">{category.name}</span>
              <ArrowRight className="ml-auto h-5 w-5 text-gray-400 group-hover:text-[#028475] transition-colors" />
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}