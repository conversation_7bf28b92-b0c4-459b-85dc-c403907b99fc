import { Logo } from "@/components/logo"
import { LoginForm } from "@/components/login-form"
import { BottomFooter } from "@/components/bottom-footer"
import { getLocationBackground } from "@/utils/get-location-background"

export default async function LoginPage() {
  let landmark
  try {
    landmark = await getLocationBackground()
    console.log("Login page received landmark:", landmark)
  } catch (error) {
    console.error("Error getting background for login page:", error)
    // Fallback if there's an error
    landmark = {
      city: "Default",
      image: "https://images.unsplash.com/photo-1518709766631-a6a7f45921c3?q=80&w=2670&auto=format&fit=crop",
      name: "Industrial",
    }
  }

  return (
    <div className="min-h-screen flex flex-col">
      {/* Background Image */}
      <div
        className="fixed inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url(${landmark.image})`,
          backgroundPosition: "center center",
          backgroundSize: "cover",
        }}
      >
        <div className="absolute inset-0 bg-black/30" />
      </div>

      {/* Content */}
      <main className="flex-1 relative z-10 flex items-center justify-center px-4 py-12">
        <div className="w-full max-w-md">
          {/* Logo */}
          <div className="flex justify-center mb-8">
            <Logo variant="light" size="large" className="h-12 w-auto" type="auth" />
          </div>

          {/* Login Card */}
          <LoginForm />

          {/* Location Badge */}
          <div className="mt-4 text-center">
            <span className="inline-block px-3 py-1 bg-white/10 backdrop-blur-sm rounded-full text-sm text-white">
              {landmark.name}, {landmark.city}
            </span>
          </div>
        </div>
      </main>

      {/* Bottom Footer */}
      <div className="mt-auto relative z-20">
        <BottomFooter />
      </div>
    </div>
  )
}

