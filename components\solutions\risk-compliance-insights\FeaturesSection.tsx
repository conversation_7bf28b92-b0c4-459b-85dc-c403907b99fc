import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, CheckCircle } from "lucide-react";

export default function FeaturesSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Illuminate Your Risk Landscape with StreamLnk Insights
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamLnk embeds risk assessment and compliance monitoring throughout its ecosystem, with advanced insights delivered via StreamResources+ and our proprietary iScore™ rating system:
          </p>

          <div className="space-y-8">
            {/* iScore Partner Ratings */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <ShieldCheck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">iScore™ Partner Ratings & Detailed Reports</h3>
                  <ul className="space-y-2 text-gray-700 mb-4">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Comprehensive, data-driven scores for Suppliers, Buyers, and Service Providers based on their operational performance, compliance history, financial indicators (where available), and platform feedback.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Access detailed iScore™ reports (premium feature) similar to D&B credit reports, offering a deep dive into a partner's reliability and risk profile.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* StreamIndex Risk & Compliance Indicators */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <BarChart3 className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">StreamIndex™ Risk & Compliance Indicators</h3>
                  <p className="text-gray-700 mb-4">
                    Real-time alerts and index values highlighting:
                  </p>
                  <ul className="space-y-2 text-gray-700 mb-4">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Regions or trade lanes with elevated logistics or customs delay risks.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Supplier categories with higher instances of document non-compliance.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Markets with increased payment default risk or FX volatility.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* AI-Powered Fraud Detection */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <ShieldCheck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">AI-Powered Fraud Detection</h3>
                  <p className="text-gray-700 mb-4">
                    Machine learning algorithms monitor transactional patterns to identify and flag potentially fraudulent activities.
                  </p>
                </div>
              </div>
            </div>

            {/* Predictive Risk Analytics */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <LineChart className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Predictive Risk Analytics</h3>
                  <ul className="space-y-2 text-gray-700 mb-4">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Forecast potential supply disruptions based on supplier iScore™ trends, geopolitical factors, and logistics performance.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Assess the risk associated with specific transactions or trade routes.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Compliance Monitoring Dashboards */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Compliance Monitoring Dashboards</h3>
                  <ul className="space-y-2 text-gray-700 mb-4">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Track the status of your own and your partners' compliance documents (licenses, certifications, insurance) with automated expiry alerts.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Access up-to-date summaries of key trade regulations relevant to your operations.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* ESG Compliance Tracking & Insights */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">ESG Compliance Tracking & Insights</h3>
                  <p className="text-gray-700 mb-4">
                    Tools to identify suppliers with strong ESG credentials and track the sustainability aspects of your supply chain.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}