"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { CheckCircle } from "lucide-react"

export function RequestDemoForm() {
  const [formSubmitted, setFormSubmitted] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real implementation, this would submit the form data to a backend
    setFormSubmitted(true)
    console.log("Demo request submitted")
  }

  return (
    <section id="demo-form" className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-md p-8">
          {!formSubmitted ? (
            <>
              <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
                Schedule Your Free, No-Obligation Demo Today
              </h2>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">Full Name*</label>
                    <Input id="fullName" placeholder="Your full name" required />
                  </div>
                  <div>
                    <label htmlFor="companyName" className="block text-sm font-medium text-gray-700 mb-1">Company Name*</label>
                    <Input id="companyName" placeholder="Your company name" required />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">Work Email*</label>
                    <Input id="email" type="email" placeholder="<EMAIL>" required />
                  </div>
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">Phone Number (Optional)</label>
                    <Input id="phone" type="tel" placeholder="+****************" />
                  </div>
                  <div>
                    <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-1">Country*</label>
                    <Select required>
                      <SelectTrigger id="country">
                        <SelectValue placeholder="Select your country" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="us">United States</SelectItem>
                        <SelectItem value="ca">Canada</SelectItem>
                        <SelectItem value="uk">United Kingdom</SelectItem>
                        <SelectItem value="au">Australia</SelectItem>
                        <SelectItem value="de">Germany</SelectItem>
                        <SelectItem value="fr">France</SelectItem>
                        <SelectItem value="jp">Japan</SelectItem>
                        <SelectItem value="cn">China</SelectItem>
                        <SelectItem value="in">India</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label htmlFor="companySize" className="block text-sm font-medium text-gray-700 mb-1">Company Size (Optional)</label>
                    <Select>
                      <SelectTrigger id="companySize">
                        <SelectValue placeholder="Select company size" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1-10">1-10 employees</SelectItem>
                        <SelectItem value="11-50">11-50 employees</SelectItem>
                        <SelectItem value="51-200">51-200 employees</SelectItem>
                        <SelectItem value="201-500">201-500 employees</SelectItem>
                        <SelectItem value="501-1000">501-1000 employees</SelectItem>
                        <SelectItem value="1001+">1001+ employees</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">Your Primary Role/Interest*</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="role-buyer" />
                      <label htmlFor="role-buyer" className="text-sm text-gray-600">I am a Buyer/Procurement Manager</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="role-supplier" />
                      <label htmlFor="role-supplier" className="text-sm text-gray-600">I am a Supplier/Sales Manager</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="role-agent" />
                      <label htmlFor="role-agent" className="text-sm text-gray-600">I am an Agent/Distributor</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="role-freight" />
                      <label htmlFor="role-freight" className="text-sm text-gray-600">I am a Freight/Logistics Provider</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="role-customs" />
                      <label htmlFor="role-customs" className="text-sm text-gray-600">I am a Customs Agent/Broker</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="role-packaging" />
                      <label htmlFor="role-packaging" className="text-sm text-gray-600">I am a Packaging/Warehouse Provider</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="role-data" />
                      <label htmlFor="role-data" className="text-sm text-gray-600">I am interested in Data/Analytics Solutions</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="role-other" />
                      <label htmlFor="role-other" className="text-sm text-gray-600">Other (Please specify)</label>
                    </div>
                  </div>
                </div>

                <div>
                  <label htmlFor="goals" className="block text-sm font-medium text-gray-700 mb-1">What are you hoping to achieve with StreamLnk? (Optional)</label>
                  <Textarea id="goals" placeholder="Tell us about your goals and challenges..." className="min-h-[100px]" />
                </div>

                <div>
                  <label htmlFor="availability" className="block text-sm font-medium text-gray-700 mb-1">Preferred Demo Availability (Optional)</label>
                  <Input id="availability" placeholder="E.g., Weekdays afternoons, Tuesday mornings, etc." />
                </div>

                <div>
                  <label htmlFor="referral" className="block text-sm font-medium text-gray-700 mb-1">How did you hear about us? (Optional)</label>
                  <Select>
                    <SelectTrigger id="referral">
                      <SelectValue placeholder="Select an option" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="search">Search Engine</SelectItem>
                      <SelectItem value="social">Social Media</SelectItem>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="colleague">Colleague Referral</SelectItem>
                      <SelectItem value="event">Industry Event</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-start space-x-2">
                  <Checkbox id="terms" required className="mt-1" />
                  <label htmlFor="terms" className="text-sm text-gray-600">
                    I agree to StreamLnk's <Link href="/privacy-policy" className="text-[#028475] hover:underline">Privacy Policy</Link> and <Link href="/terms-of-service" className="text-[#028475] hover:underline">Terms of Service</Link>.*
                  </label>
                </div>

                <Button 
                  type="submit" 
                  className="w-full bg-[#004235] hover:bg-[#028475] text-white py-3 font-medium text-lg"
                >
                  REQUEST MY PERSONALIZED DEMO
                </Button>

                <p className="text-center text-sm text-gray-500 mt-4">
                  Our team will contact you within 1 business day to confirm your demo details.
                </p>
              </form>
            </>
          ) : (
            <div className="text-center py-8">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-6">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h2 className="text-2xl font-bold text-[#004235] mb-4">Thank You for Your Request!</h2>
              <p className="text-gray-600 mb-6">We've received your demo request and will be in touch within one business day to schedule your personalized session.</p>
              <Button 
                className="bg-[#004235] hover:bg-[#028475] text-white"
                onClick={() => setFormSubmitted(false)}
              >
                Submit Another Request
              </Button>
            </div>
          )}
        </div>
      </div>
    </section>
  )
}