"use client";

import { WorkflowTimeline, WorkflowStep } from "@/components/ui/WorkflowTimeline";

export default function WorkflowVisualizationSection() {
  const timelineSteps: WorkflowStep[] = [
    {
      number: 1,
      title: "Order Confirmed",
      description: "Order placed through MyStreamLnk/E-Stream platform."
    },
    {
      number: 2,
      title: "AI Determines Logistics Needs",
      description: "System analyzes requirements for packaging, transport modes, and routing."
    },
    {
      number: 3,
      title: "StreamPak Assignment",
      description: "Packaging and warehousing services activated if needed."
    },
    {
      number: 4,
      title: "StreamFreight Assignment",
      description: "Land transport coordination for movement to/from warehouses and ports."
    },
    {
      number: 5,
      title: "StreamGlobe Assignment",
      description: "Sea freight booking and customs clearance via StreamGlobe+ network."
    },
    {
      number: 6,
      title: "Real-Time Tracking Updates",
      description: "Continuous visibility through the MyStreamLnk platform."
    },
    {
      number: 7,
      title: "Final Delivery Confirmed",
      description: "Shipment successfully delivered to destination."
    },
    {
      number: 8,
      title: "All Documents Archived",
      description: "Complete documentation stored in your Document Vault for future reference."
    }
  ];

  return (
    <WorkflowTimeline
      title="Connecting Continents, Simplifying Shipments"
      subtitle="Imagine sourcing polymers from Asia for delivery to your plant in Europe. StreamLnk can coordinate:"
      steps={timelineSteps}
      footer="All managed, tracked, and documented within one ecosystem, visible to you in MyStreamLnk."
    />
  );
}