import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

export function CtaSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2] text-[#004235]">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Join the Future of Global Supply Logistics?</h2>
        <p className="text-xl mb-10 max-w-3xl mx-auto text-gray-700">
          Elevate your packaging and warehousing services by connecting with a global network. Become a critical link in
          the international industrial supply chain.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button className="bg-[#004235] text-white hover:bg-[#028475] font-medium px-8 py-6 rounded-md text-lg">
            Apply to Join <PERSON>ak
          </Button>
          <Button
            variant="outline"
            className="border-[#004235] text-[#004235] hover:bg-[#004235]/10 font-medium px-8 py-6 rounded-md text-lg"
          >
            Learn More
          </Button>
        </div>
        <div className="mt-6 flex items-center justify-center">
          <span className="text-gray-700 text-sm">Portal Access: </span>
          <Link href="#" className="text-[#004235] text-sm underline ml-2 hover:text-[#028475]">
            StreamPak
          </Link>
        </div>
      </div>
    </section>
  )
}
