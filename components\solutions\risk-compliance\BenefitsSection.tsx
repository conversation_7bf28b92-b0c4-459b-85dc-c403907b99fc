import { CheckCircle } from 'lucide-react'

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Trade Globally with Peace of Mind
          </h2>
            
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Reduced Counterparty Risk</h3>
                  <p className="text-gray-600">Transact with greater confidence knowing partners are vetted and their performance is monitored.</p>
                </div>
              </div>
            </div>
              
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Streamlined Compliance</h3>
                  <p className="text-gray-600">Automate document management and stay ahead of regulatory requirements.</p>
                </div>
              </div>
            </div>
              
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Minimized Financial Exposure</h3>
                  <p className="text-gray-600">Secure payment mechanisms and proactive AR management protect your cash flow.</p>
                </div>
              </div>
            </div>
              
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Enhanced Operational Integrity</h3>
                  <p className="text-gray-600">Ensure quality and adherence to standards through transparent processes.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Improved Decision Making</h3>
                  <p className="text-gray-600">Leverage iScore™ and StreamIndex™ risk insights for strategic choices.</p>
                </div>
              </div>
            </div>
              
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Stronger Governance</h3>
                  <p className="text-gray-600">Maintain comprehensive audit trails and demonstrate robust internal controls.</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Protection of Brand Reputation</h3>
                  <p className="text-gray-600">By associating with a platform that prioritizes ethical and compliant trade.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}