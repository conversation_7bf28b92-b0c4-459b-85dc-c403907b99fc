"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl">
          <div className="flex items-center mb-6">
            <div className="w-12 h-1 bg-[#028475] mr-3"></div>
            <span className="text-[#028475] font-medium">SMART FREIGHT COORDINATION</span>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
            Intelligent Freight Coordination: Moving Your Industrial Materials Smarter, Faster, Globally
          </h1>
          <p className="text-lg text-gray-700 mb-8">
            Eliminate the complexities of managing multiple freight providers. StreamLnk offers a unified solution to coordinate all your land (truck & rail) and sea freight needs, powered by AI for optimal routing and carrier selection.
          </p>
          <Button
            className="bg-[#004235] hover:bg-[#028475] text-white px-6"
            size="lg"
            asChild
          >
            <Link href="/request-demo">
              Optimize Your Freight – Request a Demo
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}