"use client";

import { CheckCircle } from 'lucide-react';

export default function WhySubscribeSection() {
  const benefits = [
    {
      title: "Deep Market Understanding",
      description: "Access granular StreamIndex™ benchmarks for pricing, logistics, and risk."
    },
    {
      title: "Enhanced Due Diligence",
      description: "Utilize detailed iScore™ reports to vet potential partners thoroughly."
    },
    {
      title: "Predictive Foresight",
      description: "Leverage AI-powered demand, supply, and price forecasts."
    },
    {
      title: "Competitive Edge",
      description: "Gain insights that your competitors may not have."
    },
    {
      title: "Strategic Planning Support",
      description: "Make data-backed decisions on market entry, sourcing strategies, and risk mitigation."
    },
    {
      title: "Customizable Insights",
      description: "Tailor dashboards and reports to your specific needs (higher tiers)."
    },
    {
      title: "Direct Data Access",
      description: "Integrate StreamLnk intelligence into your own systems via API (Enterprise tier)."
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-[#004235] mb-4">
            Why Subscribe to StreamResources+? The Value of Premium Intelligence
          </h2>
          <p className="text-xl text-[#028475] font-semibold mb-6">
            Go Beyond Basic Data: Unlock the Full Power of the StreamLnk Ecosystem
          </p>
          <p className="text-lg text-gray-700">
            While StreamLnk's operational portals provide essential transactional tools, StreamResources+ offers a dedicated layer of advanced data analytics and market intelligence designed for strategic decision-making. Subscribing gives you:
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => (
            <div key={index} className="bg-[#f3f4f6] p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-center mb-3">
                <CheckCircle className="text-[#028475] h-7 w-7 mr-3 flex-shrink-0" />
                <h3 className="text-xl font-semibold text-[#004235]">{benefit.title}</h3>
              </div>
              <p className="text-gray-600 text-sm">
                {benefit.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}