import { Clip<PERSON><PERSON>he<PERSON>, MapPin, FileText, CheckCircle2, Bell } from "lucide-react"
import { Timeline } from "@/components/ui/timeline";

export function HowToJoinSection() {
  const steps = [
    {
      icon: <ClipboardCheck className="h-8 w-8 text-white" />,
      title: "Register",
      description:
        "Complete the online registration form and upload a copy of your valid customs license(s).",
    },
    {
      icon: <MapPin className="h-8 w-8 text-white" />,
      title: "Define Coverage",
      description:
        "Specify your port coverage, country expertise, and the types of clearances you handle (e.g., specific commodities, import/export).",
    },
    {
      icon: <FileText className="h-8 w-8 text-white" />,
      title: "Upload Business Documents",
      description:
        "Provide your business registration, insurance details, and any other relevant compliance certifications.",
    },
    {
      icon: <CheckCircle2 className="h-8 w-8 text-white" />,
      title: "Compliance Approval & Onboarding",
      description:
        "Our team will review your application. Upon approval, you'll receive system orientation and portal access.",
    },
    {
      icon: <Bell className="h-8 w-8 text-white" />,
      title: "Start Receiving Assignments",
      description:
        "<PERSON><PERSON> receiving notifications for shipment assignments and alerts relevant to your profile.",
    },
  ].map((item, index) => ({ ...item, title: `${index + 1}. ${item.title}` }));

  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-[#004235]">
              How to Join StreamGlobe
            </h2>
            <p className="mx-auto max-w-[700px] text-gray-600 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              Follow these steps to become a StreamGlobe partner
            </p>
          </div>
        </div>
        <div className="mx-auto max-w-4xl mt-12">
          <Timeline steps={steps} />
        </div>
      </div>
    </section>
  )
}
