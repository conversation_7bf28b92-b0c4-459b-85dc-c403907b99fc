"use client";

import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, ShieldCheck } from "lucide-react"; // Added ShieldCheck for a relevant icon

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              iScore™ by StreamLnk: Quantifying Trust, Enabling Confident Partnerships.
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Navigate the global marketplace with greater assurance. iScore™ is StreamLnk's intelligent rating system, providing objective, data-driven assessments of the performance, compliance, and reliability of every participant in our ecosystem.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
                size="lg"
                asChild
              >
                <Link href="/request-demo?product=iscore">
                  REQUEST ISCORE™ DEMO
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button
                variant="outline"
                className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
                size="lg"
                asChild
              >
                <Link href="/solutions/iscore"> {/* Placeholder link */}
                  LEARN ABOUT ISCORE™
                  <ShieldCheck className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/resources/iscore-hero.webp" // Placeholder - suggest user to replace
              alt="iScore by StreamLnk Hero Image"
              fill
              className="object-cover"
              priority
            />
            {/* TODO: Consider adding a more relevant image or illustration for iScore */}
          </div>
        </div>
      </div>
    </section>
  );
}