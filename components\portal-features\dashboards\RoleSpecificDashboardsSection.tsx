import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import ReusableTabs, { ReusableTabData } from '@/components/ui/reusable-tabs';

export default function RoleSpecificDashboardsSection() {
  // Define tab content components
  const BuyersContent = () => (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h3 className="text-xl font-bold text-[#004235] mb-4">For Buyers (MyStreamLnk Dashboard):</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-[#004235]">Key Widgets</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2 text-gray-700">
              <li>Active Orders</li>
              <li>Quotes Awaiting Approval</li>
              <li>Shipments In Transit</li>
              <li>Payments Due</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-[#004235]">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2 text-gray-700">
              <li>Search Products</li>
              <li>Request New Quote</li>
              <li>Track a Shipment</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-[#004235]">Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2 text-gray-700">
              <li>Personalized product recommendations (AI)</li>
              <li>Relevant StreamIndex™ price alerts</li>
              <li>ESG Impact Snapshot</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const SuppliersContent = () => (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h3 className="text-xl font-bold text-[#004235] mb-4">For Suppliers (E-Stream Dashboard):</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-[#004235]">Key Widgets</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2 text-gray-700">
              <li>New Orders</li>
              <li>Pending RFQs</li>
              <li>Low Stock Alerts</li>
              <li>Shipments Requiring Status Update</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-[#004235]">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2 text-gray-700">
              <li>Add New Product</li>
              <li>View Open Orders</li>
              <li>Access Auction Hub</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-[#004235]">Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2 text-gray-700">
              <li>StreamIndex™ Price Advisor alerts</li>
              <li>Demand trends for their products</li>
              <li>Supplier Reliability KPI snapshot</li>
              <li>Tier & Rewards status</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const AgentsContent = () => (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h3 className="text-xl font-bold text-[#004235] mb-4">For Agents (MyStreamLnk+ Dashboard):</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-[#004235]">Key Widgets</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2 text-gray-700">
              <li>New Leads/Customer Requests</li>
              <li>Quotes Awaiting Customer Approval</li>
              <li>Active Orders in Portfolio</li>
              <li>Pending Commissions</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-[#004235]">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2 text-gray-700">
              <li>Add New Customer</li>
              <li>Create New Quote</li>
              <li>View Commission Report</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-[#004235]">Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2 text-gray-700">
              <li>Portfolio Sales Volume</li>
              <li>Quote Conversion Rate</li>
              <li>Customer ESG Impact summary</li>
              <li>Agent Tier & Rewards status</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const LogisticsContent = () => (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h3 className="text-xl font-bold text-[#004235] mb-4">For Logistics Partners (StreamFreight, StreamGlobe+, StreamPak Dashboards):</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-[#004235]">Key Widgets</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2 text-gray-700">
              <li>New Job Assignments</li>
              <li>Active Jobs Requiring Update</li>
              <li>Compliance Document Alerts</li>
              <li>Pending Payouts</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-[#004235]">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2 text-gray-700">
              <li>View New Jobs</li>
              <li>Update Job Status</li>
              <li>Submit Invoice</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-[#004235]">Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2 text-gray-700">
              <li>Operational KPIs (On-Time Performance)</li>
              <li>Tier & Rewards status</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const ResourcesContent = () => (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h3 className="text-xl font-bold text-[#004235] mb-4">For StreamResources+ Subscribers:</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Customizable Widgets</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2 text-gray-700">
              <li>Displaying selected StreamIndex™ components</li>
              <li>Saved searches</li>
              <li>Market heatmaps</li>
              <li>Risk alerts</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-[#004235]">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc pl-5 space-y-2 text-gray-700">
              <li>Access specific analytic tools</li>
              <li>Generate reports</li>
              <li>Manage API keys</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  // Define tabs data for ReusableTabs component
  const tabsData: ReusableTabData[] = [
    {
      id: "buyers",
      title: "Buyers",
      contentComponent: BuyersContent
    },
    {
      id: "suppliers",
      title: "Suppliers",
      contentComponent: SuppliersContent
    },
    {
      id: "agents",
      title: "Agents",
      contentComponent: AgentsContent
    },
    {
      id: "logistics",
      title: "Logistics",
      contentComponent: LogisticsContent
    },
    {
      id: "resources",
      title: "StreamResources+",
      contentComponent: ResourcesContent
    }
  ];

  return (
    <section className="w-full py-12 md:py-24 bg-[#f3f4f6]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center space-y-4 text-center mb-10">
          <h2 className="text-2xl font-bold tracking-tighter sm:text-3xl md:text-4xl text-[#004235]">
            Intelligence Tailored to Your Role: What You'll See
          </h2>
          <p className="max-w-[700px] text-gray-700 md:text-xl">
            While each StreamLnk portal dashboard is customized, common powerful features include:
          </p>
        </div>

        <ReusableTabs 
          tabsData={tabsData}
          defaultTabId="buyers"
          className="w-full"
          tabsListClassName="mb-8"
          underlineColor="bg-[#004235]"
          activeTabTextColor="text-[#004235]"
          inactiveTabTextColor="text-[#004235]"
          activeTabBgColor="bg-white"
          inactiveTabBgColor="bg-[#f3f4f6]"
          hoverTabBgColor="hover:bg-gray-200"

        />
      </div>
    </section>
  );
}