"use client";

import { MainNav } from "@/components/main-nav";
import { BottomFooter } from "@/components/bottom-footer";
import HeroSection from "@/components/tools/esg-reporting/HeroSection";
import ImportanceSection from "@/components/tools/esg-reporting/ImportanceSection";
import HowStreamLnkHelpsSection from "@/components/tools/esg-reporting/HowStreamLnkHelpsSection";
import BenefitsSection from "@/components/tools/esg-reporting/BenefitsSection";
import GettingStartedSection from "@/components/tools/esg-reporting/GettingStartedSection";

export default function ESGReportingPage() {
  return (
    <div className="flex flex-col bg-white min-h-screen">
      <MainNav />

      <HeroSection />
      <ImportanceSection />
      <HowStreamLnkHelpsSection />
      <BenefitsSection />
      <GettingStartedSection />

      <BottomFooter />
    </div>
  );
}