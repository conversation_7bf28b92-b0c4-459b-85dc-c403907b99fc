import type React from "react"
import { Shield, Bell, Route, FileIcon as FileInvoice } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

interface AlertType {
  icon: React.ReactNode
  title: string
  description: string
}

export function ComplianceSection() {
  const alertTypes: AlertType[] = [
    {
      icon: <Bell className="h-8 w-8 text-[#028475]" />,
      title: "Proactive Reminders",
      description: "Receive notifications for expiring documents (insurance, permits, licenses) well in advance.",
    },
    {
      icon: <Shield className="h-8 w-8 text-[#028475]" />,
      title: "Performance Monitoring",
      description: "Get alerts related to delayed response times on bids or shipment updates.",
    },
    {
      icon: <Route className="h-8 w-8 text-[#028475]" />,
      title: "Route Integrity",
      description: "Notifications regarding significant route performance issues or delays.",
    },
    {
      icon: <FileInvoice className="h-8 w-8 text-[#028475]" />,
      title: "Financial Oversight",
      description: "Reminders for AR invoices that haven't been submitted or are overdue.",
    },
  ]

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-8 text-center">
          Compliance-Driven Performance for a Trusted Network
        </h2>
        <p className="text-lg text-gray-700 max-w-4xl mx-auto mb-12 text-center">
          StreamFreight features a smart alert system designed to maintain high standards and support your operations:
        </p>

        <div className="grid md:grid-cols-2 gap-6 mb-8">
          {alertTypes.map((alert, index) => (
            <Card key={index} className="border-none shadow-md">
              <CardContent className="p-6 flex items-start">
                <div className="mr-4 p-3 bg-[#F2F2F2] rounded-full">{alert.icon}</div>
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">{alert.title}</h3>
                  <p className="text-gray-700">{alert.description}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="bg-[#F2F2F2] p-6 rounded-lg border-l-4 border-l-[#004235] max-w-4xl mx-auto">
          <p className="text-gray-700 italic">
            Failure to maintain up-to-date documents or address critical performance alerts can result in restricted
            portal access, ensuring a high level of reliability and compliance across all network providers.
          </p>
        </div>
      </div>
    </section>
  )
}
