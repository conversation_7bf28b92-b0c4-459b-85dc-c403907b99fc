import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FlaskRoundIcon as <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Users } from "lucide-react"
import { Timeline } from "@/components/ui/timeline"

export function GetStartedSection() {
  const steps = [
    {
      icon: <ClipboardList className="h-8 w-8 text-white" />,
      title: "Apply",
      description: "Submit an overview of your product, service, or API suite and your proposed integration concept.",
    },
    {
      icon: <Users className="h-8 w-8 text-white" />,
      title: "Select Integration Package",
      description: "Discuss and select the preferred data tier, API access level, or integration package that aligns with your goals.",
    },
    {
      icon: <Flask className="h-8 w-8 text-white" />,
      title: "Sandbox Testing & Key Assignment",
      description: "Complete thorough testing in our sandbox environment and receive your API keys.",
    },
    {
      icon: <Key className="h-8 w-8 text-white" />,
      title: "Deploy & Monitor",
      description: "Deploy your integration into the live environment with ongoing monitoring and support.",
    },
    {
      icon: <BarChart className="h-8 w-8 text-white" />,
      title: "Co-Market (Optional)",
      description: "Collaborate on joint marketing initiatives to promote the integrated solution to our combined user bases.",
    },
  ]

  return (
    <section id="get-started" className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <div className="mb-12 text-center">
            <h2 className="text-3xl font-bold text-[#004235] mb-4">How to Get Started as a Data or API Partner</h2>
            <div className="w-20 h-1 bg-[#028475] mx-auto mb-6"></div>
          </div>

          <div className="max-w-4xl mx-auto">
            <Timeline steps={steps} />
          </div>
        </div>
      </div>
    </section>
  )
}
