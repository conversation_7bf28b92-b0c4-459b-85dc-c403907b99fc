import { FileText, ArrowRight } from "lucide-react"

interface NewsItemProps {
  title: string
  date: string
  description: string
  link: string
}

export default function NewsItem({ title, date, description, link }: NewsItemProps) {
  return (
    <div className="p-6 bg-white rounded-lg border border-[#f3f4f6] hover:border-[#028475] transition-colors">
      <div className="flex items-start gap-4">
        <div className="p-2 rounded-full bg-[#004235]/10 flex-shrink-0">
          <FileText className="h-6 w-6 text-[#028475]" />
        </div>
        <div>
          <h3 className="font-semibold text-[#004235] mb-1">{title}</h3>
          <p className="text-sm text-gray-500 mb-2">{date}</p>
          <p className="text-sm text-gray-600 mb-3">{description}</p>
          <a
            href={link}
            className="inline-flex items-center text-sm font-medium text-[#028475] hover:text-[#004235] transition-colors"
          >
            Read Press Release <ArrowRight className="ml-1 h-4 w-4" />
          </a>
        </div>
      </div>
    </div>
  )
}
