import type { ReactNode } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { CheckCircle2 } from "lucide-react"

interface EnvironmentalInitiativeProps {
  icon: ReactNode
  title: string
  features: string[]
}

export default function EnvironmentalInitiative({ icon, title, features }: EnvironmentalInitiativeProps) {
  return (
    <Card className="border-[#f3f4f6] hover:border-[#028475] transition-colors h-full">
      <CardContent className="pt-6 flex flex-col h-full">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 rounded-full bg-[#004235]/10 flex-shrink-0">{icon}</div>
          <h3 className="font-semibold text-[#004235]">{title}</h3>
        </div>
        <ul className="space-y-3 mt-2">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start gap-2">
              <CheckCircle2 className="h-5 w-5 text-[#028475] mt-0.5 flex-shrink-0" />
              <span className="text-gray-600 text-sm">{feature}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  )
}
