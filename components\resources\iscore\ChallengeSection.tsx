"use client";

import { AlertTriangle } from 'lucide-react';

export default function ChallengeSection() {
  const challenges = [
    {
      icon: <AlertTriangle className="h-8 w-8 text-[#028475] mr-4 flex-shrink-0" />,
      text: "Difficulty verifying claims of operational excellence or compliance."
    },
    {
      icon: <AlertTriangle className="h-8 w-8 text-[#028475] mr-4 flex-shrink-0" />,
      text: "Risk of engaging with unreliable suppliers leading to quality issues or delivery delays."
    },
    {
      icon: <AlertTriangle className="h-8 w-8 text-[#028475] mr-4 flex-shrink-0" />,
      text: "Exposure to payment defaults from buyers with poor financial track records."
    },
    {
      icon: <AlertTriangle className="h-8 w-8 text-[#028475] mr-4 flex-shrink-0" />,
      text: "Time-consuming and often subjective manual due diligence processes."
    },
    {
      icon: <AlertTriangle className="h-8 w-8 text-[#028475] mr-4 flex-shrink-0" />,
      text: "Lack of a standardized, objective measure to compare potential partners."
    },
    {
      icon: <AlertTriangle className="h-8 w-8 text-[#028475] mr-4 flex-shrink-0" />,
      text: "Hesitancy to expand business to new partners due to perceived risks."
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-4 text-center">
            The Challenge of Trust & Reliability in B2B Partnerships
          </h2>
          <p className="text-xl text-gray-700 mb-10 text-center">
            Uncertainty in Choosing Your Trading Partners?
          </p>
          <p className="text-md text-gray-600 mb-12 text-center">
            In global industrial trade, especially when dealing with new or international counterparties, assessing trustworthiness and reliability can be a significant challenge, leading to:
          </p>

          <div className="grid md:grid-cols-2 gap-x-8 gap-y-6">
            {challenges.map((challenge, index) => (
              <div key={index} className="flex items-start p-4 bg-gray-50 rounded-lg border border-gray-200">
                {challenge.icon}
                <p className="text-gray-700">{challenge.text}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}