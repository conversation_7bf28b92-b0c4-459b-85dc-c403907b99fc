"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl">
          <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
            Transforming Global Industrial Trade: End-to-End Digital Solutions by StreamLnk
          </h1>
          <p className="text-xl md:text-2xl text-[#028475] mb-8">
            Navigate the complexities of sourcing, logistics, finance, and compliance with a single, integrated, AI-powered platform. StreamLnk provides the complete ecosystem your business needs to optimize operations, reduce costs, mitigate risk, and unlock new global opportunities.
          </p>
          <Button 
            className="bg-[#004235] hover:bg-[#028475] text-white px-6"
            size="lg"
            asChild
          >
            <Link href="/request-demo">
              Request a Demo
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}