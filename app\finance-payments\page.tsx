import { MainNav } from "@/components/main-nav";
import { MainFooter } from "@/components/main-footer";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { DollarSign, FileText, Shield, Wallet, CreditCard, Upload } from "lucide-react";
import PaymentSolutionCard from "@/components/finance-payments/payment-solution-card";
import IntegrationFeature from "@/components/finance-payments/integration-feature";
import HeroSection from "@/components/finance-payments/hero-section";
import CtaSection from "@/components/finance-payments/cta-section";

export default function FinancePaymentsPage() {
  return (
    <main className="flex flex-col min-h-screen">
      <MainNav />
      
      {/* Hero Section */}
      <HeroSection 
        title="StreamLnk Finance & Payment Solutions"
        description="Comprehensive financial tools designed to streamline your global trade operations"
        imageSrc="/images/finance-payments/treasury-dashboard/Dashboard showing financial overview with charts and payment status.png"
        imageAlt="StreamLnk Finance Solutions"
      />

      {/* Payment Solutions Grid */}
      <section className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-12 text-center">
            Our Finance & Payment Solutions
          </h2>
          
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {/* BNPL Card */}
            <PaymentSolutionCard 
              title="Buy Now, Pay Later"
              description="Flexible payment terms that allow you to optimize cash flow while maintaining supplier relationships."
              icon={<CreditCard className="h-8 w-8 text-[#028475]" />}
              href="/finance-payments/bnpl"
            />
            
            {/* Escrow Payment Card */}
            <PaymentSolutionCard 
              title="Escrow Payment"
              description="Secure transaction processing with milestone-based releases for complex trade agreements."
              icon={<Shield className="h-8 w-8 text-[#028475]" />}
              href="/finance-payments/escrow-payment"
            />
            
            {/* Invoicing Center Card */}
            <PaymentSolutionCard 
              title="Invoicing Center"
              description="Centralized invoice management with automated workflows and compliance support."
              icon={<FileText className="h-8 w-8 text-[#028475]" />}
              href="/finance-payments/invoicing-center"
            />
            
            {/* Treasury Dashboard Card */}
            <PaymentSolutionCard 
              title="Treasury Dashboard"
              description="Real-time visibility into your financial position with comprehensive reporting tools."
              icon={<DollarSign className="h-8 w-8 text-[#028475]" />}
              href="/finance-payments/treasury-dashboard"
            />
            
            {/* Upload Proof of Payment Card */}
            <PaymentSolutionCard 
              title="Upload Proof of Payment"
              description="Streamlined documentation process for payment verification and audit trails."
              icon={<Upload className="h-8 w-8 text-[#028475]" />}
              href="/finance-payments/upload-proof-payment"
            />
            
            {/* Wire Payment Card */}
            <PaymentSolutionCard 
              title="Wire Payment"
              description="Secure and efficient international wire transfers with competitive rates."
              icon={<Wallet className="h-8 w-8 text-[#028475]" />}
              href="/finance-payments/wire-payment"
            />
          </div>
        </div>
      </section>

      {/* Integration Section */}
      <section className="py-16 md:py-24 bg-[#F2F2F2]">
        <div className="container px-4 md:px-6">
          <div className="grid gap-12 lg:grid-cols-2 items-center">
            <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
              <Image
                src="/images/finance-payments/invoicing-center/Diagram illustrating StreamLnk Invoicing Center integration with payment workflows and Treasury Dashboard.png"
                alt="Integration diagram"
                fill
                className="object-cover"
              />
            </div>
            <div>
              <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-6">Integrated Financial Ecosystem</h2>
              <p className="text-gray-600 mb-8">
                All StreamLnk financial solutions work together seamlessly to provide a comprehensive approach to managing your global trade finances.
              </p>
              <ul className="space-y-4">
                <IntegrationFeature 
                  title="Cross-Solution Visibility"
                  description="Monitor all financial activities from a single dashboard"
                />
                <IntegrationFeature 
                  title="Automated Workflows"
                  description="Payments trigger appropriate documentation and compliance processes"
                />
                <IntegrationFeature 
                  title="Unified Reporting"
                  description="Generate comprehensive financial reports across all payment methods"
                />
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <CtaSection />

      <MainFooter />
    </main>
  );
}