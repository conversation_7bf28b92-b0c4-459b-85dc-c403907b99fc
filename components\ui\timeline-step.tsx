import { LucideIcon } from 'lucide-react';
import React from 'react';

export interface TimelineStepProps {
  number?: number;
  title: string;
  description: string;
  icon?: React.ReactNode;
}

export function TimelineStep({ number, title, description, icon }: TimelineStepProps) {
  return (
    <div className="flex flex-col items-center text-center">
      <div className="flex items-center justify-center w-12 h-12 rounded-full bg-[#004235] text-white font-bold text-xl mb-4">
        {icon || number}
      </div>
      <h3 className="text-lg font-semibold text-[#004235] mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  );
}