"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";

export default function CallToActionSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Ready to Elevate Your Industrial Procurement?
          </h2>
          <p className="text-lg text-gray-700 mb-10">
            Join leading manufacturers who are leveraging StreamLnk to build more resilient, efficient, and cost-effective supply chains.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white px-6"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                Request a Demo
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white px-6"
              size="lg"
              asChild
            >
              <Link href="/contact-sales">
                Contact Sales
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}