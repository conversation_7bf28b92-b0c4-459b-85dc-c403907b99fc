export default function UseCasesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            From Resin Sourcing to Finished Product Logistics – Intelligently Managed
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            Use Case Examples:
          </p>

          <div className="space-y-6">
            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <h3 className="font-semibold text-[#004235] text-lg mb-3">For a Plastics Converter</h3>
              <p className="text-gray-700">
                "Quickly source 50MT of a specific injection molding PP grade from three verified global suppliers, compare landed costs including sea freight and customs, and secure the best deal with BNPL terms, all within 48 hours via MyStreamLnk."
              </p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <h3 className="font-semibold text-[#004235] text-lg mb-3">For a Polymer Producer</h3>
              <p className="text-gray-700">
                "Utilize E-Stream and the Auction Engine to efficiently sell 1000MT of surplus LLDPE to a diverse pool of international buyers, optimizing price realization and reducing year-end inventory."
              </p>
            </div>

            <div className="bg-[#F2F2F2] p-6 rounded-lg">
              <h3 className="font-semibold text-[#004235] text-lg mb-3">For a Compounder</h3>
              <p className="text-gray-700">
                "Track the real-time status of multiple incoming resin shipments from different origins and coordinate just-in-time delivery to your plant using StreamLnk's integrated logistics."
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}