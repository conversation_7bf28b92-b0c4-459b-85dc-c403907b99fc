"use client"

import { Lightbulb, Users, Package } from "lucide-react"

const useCases = [
  {
    icon: <Users className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Tier 1 Supplier Sourcing Resins",
    description: "An automotive interior components supplier uses MyStreamLnk to source multiple grades of certified polypropylene from three different global E-Stream suppliers, comparing landed costs and lead times in real-time to ensure uninterrupted supply for their JIT production schedule. They utilize BNPL to manage cash flow for bulk orders.",
  },
  {
    icon: <Package className="h-10 w-10 text-[#028475] mb-4" />,
    title: "OEM Managing Component Logistics",
    description: "An EV manufacturer uses StreamLnk's integrated logistics (StreamGlobe & StreamFreight) to track critical battery components from Asia to their assembly plant in Europe, receiving automated alerts for any potential delays and ensuring customs (StreamGlobe+) are cleared proactively.",
  },
  {
    icon: <Lightbulb className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Polymer Producer Selling to Automotive Sector",
    description: "A polymer producer on E-Stream uses the platform to showcase their IATF-certified grades to a wider network of automotive buyers, leveraging StreamIndex™ to price competitively and StreamLnk's logistics to offer reliable DDP delivery options.",
  },
]

export default function UseCasesSection() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Driving Success in Automotive – Use Cases
          </h2>
          <p className="text-lg text-gray-700">
            Real-World Applications for a Faster, Leaner Automotive Supply Chain
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {useCases.map((useCase, index) => (
            <div key={index} className="bg-[#F2F2F2] p-8 rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300 flex flex-col items-center text-center">
              {useCase.icon}
              <h3 className="text-xl font-semibold text-[#004235] mb-3">{useCase.title}</h3>
              <p className="text-gray-600 leading-relaxed">{useCase.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}