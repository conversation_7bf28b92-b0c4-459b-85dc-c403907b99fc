"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Leaf, BarChart2, Users, DownloadCloud, CheckSquare, Recycle, BarChartHorizontal, ShieldCheck, PieChart, Search } from "lucide-react"; // Added more icons
import Link from "next/link";

export default function HowStreamLnkHelpsSection() {
  const features = [
    {
      icon: <Search className="text-[#028475] h-10 w-10 mr-4 flex-shrink-0" />,
      title: "Sustainable Material Sourcing (MyStreamLnk)",
      description: "Filter products by ESG attributes (e.g., \"Recycled Content %\", \"Bio-Based\", \"Low Carbon Production\", \"Ethically Sourced\") based on verified supplier data and certifications.",
      benefit: "Easily identify and procure materials that align with your sustainability goals."
    },
    {
      icon: <BarChart2 className="text-[#028475] h-10 w-10 mr-4 flex-shrink-0" />,
      title: "Estimated & Actual Carbon Footprint Tracking",
      description: "View estimated CO2e for materials (if supplier provides PCF) and logistics options during quoting. (Future) Track actual carbon footprint for completed shipments based on carrier data and refined calculations.",
      benefit: "Understand and manage the carbon intensity of your supply chain."
    },
    {
      icon: <PieChart className="text-[#028475] h-10 w-10 mr-4 flex-shrink-0" />,
      title: "ESG Impact Center (MyStreamLnk - Buyer Portal)",
      description: "A dedicated dashboard to track aggregated CO2e savings, monitor spend on sustainable materials, and view supplier ESG credentials.",
      benefit: "Provides data for corporate sustainability reporting.",
      button: {
        text: "DOWNLOAD ESG REPORT",
        href: "/my-streamlnk/esg-report" // Placeholder link
      }
    },
    {
      icon: <ShieldCheck className="text-[#028475] h-10 w-10 mr-4 flex-shrink-0" />,
      title: "Supplier ESG Profile & Verification (E-Stream)",
      description: "Suppliers can showcase their ESG certifications and self-declare adherence to sustainability policies. StreamLnk verifies key certifications.",
      benefit: "Provides buyers with greater transparency into supplier ESG practices."
    },
    {
      icon: <Recycle className="text-[#028475] h-10 w-10 mr-4 flex-shrink-0" />,
      title: "Support for Circular Economy Materials",
      description: "Dedicated features for sourcing and trading recycled polymers, metals, and other secondary raw materials.",
      benefit: "Facilitates participation in the circular economy."
    },
    {
      icon: <BarChartHorizontal className="text-[#028475] h-10 w-10 mr-4 flex-shrink-0" />,
      title: "Data for Scope 3 Emissions Reporting (Future Enhancement)",
      description: "Provide data points on purchased goods and transportation that can feed into a company's Scope 3 (indirect) emissions calculations.",
      benefit: "Simplifies complex emissions reporting."
    },
    {
      icon: <CheckSquare className="text-[#028475] h-10 w-10 mr-4 flex-shrink-0" />,
      title: "iScore™ ESG Component (Future Enhancement)",
      description: "Integration of key verifiable ESG metrics into the overall iScore™ for suppliers and logistics partners.",
      benefit: "Holistic partner assessment including sustainability."
    }
  ];

  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            How StreamLnk Helps You Track, Manage, and Report Your ESG Performance
          </h2>
          <p className="text-xl text-gray-700 mb-12 text-center">
            StreamLnk's ESG Tools – Enabling Responsible Sourcing & Reporting
          </p>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamLnk integrates ESG considerations and reporting capabilities throughout its platform:
          </p>

          <div className="space-y-10">
            {features.map((feature, index) => (
              <div key={index} className="p-6 bg-white rounded-lg shadow-md">
                <div className="flex items-start mb-3">
                  {feature.icon}
                  <div>
                    <h3 className="text-2xl font-semibold text-[#004235]">{feature.title}</h3>
                  </div>
                </div>
                <p className="text-gray-700 mb-2 pl-14"><strong className="text-gray-800">Functionality:</strong> {feature.description}</p>
                <p className="text-gray-700 pl-14"><strong className="text-gray-800">Benefit:</strong> {feature.benefit}</p>
                {feature.button && (
                  <div className="mt-4 pl-14">
                    <Button
                      variant="outline"
                      className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
                      size="sm"
                      asChild
                    >
                      <Link href={feature.button.href}>
                        {feature.button.text}
                        <DownloadCloud className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}