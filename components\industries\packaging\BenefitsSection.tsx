"use client";

import { ArrowR<PERSON>, Recycle, ShieldCheck, Users, TrendingUp, Truck } from "lucide-react";

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Innovate Faster, Source Smarter, Package Sustainably
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            Benefits for the Packaging Industry
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[ 
              { title: "Diversified & Reliable Material Sourcing", description: "Access a global network of verified suppliers for all your packaging material needs.", icon: <Users className="h-8 w-8 text-[#028475] mb-3" /> },
              { title: "Cost Optimization", description: "Achieve better pricing through transparent quoting and auction opportunities.", icon: <TrendingUp className="h-8 w-8 text-[#028475] mb-3" /> },
              { title: "Streamlined Supply Chain", description: "Integrate sourcing, logistics, and compliance for greater efficiency.", icon: <Truck className="h-8 w-8 text-[#028475] mb-3" /> },
              { title: "Meet Sustainability Targets", description: "Easily find and source recycled, bio-based, and other sustainable packaging materials.", icon: <Recycle className="h-8 w-8 text-[#028475] mb-3" /> },
              { title: "Ensure Quality & Compliance", description: "Source materials with verified certifications and manage documentation effectively.", icon: <ShieldCheck className="h-8 w-8 text-[#028475] mb-3" /> },
              { title: "Faster Time-to-Market", description: "Optimize your material procurement and logistics to support agile production.", icon: <ArrowRight className="h-8 w-8 text-[#028475] mb-3" /> }
            ].map((benefit, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-sm text-center">
                {benefit.icon}
                <h3 className="font-semibold text-[#004235] text-lg mb-2">{benefit.title}</h3>
                <p className="text-gray-600">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}