import { FileText, Bar<PERSON>hart3, Clock, CheckCircle2, Globe, FileCheck } from "lucide-react"
import FeatureCard from "@/components/finance-payments/invoicing-center/feature-card"

export default function KeyFeaturesSection() {
  return (
    <section className="py-16 md:py-24 bg-[#f3f4f6]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Key Features</h2>
          <p className="text-gray-600 max-w-3xl">
            Our Invoicing Center offers powerful tools to manage your financial documents and processes.
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          <FeatureCard
            icon={<FileText className="h-8 w-8 text-[#028475]" />}
            title="Create & Send Invoices"
            features={[
              "Generate invoices manually or auto-generate from orders and shipments",
              "Customize with tax IDs, PO numbers, and document attachments",
              "Send invoices directly through the platform with tracking",
            ]}
          />

          <FeatureCard
            icon={<BarChart3 className="h-8 w-8 text-[#028475]" />}
            title="Accounts Receivable (AR) Dashboard"
            features={[
              "Monitor outstanding invoices, aging buckets, and customer payment history",
              "Filter by region, partner type, currency, and due date",
              "Generate AR reports and forecasts",
            ]}
          />

          <FeatureCard
            icon={<Clock className="h-8 w-8 text-[#028475]" />}
            title="Accounts Payable (AP) Dashboard"
            features={[
              "View all open payables, payment due dates, and related documentation",
              "Get alerts on upcoming deadlines and late penalties",
              "Schedule and track payments",
            ]}
          />

          <FeatureCard
            icon={<CheckCircle2 className="h-8 w-8 text-[#028475]" />}
            title="Invoice Matching & Validation"
            features={[
              "Auto-match payments and proof of payment uploads to open invoices",
              "Flag discrepancies for review or escalation",
              "Streamline reconciliation processes",
            ]}
          />

          <FeatureCard
            icon={<Globe className="h-8 w-8 text-[#028475]" />}
            title="Multi-Currency Support"
            features={[
              "Manage invoices in over 25 currencies with real-time conversion reference",
              "Handle multi-currency reconciliation",
              "Track exchange rate fluctuations",
            ]}
          />

          <FeatureCard
            icon={<FileCheck className="h-8 w-8 text-[#028475]" />}
            title="Document Management"
            features={[
              "Centralized storage for all invoice-related documents",
              "Searchable document repository",
              "Automated document organization and tagging",
            ]}
          />
        </div>
      </div>
    </section>
  )
}