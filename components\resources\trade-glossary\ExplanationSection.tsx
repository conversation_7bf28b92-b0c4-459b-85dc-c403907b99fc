"use client";

import { Lightbulb, BookO<PERSON>, Users, CheckCircle } from 'lucide-react';

export default function ExplanationSection() {
  return (
    <section className="py-16 md:py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-[#004235] mb-4">
            Understanding the Language of International Trade
          </h2>
          <p className="text-xl text-[#028475] font-semibold">
            Why a Trade Glossary? Simplifying Complexity
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <p className="text-lg text-gray-700 mb-8 text-center">
            The world of global industrial trade is filled with specialized terminology, acronyms, and concepts that can be confusing. StreamLnk's Trade Glossary is designed to:
          </p>

          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-gray-50 p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center text-[#004235] mb-3">
                <Lightbulb className="h-7 w-7 mr-3" />
                <h3 className="text-xl font-semibold">Provide Clarity</h3>
              </div>
              <p className="text-gray-600">
                Offer clear, concise definitions for common and complex trade terms, demystifying industry jargon.
              </p>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center text-[#004235] mb-3">
                <BookOpen className="h-7 w-7 mr-3" />
                <h3 className="text-xl font-semibold">Enhance Understanding</h3>
              </div>
              <p className="text-gray-600">
                Help users better understand processes and documentation within the StreamLnk platform and broader trade ecosystem.
              </p>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center text-[#004235] mb-3">
                <Users className="h-7 w-7 mr-3" />
                <h3 className="text-xl font-semibold">Facilitate Communication</h3>
              </div>
              <p className="text-gray-600">
                Enable smoother communication between buyers, suppliers, logistics providers, and financial institutions.
              </p>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center text-[#004235] mb-3">
                <CheckCircle className="h-7 w-7 mr-3" />
                <h3 className="text-xl font-semibold">Serve as a Resource</h3>
              </div>
              <p className="text-gray-600">
                Act as a valuable learning tool for professionals new to international trade or specific industry sectors.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}