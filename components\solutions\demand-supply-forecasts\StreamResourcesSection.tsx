"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>t, Webhook } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"

export default function StreamResourcesSection() {
  const features = [
    {
      title: "Interactive Dashboards",
      icon: <BarChart className="h-6 w-6 text-[#028475]" />,
      description: "Visualize demand heatmaps, supply trend charts, and price forecasts."
    },
    {
      title: "Customizable Alerts",
      icon: <Bell className="h-6 w-6 text-[#028475]" />,
      description: "Set up notifications for specific forecast triggers (e.g., \"Alert me if forecasted demand for HDPE in Europe exceeds X%\")."
    },
    {
      title: "Detailed Reports",
      icon: <FileText className="h-6 w-6 text-[#028475]" />,
      description: "Access regular market outlook reports incorporating these forecasts."
    },
    {
      title: "API Access (Enterprise Tier)",
      icon: <Webhook className="h-6 w-6 text-[#028475]" />,
      description: "Integrate forecast data directly into your internal planning systems."
    }
  ]

  return (
    <section className="py-16 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-6 text-center">
            Intelligence at Your Fingertips via StreamResources+
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamLnk's demand and supply forecasts are premium features accessible through our StreamResources+ data and analytics portal:
          </p>
          
          <div className="grid md:grid-cols-2 gap-6 mb-10">
            {features.map((feature, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-sm flex items-start">
                <div className="mr-4 mt-1">{feature.icon}</div>
                <div>
                  <h3 className="text-lg font-semibold text-[#004235] mb-2">{feature.title}</h3>
                  <p className="text-gray-700">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-8">
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white"
              size="lg"
              asChild
            >
              <Link href="/streamresources-subscription">
                EXPLORE STREAMRESOURCES+ SUBSCRIPTION TIERS
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}