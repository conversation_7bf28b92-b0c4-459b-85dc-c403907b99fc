"use client";

import { Globe, Search, HardHat, Truck, FileText, BarChart3 } from "lucide-react"; // Adjusted icons for construction

export default function SolutionsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-4 text-center">
            Your Digital Foundation for Efficient Project Material Management
          </h2>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamLnk's Tailored Solutions for the Construction Materials Sector
          </p>
          <p className="text-lg text-gray-700 mb-10 text-center">
            StreamLnk provides a comprehensive platform to streamline the procurement, logistics, and management of construction materials for contractors, developers, and suppliers:
          </p>

          <div className="space-y-8">
            {/* E-Stream for Construction Material Suppliers */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Globe className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">E-Stream for Construction Material Suppliers</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>List a wide range of building materials: cement, aggregates, ready-mix concrete (regional), steel (rebar, structural), lumber, insulation, fixtures, specialized components, and sustainable/green building materials.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Provide detailed specifications, certifications (e.g., ISO, LEED compliance data), and regional availability.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Utilize StreamIndex™ for pricing insights on key construction commodities.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Offer project-based or bulk volume pricing.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* MyStreamLnk for Contractors, Developers & Builders */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Search className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">MyStreamLnk for Contractors, Developers & Builders (Buyers)</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Source diverse construction materials from a network of local, regional, and global verified suppliers.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Advanced search by material type, specification, required certifications, delivery location, and supplier iScore™.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Digital RFQ process for bulk materials or project-specific requirements, with transparent landed cost visibility.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Schedule and track deliveries to multiple project sites with real-time updates.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Project-Based Logistics Coordination */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <Truck className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Project-Based Logistics Coordination (StreamFreight, StreamGlobe+, StreamPak)</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Coordinate multi-modal transport for bulk materials (e.g., rail for aggregates, trucks for cement) and specialized equipment.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Manage Just-in-Time (JIT) deliveries to construction sites, with features for scheduling specific delivery windows.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Facilitate on-site storage solutions or coordination with nearby StreamPak warehousing partners if needed.</span>
                    </li>
                     <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Efficient customs clearance (StreamGlobe+) for imported materials or equipment.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Quality & Compliance Document Management */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <FileText className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">Quality & Compliance Document Management</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Centralized access to material certifications, test reports, and compliance documentation required for project audits and quality assurance.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* StreamResources+ for Construction Market Intelligence */}
            <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-[#004235]/10 p-4 rounded-full flex items-center justify-center">
                  <BarChart3 className="h-8 w-8 text-[#028475]" />
                </div>
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-[#004235] mb-3">StreamResources+ for Construction Market Intelligence</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Insights into pricing trends for key construction materials by region.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Analysis of supply availability and potential logistics bottlenecks impacting project timelines.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#028475] mr-2">•</span>
                      <span>Forecasting for material demand based on construction activity indicators.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}