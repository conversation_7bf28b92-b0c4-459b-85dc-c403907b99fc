"use client"

import { <PERSON>, FileTex<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Sign, Eye, Users } from "lucide-react"

export default function ChallengesSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 flex items-center">
            <span className="w-10 h-1 bg-[#028475] mr-3"></span>
            Is Your Financial Workflow Slowing Down Your Business?
          </h2>
          <p className="text-lg text-gray-700 mb-10">
            Managing receivables and payables for numerous high-value industrial transactions, especially across different customers and suppliers, can be a major operational drag, leading to:
          </p>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Challenge 1 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <Clock className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <p className="text-gray-700">
                  <span className="font-semibold">Delayed Payments:</span> High Days Sales Outstanding (DSO) impacting cash flow.
                </p>
              </div>
            </div>

            {/* Challenge 2 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <FileText className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <p className="text-gray-700">
                  <span className="font-semibold">Manual Invoice Tracking:</span> Time-consuming efforts to monitor due dates and follow up on unpaid invoices.
                </p>
              </div>
            </div>

            {/* Challenge 3 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <AlertTriangle className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <p className="text-gray-700">
                  <span className="font-semibold">Dispute Resolution Complexities:</span> Difficulty in reconciling discrepancies and resolving payment disputes efficiently.
                </p>
              </div>
            </div>

            {/* Challenge 4 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <Eye className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <p className="text-gray-700">
                  <span className="font-semibold">Lack of Visibility:</span> Poor insight into overall AR exposure and AP obligations.
                </p>
              </div>
            </div>

            {/* Challenge 5 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <DollarSign className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <p className="text-gray-700">
                  <span className="font-semibold">High Administrative Costs:</span> Significant manual effort in invoice processing, payment reminders, and reconciliation.
                </p>
              </div>
            </div>

            {/* Challenge 6 */}
            <div className="flex items-start">
              <div className="mr-4 mt-1">
                <Users className="h-6 w-6 text-[#028475]" />
              </div>
              <div>
                <p className="text-gray-700">
                  <span className="font-semibold">Risk of Bad Debt:</span> Increased exposure to defaults without proactive management.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}