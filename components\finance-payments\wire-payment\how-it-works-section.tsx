import { StandardizedTimeline } from "@/components/ui/standardized-timeline";
import { DollarSign, Banknote, Send, CheckCircle, Handshake } from "lucide-react";

export default function HowItWorksSection() {
  const steps = [
    {
      number: 1,
      icon: <DollarSign className="h-8 w-8 text-white" />,
      title: "Select Your Payment Currency",
      description: "Choose your preferred settlement currency during RFQ acceptance or at checkout."
    },
    {
      number: 2,
      icon: <Banknote className="h-8 w-8 text-white" />,
      title: "Receive Payment Instructions",
      description: "Instantly receive bank account details and payment ID reference."
    },
    {
      number: 3,
      icon: <Send className="h-8 w-8 text-white" />,
      title: "Transfer Funds",
      description: "Submit the wire through your bank or payment provider."
    },
    {
      number: 4,
      icon: <CheckCircle className="h-8 w-8 text-white" />,
      title: "Auto-Reconciliation",
      description: "StreamLnk's platform automatically matches payments to open invoices."
    },
    {
      number: 5,
      icon: <Handshake className="h-8 w-8 text-white" />,
      title: "Confirmation & Clearance",
      description: "Both parties receive confirmation, and shipments are cleared for dispatch."
    }
  ];

  return (
    <StandardizedTimeline
      title="How It Works"
      description="Our streamlined process makes international payments simple and efficient."
      steps={steps}
      bgColor="bg-[#f3f4f6]"
    />
  );
}