"use client";

import { useState } from "react";
import Link from "next/link";
import * as Icons from "lucide-react";
import { Button } from "../ui/button";
import { MegaMenuNavItem, NavItemBase, MegaMenuSectionData, MegaMenuBottomGridSection } from "./types";

interface MobileNavMenuProps {
  navigationItems: MegaMenuNavItem[];
  customerPortalLinks: NavItemBase[];
  setMobileMenuOpen: (open: boolean) => void;
}

export function MobileNavMenu({ 
  navigationItems, 
  customerPortalLinks, 
  setMobileMenuOpen 
}: MobileNavMenuProps) {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});

  // Toggle section expansion
  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  return (
    // Use fixed inset-0 with padding top matching header height when sticky
    <div className="md:hidden bg-background shadow-lg fixed inset-0 z-40 overflow-y-auto pt-20 pb-6 px-4">
      <div className="space-y-6 divide-y divide-border"> {/* Use theme divider */}
        {/* Location Selection */}
        <div className="pt-4 first:pt-0">
          <h3 className="text-base font-semibold text-foreground mb-2">Location</h3>
          <Link
            href="/location"
            className="flex items-center gap-2 text-muted-foreground hover:text-[#004235] py-1 transition-colors"
            onClick={() => setMobileMenuOpen(false)}
          >
            <Icons.Globe className="h-5 w-5 text-[#004235]" />
            <span>United States of America</span>
          </Link>
        </div>

        {navigationItems.map(group => (
          <div key={group.id} className="pt-4 first:pt-0">
            <h3 className="text-base font-semibold text-foreground mb-2">{group.trigger}</h3>
            
            {group.content.type === 'custom' && (
              <div className="space-y-4">
                {/* Quick Links Section */}
                <div>
                  <button 
                    onClick={() => toggleSection(`${group.id}-sidebar`)}
                    className="flex items-center justify-between w-full text-sm font-medium text-foreground mb-2"
                  >
                    <span>Quick Links</span>
                    <Icons.ChevronDown 
                      className={`h-4 w-4 text-muted-foreground transition-transform ${expandedSections[`${group.id}-sidebar`] ? 'rotate-180' : ''}`} 
                    />
                  </button>
                  
                  {expandedSections[`${group.id}-sidebar`] && (
                    <div className="grid grid-cols-2 gap-x-4 gap-y-2 pl-2">
                      {group.content.sidebarLinks.map(item => (
                        <Link
                          key={item.href + (item.label || '')} 
                          href={item.href}
                          className="flex items-center gap-1 text-muted-foreground hover:text-[#004235] py-1 transition-colors text-sm"
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          {item.icon && <span className="text-[#004235] flex-shrink-0">{item.icon}</span>}
                          <span className="truncate">{item.label}</span>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
                
                {/* Main Sections with Columns */}
                {group.content.mainColumns.map((column, idx) => (
                  <div key={`${group.id}-col-${idx}`}>
                    <button 
                      onClick={() => toggleSection(`${group.id}-col-${idx}`)}
                      className="flex items-center justify-between w-full text-sm font-medium text-foreground mb-2"
                    >
                      <span>{column.title}</span>
                      <Icons.ChevronDown 
                        className={`h-4 w-4 text-muted-foreground transition-transform ${expandedSections[`${group.id}-col-${idx}`] ? 'rotate-180' : ''}`} 
                      />
                    </button>
                    
                    {expandedSections[`${group.id}-col-${idx}`] && (
                      <div className="grid grid-cols-2 gap-x-4 gap-y-2 pl-2">
                        {column.items.map(item => (
                          <Link
                            key={item.href + (item.label || '')} 
                            href={item.href}
                            className="flex items-center gap-1 text-muted-foreground hover:text-[#004235] py-1 transition-colors text-sm"
                            onClick={() => setMobileMenuOpen(false)}
                          >
                            {item.icon && <span className="text-[#004235] flex-shrink-0">{item.icon}</span>}
                            <span className="truncate">{item.label}</span>
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
                
                {/* Bottom Grid Sections */}
                {group.content.bottomGrid && (
                  <div>
                    <button 
                      onClick={() => toggleSection(`${group.id}-bottom`)}
                      className="flex items-center justify-between w-full text-sm font-medium text-foreground mb-2"
                    >
                      <span>{group.content.bottomGrid.title}</span>
                      <Icons.ChevronDown 
                        className={`h-4 w-4 text-muted-foreground transition-transform ${expandedSections[`${group.id}-bottom`] ? 'rotate-180' : ''}`} 
                      />
                    </button>
                    
                    {expandedSections[`${group.id}-bottom`] && (
                      <div className="space-y-3 pl-2">
                        {group.content.bottomGrid.sections.map((section, secIdx) => (
                          <div key={`${group.id}-bottom-${secIdx}`}>
                            <h4 className="text-xs font-medium text-muted-foreground mb-1">{section.title}</h4>
                            <div className="grid grid-cols-2 gap-x-4 gap-y-1">
                              {section.links.map(link => (
                                <Link
                                  key={link.href + link.label} 
                                  href={link.href}
                                  className="text-sm text-muted-foreground hover:text-[#004235] py-1 transition-colors truncate"
                                  onClick={() => setMobileMenuOpen(false)}
                                >
                                  {link.label}
                                </Link>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        ))}

        {/* Simplified Customer Portal Links for Mobile */}
        <div className="pt-4">
          <h3 className="text-base font-semibold text-foreground mb-2">Our Portals</h3>
          <div className="grid grid-cols-2 gap-x-4 gap-y-2">
            {customerPortalLinks.map(link => (
              <Link
                key={link.label}
                href={link.href}
                className="flex items-center gap-2 text-muted-foreground hover:text-[#004235] py-2 transition-colors break-words"
                onClick={() => setMobileMenuOpen(false)}
              >
                {link.icon && <span className="text-[#004235] flex-shrink-0">{link.icon}</span>}
                <span className="text-sm truncate">{link.label}</span>
              </Link>
            ))}
          </div>
        </div>

        {/* Login/Signup Buttons */}
        <div className="pt-6">
          <div className="grid grid-cols-2 gap-3">
            <Button variant="outline" asChild>
              <Link href="/login" onClick={() => setMobileMenuOpen(false)}>Login</Link>
            </Button>
            <Button variant="default" asChild className="bg-[#004235] hover:bg-[#004235]/90 text-primary-foreground">
              <Link href="/signup" onClick={() => setMobileMenuOpen(false)}>Sign Up</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}