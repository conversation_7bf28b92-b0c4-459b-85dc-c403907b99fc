import { Calendar, CheckCircle2, DollarSign, <PERSON><PERSON><PERSON> } from "lucide-react";
import BnplStep from "@/components/finance-payments/bnpl/bnpl-step";

export default function HowBnplWorksSection() {
  return (
    <section className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">How BNPL Works on StreamLnk</h2>
          <p className="text-gray-600 max-w-3xl">
            Our Buy Now Pay Later solution streamlines procurement while maintaining healthy cash flow for your
            business.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <BnplStep
            icon={<Calendar className="h-8 w-8 text-[#028475]" />}
            title="Request BNPL Terms at Checkout"
            description="Eligible buyers can opt to pay 30, 45, or 60 days after delivery."
          />
          <BnplStep
            icon={<LineChart className="h-8 w-8 text-[#028475]" />}
            title="Credit Assessment in Real-Time"
            description="Our platform performs automatic eligibility checks using trade history, StreamIndex score, and external credit data."
          />
          <BnplStep
            icon={<CheckCircle2 className="h-8 w-8 text-[#028475]" />}
            title="Instant Supplier Notification"
            description="Once approved, suppliers receive payment confirmation and dispatch instructions as if payment has already cleared."
          />
          <BnplStep
            icon={<DollarSign className="h-8 w-8 text-[#028475]" />}
            title="StreamLnk Settles the Invoice"
            description="The supplier is paid upfront; the buyer repays StreamLnk on the agreed timeline."
          />
        </div>
      </div>
    </section>
  );
}