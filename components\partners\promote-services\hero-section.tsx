import Image from "next/image"
import { Button } from "@/components/ui/button"

export function HeroSection() {
  return (
    <section className="relative bg-[#F2F2F2] py-16 md:py-24 overflow-hidden">
      <div className="absolute inset-0 z-0">
        <Image
          src="/images/partner/promote-services/Global Marketplace.webp"
          alt="Global industrial marketplace"
          width={1920}
          height={1080}
          className="w-full h-full object-cover opacity-15"
          priority
        />
      </div>
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <div className="w-20 h-1 bg-[#028475] mx-auto mb-6"></div>
          <h1 className="text-3xl md:text-5xl font-bold text-[#004235] mb-6 leading-tight">
            Amplify Your Reach: Showcase Your Industrial Services to a Global Marketplace
          </h1>
          <p className="text-lg md:text-xl text-gray-700 mb-8 leading-relaxed">
            Tap into StreamLnk's worldwide network of verified buyers, suppliers, and logistics stakeholders. Position
            your freight, customs, warehousing, compliance, or other essential industrial services directly within
            active trade workflows and win business faster.
          </p>
          <Button className="bg-[#004235] hover:bg-[#028475] text-white px-8 py-6 text-lg rounded-md">
            Apply to Promote Your Services
          </Button>
        </div>
      </div>
      <div className="absolute bottom-0 left-0 w-full h-16 bg-gradient-to-t from-white to-transparent"></div>
    </section>
  )
}
