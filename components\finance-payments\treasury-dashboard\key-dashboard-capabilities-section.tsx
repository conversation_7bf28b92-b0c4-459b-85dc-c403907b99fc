import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "lucide-react"
import DashboardFeature from "@/components/finance-payments/treasury-dashboard/dashboard-feature"

export default function KeyDashboardCapabilitiesSection() {
  return (
    <section className="py-16 md:py-24 bg-[#f3f4f6]">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-4">Key Dashboard Capabilities</h2>
          <p className="text-gray-600 max-w-3xl">
            Our Treasury & FX Dashboard offers powerful tools to manage your global financial operations.
          </p>
        </div>

        <div className="grid gap-8 md:gap-12">
          {/* Live FX Rate Tracking */}
          <DashboardFeature
            icon={<LineChart className="h-8 w-8 text-[#028475]" />}
            title="Live FX Rate Tracking"
            description="Monitor exchange rates in real-time across all major currencies."
            features={[
              "Monitor exchange rates in real-time across all major currencies",
              "Set FX alerts for favorable conversion thresholds",
              "Historical rate trends and analysis",
            ]}
            imageSrc="/images/finance-payments/Graphic of real-time FX rate tracking dashboard displaying multiple major currencies.png"
            imageAlt="Real-time FX rate tracking dashboard displaying multiple major currencies"
          />

          {/* Payment Activity Overview */}
          <DashboardFeature
            icon={<BarChart3 className="h-8 w-8 text-[#028475]" />}
            title="Payment Activity Overview"
            description="Comprehensive view of all payment activities across your organization."
            features={[
              "View incoming and outgoing payments by status, partner, and country",
              "Filter transactions by currency, method (wire, BNPL, escrow), or business unit",
              "Real-time payment status updates and notifications",
            ]}
            imageSrc="/images/finance-payments/Graphic of comprehensive payment activity dashboard showing status and filters.png"
            imageAlt="Comprehensive payment activity dashboard showing status and filters"
            imageRight
          />

          {/* Liquidity & Exposure Analysis */}
          <DashboardFeature
            icon={<Wallet className="h-8 w-8 text-[#028475]" />}
            title="Liquidity & Exposure Analysis"
            description="Gain insights into your financial positions across currencies and regions."
            features={[
              "See available funds by currency and country",
              "Detect overexposed or underfunded regions instantly",
              "Forecast cash flow based on pending transactions",
            ]}
            imageSrc="/images/finance-payments/Graphic of liquidity and exposure analysis dashboard displaying funds by currency and region.png"
            imageAlt="Liquidity and exposure analysis dashboard displaying funds by currency and region"
          />

          {/* Reconciliation & Document Matching */}
          <DashboardFeature
            icon={<FileCheck className="h-8 w-8 text-[#028475]" />}
            title="Reconciliation & Document Matching"
            description="Streamline your financial reconciliation processes."
            features={[
              "Match payments with invoices, orders, and proof of payment documents",
              "Export transaction summaries to your ERP or accounting system",
              "Automated reconciliation suggestions and anomaly detection",
            ]}
            imageSrc="/images/finance-payments/Graphic of reconciliation dashboard matching payments with invoices and documents.png"
            imageAlt="Reconciliation dashboard matching payments with invoices and documents"
            imageRight
          />
        </div>
      </div>
    </section>
  )
}