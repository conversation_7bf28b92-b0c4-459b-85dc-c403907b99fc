import { Building2, Users, HeadphonesIcon, DollarSign, Globe } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

export function WhatIsSection() {
  const features = [
    {
      icon: <Building2 className="h-10 w-10 text-[#028475]" />,
      title: "Onboard Your Existing Portfolio",
      description: "Seamlessly bring your network of industrial buyers onto the StreamLnk platform.",
    },
    {
      icon: <Users className="h-10 w-10 text-[#028475]" />,
      title: "Manage Client Accounts Digitally",
      description:
        "Oversee their RFQs, orders, shipment tracking, and documentation through a sophisticated yet user-friendly interface.",
    },
    {
      icon: <HeadphonesIcon className="h-10 w-10 text-[#028475]" />,
      title: "Serve as the Primary Contact",
      description:
        "Act as their trusted advisor and main point of contact, backed by StreamLnk's comprehensive backend infrastructure and support.",
    },
    {
      icon: <DollarSign className="h-10 w-10 text-[#028475]" />,
      title: "Earn Attractive Commissions",
      description: "Build a recurring revenue stream based on every successful transaction your clients make.",
    },
    {
      icon: <Globe className="h-10 w-10 text-[#028475]" />,
      title: "Grow Your Territory & Influence",
      description:
        "Expand your market reach and establish a lasting business footprint in the evolving world of digital trade.",
    },
  ]

  return (
    <section id="learn-more" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">What Is MyStreamLnk+?</h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            MyStreamLnk+ is an exclusive, dedicated portal meticulously designed for independent sales professionals,
            experienced territory managers, and official country/regional distributors in the industrial sector. This
            platform empowers you to:
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="border border-gray-200 hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-[#004235] mb-2">{feature.title}</h3>
                <p className="text-gray-700">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-12 text-center">
          <p className="text-lg text-gray-700 max-w-3xl mx-auto italic">
            Think of MyStreamLnk+ as your advanced toolkit for global trade representation—built for serious,
            results-driven professionals.
          </p>
        </div>
      </div>
    </section>
  )
}
