import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function HeroSection() {
  return (
    <section className="bg-[#F2F2F2] py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-[#004235] mb-6">
              Connect Your Ecosystem: Seamless Integrations with StreamLnk
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Maximize efficiency and data consistency by integrating StreamLnk with your core business systems. Our platform is designed for seamless connectivity, enabling automated data flows and unified workflows.
            </p>
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white px-6"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                EXPLORE INTEGRATIONS – EXPERT
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/placeholder-image.svg" // Placeholder image
              alt="Seamless Integrations"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
}