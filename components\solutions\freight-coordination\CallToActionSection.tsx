"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export default function CallToActionSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-3xl font-bold text-[#004235] mb-6">Ready to Simplify Your Freight Process?</h2>
        <p className="text-lg text-gray-700 mb-8">
          Experience the efficiency and control of StreamLnk's Freight Coordination solution.
        </p>
        <Button
          className="bg-[#004235] hover:bg-[#028475] text-white px-8"
          size="lg"
          asChild
        >
          <Link href="/request-demo">
            REQUEST A DEMO
            <ArrowRight className="ml-2 h-5 w-5" />
          </Link>
        </Button>
      </div>
    </section>
  );
}