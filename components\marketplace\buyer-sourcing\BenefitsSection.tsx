"use client";

import { CheckCircle } from "lucide-react";

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Optimize Costs, Reduce Risk, Enhance Efficiency
          </h2>
          <p className="text-xl font-semibold text-[#004235] mb-8 text-center">
            Benefits for Buyers & Procurement Teams
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start">
                <CheckCircle className="h-6 w-6 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-lg font-semibold text-[#004235] mb-2">Significant Time Savings</h3>
                  <p className="text-gray-700">Drastically reduce the time spent on supplier discovery, RFQ processes, and logistics coordination.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start">
                <CheckCircle className="h-6 w-6 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-lg font-semibold text-[#004235] mb-2">Cost Optimization</h3>
                  <p className="text-gray-700">Access competitive global pricing, transparent landed costs, and opportunities through auctions.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start">
                <CheckCircle className="h-6 w-6 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-lg font-semibold text-[#004235] mb-2">Expanded Supplier Choice</h3>
                  <p className="text-gray-700">Connect with a wider range of verified international and local suppliers.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start">
                <CheckCircle className="h-6 w-6 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-lg font-semibold text-[#004235] mb-2">Reduced Sourcing Risk</h3>
                  <p className="text-gray-700">Transact with vetted suppliers and utilize secure payment mechanisms like Escrow.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start">
                <CheckCircle className="h-6 w-6 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-lg font-semibold text-[#004235] mb-2">Enhanced Visibility & Control</h3>
                  <p className="text-gray-700">Gain end-to-end transparency over your orders and shipments.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start">
                <CheckCircle className="h-6 w-6 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-lg font-semibold text-[#004235] mb-2">Streamlined Operations</h3>
                  <p className="text-gray-700">Centralize all your industrial procurement activities on one platform.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start">
                <CheckCircle className="h-6 w-6 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-lg font-semibold text-[#004235] mb-2">Achieve Sustainability Targets</h3>
                  <p className="text-gray-700">Easily find and source materials that meet your ESG goals.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start">
                <CheckCircle className="h-6 w-6 text-[#028475] mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-lg font-semibold text-[#004235] mb-2">Data-Driven Decisions</h3>
                  <p className="text-gray-700">Leverage StreamIndex™ and iScore™ insights for more strategic procurement.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}