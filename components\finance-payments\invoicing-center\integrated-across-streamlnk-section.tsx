import Image from "next/image"
import { Link, Building2, DollarSign } from "lucide-react"
import IntegrationFeature from "@/components/finance-payments/invoicing-center/integration-feature"

export default function IntegratedAcrossStreamLnkSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container px-4 md:px-6">
        <div className="grid gap-12 lg:grid-cols-2 items-center">
          <div>
            <h2 className="text-3xl font-bold tracking-tight text-[#004235] mb-6">Integrated Across StreamLnk</h2>
            <p className="text-gray-600 mb-8">
              The Invoicing Center is seamlessly integrated with StreamLnk's comprehensive ecosystem.
            </p>

            <div className="space-y-4">
              <IntegrationFeature
                icon={<Link className="h-6 w-6 text-[#028475]" />}
                title="Payment Workflow Integration"
                description="Linked to payment workflows (wire, escrow, BNPL)"
              />
              <IntegrationFeature
                icon={<Building2 className="h-6 w-6 text-[#028475]" />}
                title="Portal Embedding"
                description="Embedded in MyStreamLnk, E-Stream, StreamFreight, and StreamPak portals"
              />
              <IntegrationFeature
                icon={<DollarSign className="h-6 w-6 text-[#028475]" />}
                title="Treasury Dashboard Sync"
                description="Syncs with Treasury Dashboard for payment visibility"
              />
            </div>
          </div>
          <div className="relative w-full h-[300px] md:h-[400px] rounded-lg overflow-hidden shadow-xl">
            <Image
              src="/images/finance-payments/Diagram illustrating StreamLnk Invoicing Center integration with payment workflows and Treasury Dashboard.png"
              alt="Integration diagram"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  )
}