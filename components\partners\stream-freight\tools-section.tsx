import type React from "react"
import { <PERSON><PERSON>l, Bar<PERSON>hart2, Route, Clock, DollarSign, Award } from "lucide-react"

interface Tool {
  icon: React.ReactNode
  name: string
  description: string
}

export function ToolsSection() {
  const tools: Tool[] = [
    {
      icon: <Gavel className="h-10 w-10 text-white" />,
      name: "StreamFreight Bid Engine",
      description: "For real-time bidding on available shipments.",
    },
    {
      icon: <BarChart2 className="h-10 w-10 text-white" />,
      name: "AR/AP Invoice & Treasury Dashboard",
      description: "For managing invoices and tracking payments.",
    },
    {
      icon: <Route className="h-10 w-10 text-white" />,
      name: "Smart Route Builder",
      description: "Suggestions for optimal pickup and drop-off paths.",
    },
    {
      icon: <Clock className="h-10 w-10 text-white" />,
      name: "Compliance Expiry Tracker",
      description: "To monitor and manage your critical document validity.",
    },
    {
      icon: <DollarSign className="h-10 w-10 text-white" />,
      name: "Payment Scheduling & History Tool",
      description: "For clear visibility into your earnings.",
    },
    {
      icon: <Award className="h-10 w-10 text-white" />,
      name: "Performance Scorecard",
      description: "To track your operational metrics and service quality.",
    },
  ]

  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-12 text-center">Tools You'll Have Access To</h2>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {tools.map((tool, index) => (
            <div key={index} className="flex flex-col items-center">
              <div className="w-20 h-20 rounded-full bg-[#004235] flex items-center justify-center mb-4">
                {tool.icon}
              </div>
              <h3 className="text-xl font-semibold text-[#004235] mb-2 text-center">{tool.name}</h3>
              <p className="text-gray-700 text-center">{tool.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
