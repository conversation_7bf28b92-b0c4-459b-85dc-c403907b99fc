"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Send } from 'lucide-react';
import Link from 'next/link';

export default function SuggestTermSection() {
  return (
    <section className="py-16 md:py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center bg-gray-50 p-8 md:p-12 rounded-lg shadow-lg border border-gray-200">
          <h2 className="text-3xl font-bold text-[#004235] mb-4">
            Help Us Grow Our Glossary
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            Can't find a term? Is there an industry term or StreamLnk concept you're looking for that isn't listed? Let us know, and we'll consider adding it to help the entire community.
          </p>
          <Button 
            className="bg-[#004235] hover:bg-[#028475] text-white px-8 py-3 text-lg w-full sm:w-auto"
            size="lg"
            asChild
          >
            {/* This link should ideally go to a contact form or a dedicated suggestion page */}
            <Link href="/contact?subject=Glossary+Term+Suggestion&source=trade-glossary-suggest">
              SUGGEST A TERM
              <Send className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}