"use client"

import { Minus, Plus } from "lucide-react";
import React from "react";

interface FilterSectionProps {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
}

export function FilterSection({ title, children, defaultOpen = false }: FilterSectionProps) {
  // For now, keep the isOpen logic as it was, will address interactivity later if needed.
  const isOpen = defaultOpen;
  const Icon = isOpen ? Minus : Plus;

  return (
    <div className="border-b py-4">
      <button className="flex w-full items-center justify-between text-left">
        <h3 className="font-semibold text-gray-700">{title}</h3>
        <Icon className="h-5 w-5 text-gray-500" />
      </button>
      {isOpen && <div className="mt-3 space-y-2">{children}</div>}
    </div>
  );
}