"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"

export function ContinuousEnhancementSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Continuously Enhancing Trust and Quality
          </h2>
          <p className="text-lg text-gray-700 mb-10">
            StreamLnk's verification process and iScore™ system are continuously refined. We actively monitor partner compliance, gather user feedback, and adapt our standards to ensure our network remains the most reliable source for industrial materials globally. We believe that a foundation of trust is essential for frictionless global trade.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              className="bg-[#004235] hover:bg-[#028475] text-white"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                Request a Demo to Explore Our Network
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            
            <Button 
              variant="outline" 
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white"
              size="lg"
              asChild
            >
              <Link href="/solutions/risk-compliance/iscore">
                Learn More About iScore™
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}