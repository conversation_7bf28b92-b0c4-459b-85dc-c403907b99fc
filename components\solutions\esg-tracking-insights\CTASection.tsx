import Link from "next/link";
import { ArrowR<PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function CTASection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Let's Build More Responsible Industrial Supply Chains, Together.
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            Partner with StreamLnk for a Sustainable Future
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white px-6"
              size="lg"
              asChild
            >
              <Link href="/request-demo">
                REQUEST A DEMO
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}