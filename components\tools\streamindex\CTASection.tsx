"use client";

import Link from "next/link";
import { ArrowR<PERSON>, MessageSquare, Search } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function CTASection() {
  return (
    <section className="py-16 bg-[#f3f4f6]">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-[#004235] mb-6">
            Don't Navigate Blindly. Equip Your Business with StreamIndex™.
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            Unlock Premium Market Intelligence Today
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              className="bg-[#004235] hover:bg-[#028475] text-white w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/request-demo?tool=streamindex&source=cta">
                REQUEST LIVE DEMO
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>

            <Button
              variant="outline"
              className="border-[#004235] text-[#004235] hover:bg-[#004235] hover:text-white w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/solutions/streamresources#subscription-plans"> {/* Assuming this link structure */}
                EXPLORE SUBSCRIPTIONS
                <Search className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
          
          <div className="mt-6">
            <Button
              variant="link"
              className="text-[#028475] hover:text-[#004235] w-full sm:w-auto"
              size="lg"
              asChild
            >
              <Link href="/contact-us?interest=data-solutions">
                CONTACT DATA TEAM
                <MessageSquare className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}