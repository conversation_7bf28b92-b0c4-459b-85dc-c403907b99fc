{"compilerOptions": {"baseUrl": ".", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "target": "ES6", "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/components/*": ["components/*"], "@/app/*": ["app/*"], "@/lib/*": ["lib/*"], "@/utils/*": ["utils/*"], "@/hooks/*": ["hooks/*"], "@/data/*": ["data/*"], "@/types/*": ["types/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}