import type { ReactNode } from "react"

interface IntegrationFeatureProps {
  icon: ReactNode
  title: string
  description: string
}

export default function IntegrationFeature({ icon, title, description }: IntegrationFeatureProps) {
  return (
    <div className="flex items-start gap-4 p-4 rounded-lg bg-white shadow-sm">
      <div className="p-2 rounded-full bg-[#004235]/10 flex-shrink-0">{icon}</div>
      <div>
        <h4 className="font-medium text-[#004235] mb-1">{title}</h4>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
    </div>
  )
}
