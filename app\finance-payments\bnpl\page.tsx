import { MainNav } from "@/components/main-nav";
import { MainFooter } from "@/components/main-footer";
import HeroSection from "@/components/finance-payments/bnpl/hero-section";
import HowBnplWorksSection from "@/components/finance-payments/bnpl/how-bnpl-works-section";
import BenefitsSection from "@/components/finance-payments/bnpl/benefits-section";
import EligibilityRiskControlsSection from "@/components/finance-payments/bnpl/eligibility-risk-controls-section";
import IntegrationWithTreasuryToolsSection from "@/components/finance-payments/bnpl/integration-with-treasury-tools-section";
import HowToUseBnplSection from "@/components/finance-payments/bnpl/how-to-use-bnpl-section";
import CtaSection from "@/components/finance-payments/bnpl/cta-section";

export default function BnplPage() {
  return (
    <main className="flex flex-col min-h-screen">
      <MainNav />
      <HeroSection />
      <HowBnplWorksSection />
      <BenefitsSection />
      <EligibilityRiskControlsSection />
      <IntegrationWithTreasuryToolsSection />
      <HowToUseBnplSection />
      <CtaSection />
      <MainFooter />
    </main>
  );
}
