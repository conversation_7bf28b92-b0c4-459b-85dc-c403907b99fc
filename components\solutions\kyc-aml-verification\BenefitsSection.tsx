import { CheckCircle } from 'lucide-react'

export default function BenefitsSection() {
  return (
    <section className="py-16 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-[#004235] mb-8 text-center">
            Trade Securely, Globally, and with Greater Peace of Mind
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Reduced Risk of Fraud</h3>
                  <p className="text-gray-600">Significantly minimizes the chance of engaging with illicit or non-existent entities.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Enhanced Trust & Credibility</h3>
                  <p className="text-gray-600">Fosters a marketplace where all participants have been vetted, encouraging safer transactions.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Regulatory Compliance</h3>
                  <p className="text-gray-600">Helps your business meet its own due diligence obligations and avoid penalties associated with non-compliant counterparties.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Protection of Platform Integrity</h3>
                  <p className="text-gray-600">Safeguards the entire StreamLnk ecosystem from being misused for illicit purposes.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-[#004235] text-lg">Facilitates Smoother Transactions</h3>
                  <p className="text-gray-600">Pre-verified counterparties lead to fewer delays and complications in payments and fulfillment.</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-start mb-4">
                <CheckCircle className="text-[#028475] h-6 w-6 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-xl font-semibold text-[#004235] mb-2">Secure Access to Financial Services</h3>
                  <p className="text-gray-600">Verified identities are crucial for accessing integrated BNPL and Escrow services.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}