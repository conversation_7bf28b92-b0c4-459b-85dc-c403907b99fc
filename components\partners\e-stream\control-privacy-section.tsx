import { BarChart3, CheckCircle, Globe, Server, Shield } from "lucide-react"

const controls = [
  "Which countries or regions see your products.",
  "Who can request quotes (end-users only, or include resellers).",
  "Which customers are eligible for auctions or promotions.",
  "How buyer identities are shown (masked or full disclosure).",
]

const resources = [
  {
    title: "StreamIndex™",
    description: "Price benchmarking and product velocity analytics.",
    icon: <BarChart3 className="h-8 w-8 text-[#028475]" />,
  },
  {
    title: "StreamResources+",
    description: "Access performance scores, demand data, and market trends.",
    icon: <Server className="h-8 w-8 text-[#028475]" />,
  },
  {
    title: "Compliance Center",
    description: "Manage certificates, product testing, documentation expiration, and upload history.",
    icon: <Shield className="h-8 w-8 text-[#028475]" />,
  },
  {
    title: "AI Forecasting",
    description: "Get notified about seasonal trends, regional demand spikes, and market slowdowns.",
    icon: <Globe className="h-8 w-8 text-[#028475]" />,
  },
]

export function ControlPrivacySection() {
  return (
    <section id="supplier-resources" className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          <div>
            <h2 className="text-3xl font-bold text-[#004235] mb-6">Control and Privacy, On Your Terms</h2>
            <p className="text-gray-700 mb-6">StreamLnk is a secure, B2B-only platform. As a supplier, you control:</p>

            <div className="space-y-4">
              {controls.map((control, index) => (
                <div key={index} className="flex items-start gap-3">
                  <CheckCircle className="h-6 w-6 text-[#028475] flex-shrink-0 mt-0.5" />
                  <p className="text-gray-700">{control}</p>
                </div>
              ))}
            </div>

            <p className="text-gray-700 mt-6">
              All communications are managed in-platform. Buyers will never have direct access to your contact or
              pricing history unless approved.
            </p>
          </div>

          <div>
            <h2 className="text-3xl font-bold text-[#004235] mb-6">Supplier Resources</h2>

            <div className="space-y-6">
              {resources.map((resource, index) => (
                <div key={index} className="bg-[#F2F2F2] rounded-lg p-5 flex items-start gap-4">
                  <div className="bg-white rounded-full p-2 shadow-md">{resource.icon}</div>
                  <div>
                    <h3 className="text-lg font-semibold text-[#004235] mb-2">{resource.title}</h3>
                    <p className="text-gray-700">{resource.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
