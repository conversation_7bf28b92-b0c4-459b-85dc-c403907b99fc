"use client";

import { ShoppingCart, DollarSign, Truck, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';

const benefits = [
  {
    icon: <ShoppingCart className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Optimize Sourcing & Procurement",
    description: "Validate supplier quotes, identify cost-saving opportunities, negotiate with data-backed confidence."
  },
  {
    icon: <DollarSign className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Enhance Sales & Pricing Strategies",
    description: "Price products competitively based on real-time market conditions, identify high-demand regions."
  },
  {
    icon: <Truck className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Improve Logistics & Supply Chain Planning",
    description: "Select more efficient routes, anticipate delays, benchmark carrier performance."
  },
  {
    icon: <ShieldCheck className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Mitigate Market & Operational Risks",
    description: "Proactively identify and respond to emerging risks indicated by the indices."
  },
  {
    icon: <Brain className="h-10 w-10 text-[#028475] mb-4" />,
    title: "Inform Investment & Strategic Decisions",
    description: "Gain a deeper understanding of market dynamics for better capital allocation and business planning."
  }
];

export default function BenefitsSection() {
  return (
    <section className="py-16 md:py-24 bg-[#F2F2F2]">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#004235] mb-4">
            Turn Market Data into Your Competitive Edge
          </h2>
          <p className="text-xl text-gray-700">
            Leveraging StreamIndex™ for Your Strategic Advantage
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-md flex flex-col items-center text-center hover:shadow-lg transition-shadow duration-300">
              {benefit.icon}
              <h3 className="font-semibold text-[#004235] text-lg mb-2">{benefit.title}</h3>
              <p className="text-gray-600 text-sm">{benefit.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}